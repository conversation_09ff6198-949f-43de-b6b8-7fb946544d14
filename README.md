# 攸家 App V2.0

> 基于 React Native + Expo 的智能门禁管理系统移动端应用

[![React Native](https://img.shields.io/badge/React%20Native-0.79.2-blue.svg)](https://reactnative.dev/)
[![Expo](https://img.shields.io/badge/Expo-SDK%2053-black.svg)](https://expo.dev/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.8.3-blue.svg)](https://www.typescriptlang.org/)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)

## 📱 项目简介

攸家App是一个面向小区管理人员的移动端门禁管理系统，提供设备管理、用户管理、社区内容审核等核心功能。基于现代化的React Native + Expo技术栈，采用TypeScript开发，确保代码质量和开发效率。

## ✨ 功能特性

### 🔐 认证管理

- 手机号验证码登录
- Token自动管理和刷新
- 权限控制和路由守卫

### 📱 设备管理

- **设备注册**: 门禁、网关、呼叫器设备注册
- **设备调试**: 远程开门、过渡模式、临时凭证管理
- **设备解绑**: 设备解绑和原因记录
- **设备控制**: 批量设备操作和状态监控

### 👥 用户管理

- **小区住户**: 住户信息查看、搜索筛选、数据下发
- **小区员工**: 员工信息管理、权限范围查看
- **门卡管理**: 门卡录入、删除、状态管理

### 🏘️ 社区管理

- **邻里互助**: 内容查看、审核、删除管理
- **举报处理**: 举报查看、回复、警告、禁封处理

### ⚙️ 个人中心

- 个人信息管理和编辑
- 应用设置和关于我们
- 自动更新检查和执行

## 🛠️ 技术栈

### 核心技术

- **框架**: React Native 0.79.2 + Expo SDK 53
- **语言**: TypeScript 5.8.3
- **路由**: Expo Router (基于文件系统)
- **状态管理**: Zustand + React Query (TanStack Query)
- **UI组件**: NativeBase
- **网络请求**: Axios + React Query

### 开发工具

- **代码规范**: ESLint + Prettier
- **测试框架**: Jest + React Native Testing Library
- **构建部署**: EAS Build + EAS Submit
- **版本控制**: Git + Husky

## 🚀 快速开始

### 环境要求

- Node.js 18.0.0+
- npm 9.0.0+
- Expo CLI
- iOS: Xcode 14+ (macOS)
- Android: Android Studio

### 安装和运行

```bash
# 1. 克隆项目
git clone <repository-url>
cd YouJia

# 2. 初始化项目结构
npm run setup

# 3. 安装依赖
npm install

# 4. 配置环境变量
cp .env.example .env
# 编辑 .env 文件填入配置

# 5. 启动开发服务器
npm start

# 6. 在特定平台运行
npm run ios     # iOS 模拟器
npm run android # Android 模拟器
```

### 开发进度跟踪

```bash
# 查看开发进度报告
npm run progress:report

# 查看下一步建议
npm run progress:next

# 标记任务完成
npm run progress complete <任务ID>
```

## 📁 项目结构

```
YouJia/
├── app/                          # Expo Router 路由目录
│   ├── (auth)/                   # 认证相关路由
│   ├── (tabs)/                   # 主要功能Tab路由
│   │   ├── device-register/      # 设备注册
│   │   ├── device-debug/         # 设备调试
│   │   ├── device-control/       # 设备控制
│   │   ├── residents/            # 小区住户
│   │   ├── staff/                # 小区员工
│   │   ├── community/            # 社区管理
│   │   └── profile/              # 个人中心
│   └── _layout.tsx               # 根布局
├── src/                          # 源代码目录
│   ├── components/               # 组件库
│   │   ├── ui/                   # 基础UI组件
│   │   ├── business/             # 业务组件
│   │   └── layout/               # 布局组件
│   ├── services/                 # 服务层
│   │   ├── api/                  # API接口
│   │   ├── http/                 # HTTP客户端
│   │   └── storage/              # 本地存储
│   ├── stores/                   # 状态管理 (Zustand)
│   ├── hooks/                    # 自定义Hooks
│   ├── utils/                    # 工具函数
│   ├── types/                    # TypeScript类型定义
│   └── config/                   # 配置文件
├── assets/                       # 静态资源
├── docs/                         # 项目文档
├── __tests__/                    # 测试文件
└── scripts/                      # 脚本文件
```

## 📚 项目文档

- **[快速开始指南](./docs/快速开始指南.md)** - 项目快速上手指南
- **[技术架构设计文档](./docs/技术架构设计文档.md)** - 详细的技术架构说明
- **[开发规范](./docs/开发规范.md)** - 代码规范和最佳实践
- **[开发实施计划](./docs/开发实施计划.md)** - 详细的开发计划和时间安排
- **[开发任务清单](./docs/开发任务清单.md)** - 具体的开发任务和验收标准

## 🧪 测试和质量保证

```bash
# 运行测试
npm test
npm run test:watch
npm run test:coverage

# 代码质量检查
npm run lint
npm run lint:fix
npm run type-check
```

## 🏗️ 构建和部署

```bash
# 开发构建
npm run build:android -- --profile development
npm run build:ios -- --profile development

# 生产构建
npm run build:android -- --profile production
npm run build:ios -- --profile production

# 发布到应用商店
npm run submit:android
npm run submit:ios
```

## 📈 开发计划

项目采用敏捷开发方式，分为6个主要阶段：

1. **基础架构搭建** (第1-2周) - 环境配置、基础组件、路由配置
2. **认证模块开发** (第3周) - 登录功能、状态管理
3. **设备管理功能** (第4-6周) - 设备注册、调试、控制
4. **用户管理功能** (第7-8周) - 住户、员工管理
5. **社区管理功能** (第9-10周) - 内容审核、举报处理
6. **个人中心和优化** (第11周) - 个人信息、应用设置、整体优化

详细计划请参考 [开发实施计划](./docs/开发实施计划.md)

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

请确保遵循项目的 [开发规范](./docs/开发规范.md)

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系我们

- 项目维护者: [项目团队]
- 邮箱: [联系邮箱]
- 项目地址: [GitHub仓库地址]

---

⭐ 如果这个项目对你有帮助，请给我们一个星标！

## Join the community

Join our community of developers creating universal apps.

- [Expo on GitHub](https://github.com/expo/expo): View our open source platform and contribute.
- [Discord community](https://chat.expo.dev): Chat with Expo users and ask questions.
