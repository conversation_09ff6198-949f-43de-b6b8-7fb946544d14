# 攸家 App V2.0 开发任务清单

## 使用说明

本文档提供了详细的开发任务清单，每个任务都有明确的验收标准和交付物。开发人员可以按照清单逐项完成，确保项目按计划推进。

### 进度跟踪工具

项目提供了自动化的进度跟踪工具：

```bash
# 查看整体进度报告
npm run progress:report

# 查看下一步建议
npm run progress:next

# 标记任务完成
npm run progress complete <任务ID>
```

## 阶段一：基础架构搭建 (第1-2周)

### 任务 1.1：项目环境配置 ⏳

**工时**: 8小时  
**负责人**: 技术负责人

#### 验收标准

- [ ] 开发环境正常运行 (Node.js, Expo CLI, 模拟器)
- [ ] 项目依赖安装成功
- [ ] 环境变量配置完成
- [ ] VS Code 工作区配置完成
- [ ] Git hooks 配置完成

#### 具体步骤

1. **环境检查**

   ```bash
   node --version  # >= 18.0.0
   npm --version   # >= 9.0.0
   npx expo --version
   ```

2. **安装依赖**

   ```bash
   npm install
   ```

3. **配置环境变量**

   ```bash
   cp .env.example .env
   # 编辑 .env 文件，填入正确的配置
   ```

4. **配置开发工具**

   - 安装推荐的 VS Code 插件
   - 配置工作区设置

5. **设置 Git hooks**
   ```bash
   npx husky install
   ```

#### 交付物

- [ ] `.env` 文件配置完成
- [ ] `.vscode/settings.json` 配置完成
- [ ] 开发环境验证通过

---

### 任务 1.2：基础配置优化 ⏳

**工时**: 8小时  
**负责人**: 前端开发工程师

#### 验收标准

- [ ] Metro 配置优化完成
- [ ] Babel 配置完成
- [ ] 应用配置更新完成
- [ ] EAS 构建配置完成

#### 具体步骤

1. **优化 Metro 配置**

   ```javascript
   // metro.config.js
   const { getDefaultConfig } = require('expo/metro-config');

   const config = getDefaultConfig(__dirname);

   config.resolver.alias = {
     '@': './src',
   };

   module.exports = config;
   ```

2. **配置 Babel**

   ```javascript
   // babel.config.js
   module.exports = function (api) {
     api.cache(true);
     return {
       presets: ['babel-preset-expo'],
       plugins: [
         [
           'module-resolver',
           {
             alias: {
               '@': './src',
             },
           },
         ],
       ],
     };
   };
   ```

3. **更新应用配置**

   - 更新 `app.json` 中的应用信息
   - 配置图标和启动画面
   - 设置权限配置

4. **配置 EAS 构建**
   - 配置不同环境的构建配置
   - 设置签名和证书

#### 交付物

- [ ] `metro.config.js` 配置完成
- [ ] `babel.config.js` 配置完成
- [ ] `app.json` 更新完成
- [ ] `eas.json` 配置完成

---

### 任务 1.3：HTTP客户端和拦截器 ⏳

**工时**: 16小时  
**负责人**: 前端开发工程师

#### 验收标准

- [ ] Axios HTTP 客户端配置完成
- [ ] 请求/响应拦截器实现完成
- [ ] Token 自动管理功能完成
- [ ] 错误处理机制完成
- [ ] 单元测试覆盖率 ≥ 80%

#### 具体步骤

1. **创建 HTTP 客户端**

   ```typescript
   // src/services/http/client.ts
   import axios from 'axios';
   import { ENV } from '@/config/env';

   export const httpClient = axios.create({
     baseURL: ENV.API_BASE_URL,
     timeout: 10000,
     headers: {
       'Content-Type': 'application/json',
     },
   });
   ```

2. **实现请求拦截器**

   - 自动添加 Authorization header
   - 添加请求 ID 用于追踪
   - 处理请求参数

3. **实现响应拦截器**

   - 统一处理响应格式
   - 自动 Token 刷新
   - 错误状态码处理

4. **错误处理机制**

   - 网络错误处理
   - 业务错误处理
   - 超时处理

5. **编写测试用例**
   - 测试请求拦截器
   - 测试响应拦截器
   - 测试错误处理

#### 交付物

- [ ] `src/services/http/client.ts`
- [ ] `src/services/http/interceptors.ts`
- [ ] `src/services/http/types.ts`
- [ ] `src/services/http/index.ts`
- [ ] `__tests__/services/http/client.test.ts`

---

### 任务 1.4：状态管理配置 ⏳

**工时**: 8小时  
**负责人**: 前端开发工程师

#### 验收标准

- [ ] Zustand store 配置完成
- [ ] React Query 配置完成
- [ ] 认证状态管理完成
- [ ] 应用全局状态管理完成

#### 具体步骤

1. **配置 Zustand**

   ```typescript
   // src/stores/auth.ts
   import { create } from 'zustand';
   import { persist } from 'zustand/middleware';

   interface AuthState {
     token: string | null;
     user: User | null;
     isAuthenticated: boolean;
     login: (token: string, user: User) => void;
     logout: () => void;
   }

   export const useAuthStore = create<AuthState>()(
     persist(
       (set) => ({
         token: null,
         user: null,
         isAuthenticated: false,
         login: (token, user) => set({ token, user, isAuthenticated: true }),
         logout: () => set({ token: null, user: null, isAuthenticated: false }),
       }),
       {
         name: 'auth-storage',
       }
     )
   );
   ```

2. **配置 React Query**

   - 设置查询客户端
   - 配置缓存策略
   - 设置重试机制

3. **创建认证 Hook**
   ```typescript
   // src/hooks/useAuth.ts
   export const useAuth = () => {
     const { token, user, isAuthenticated, login, logout } = useAuthStore();

     return {
       token,
       user,
       isAuthenticated,
       login,
       logout,
     };
   };
   ```

#### 交付物

- [ ] `src/stores/auth.ts`
- [ ] `src/stores/app.ts`
- [ ] `src/stores/index.ts`
- [ ] `src/hooks/useAuth.ts`

---

### 任务 2.1：基础UI组件库 ⏳

**工时**: 24小时  
**负责人**: 前端开发工程师

#### 验收标准

- [ ] Button 组件完成 (支持多种样式和状态)
- [ ] Input 组件完成 (支持验证和错误提示)
- [ ] Modal 组件完成 (支持不同尺寸和动画)
- [ ] Loading 组件完成 (支持全屏和局部加载)
- [ ] Toast 组件完成 (支持不同类型提示)
- [ ] 所有组件都有对应的测试用例
- [ ] 组件文档完成

#### 具体步骤

1. **Button 组件**

   ```typescript
   // src/components/ui/Button/Button.tsx
   interface ButtonProps {
     title: string;
     variant?: 'primary' | 'secondary' | 'danger';
     size?: 'small' | 'medium' | 'large';
     disabled?: boolean;
     loading?: boolean;
     onPress?: () => void;
   }
   ```

2. **Input 组件**

   - 支持不同输入类型
   - 内置验证功能
   - 错误状态显示
   - 清除按钮

3. **Modal 组件**

   - 支持不同尺寸
   - 自定义动画
   - 背景遮罩
   - 关闭回调

4. **Loading 组件**

   - 全屏加载
   - 局部加载
   - 自定义文本
   - 不同样式

5. **Toast 组件**
   - 成功/错误/警告/信息类型
   - 自动消失
   - 手动关闭
   - 位置配置

#### 交付物

- [ ] `src/components/ui/Button/` 完整组件
- [ ] `src/components/ui/Input/` 完整组件
- [ ] `src/components/ui/Modal/` 完整组件
- [ ] `src/components/ui/Loading/` 完整组件
- [ ] `src/components/ui/Toast/` 完整组件
- [ ] `src/components/ui/index.ts` 统一导出
- [ ] 对应的测试文件

---

### 任务 2.2：路由和导航配置 ⏳

**工时**: 16小时  
**负责人**: 前端开发工程师

#### 验收标准

- [ ] Expo Router 根布局配置完成
- [ ] 认证路由组配置完成
- [ ] Tab 路由组配置完成
- [ ] 底部导航栏实现完成
- [ ] 路由守卫和权限控制完成

#### 具体步骤

1. **根布局配置**

   ```typescript
   // app/_layout.tsx
   import { Stack } from 'expo-router';

   export default function RootLayout() {
     return (
       <Stack screenOptions={{ headerShown: false }}>
         <Stack.Screen name="(auth)" />
         <Stack.Screen name="(tabs)" />
       </Stack>
     );
   }
   ```

2. **认证路由组**

   - 登录页面路由
   - 认证布局
   - 重定向逻辑

3. **Tab 路由组**

   - 底部导航配置
   - 各功能模块路由
   - 嵌套路由支持

4. **权限控制**
   - 路由守卫
   - 权限检查
   - 重定向处理

#### 交付物

- [ ] `app/_layout.tsx`
- [ ] `app/(auth)/_layout.tsx`
- [ ] `app/(auth)/login.tsx`
- [ ] `app/(tabs)/_layout.tsx`
- [ ] `src/components/layout/TabBar.tsx`
- [ ] `src/utils/permissions.ts`

## 检查点：基础架构验收

### 验收标准

- [ ] 所有基础架构任务完成
- [ ] 开发环境正常运行
- [ ] 基础组件库功能完整
- [ ] 路由导航正常工作
- [ ] 代码规范检查通过
- [ ] 单元测试覆盖率 ≥ 70%

### 验收方法

```bash
# 运行项目
npm start

# 代码检查
npm run lint
npm run type-check

# 运行测试
npm run test:coverage

# 查看进度
npm run progress:report
```

---

## 下一阶段预览

完成基础架构搭建后，将进入**阶段二：认证模块开发**，主要任务包括：

1. **登录页面UI开发** - 实现手机号和验证码登录界面
2. **认证API接口** - 对接后端认证服务
3. **认证状态管理** - 完善登录状态管理
4. **登录流程集成测试** - 端到端测试登录功能

请确保当前阶段的所有任务都已完成并通过验收，再进入下一阶段的开发。
