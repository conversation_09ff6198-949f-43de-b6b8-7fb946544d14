# 产品需求文档 (PRD) - 攸家 App V2.0

## 1. 引言 (Introduction)

### 1.1 文档目的

本文档旨在详细定义“攸家” App V2.0.0 的产品需求，包括功能需求、非功能需求、用户场景和交互逻辑等。其主要目标是作为产品设计、开发、测试和验收的主要依据，确保各方对产品需求有统一、清晰的理解，并指导开发团队按时、高质量地完成产品开发。

### 1.2 产品概述

**门禁管理系统** 是一个利用物联网(IoT)、人工智能(AI)、大数据等技术打造的智慧化居住环境综合平台。
**攸家App** 作为该系统的重要组成部分，是专门为面向小区管理人员（如：设备安装人员、运维调试人员）的移动端管理工具。它旨在简化和集中化管理小区内的智能硬件设备（如：门禁、网关、呼叫器），管理小区人员（如：住户、员工）信息，处理小区事务（如：邻里互助信息查看、举报管理），从而提高社区管理的效率和智能化水平。

### 1.3 产品目标

- **业务目标:**

  - 提高设备现场部署、注册、调试的效率和标准化程度，支持多种设备类型。
  - 提供便捷的移动工具，支持运维人员进行设备状态监控、功能验证和批量/单个远程操作。
  - 提供标准化的设备解绑流程。
  - 方便现场人员快速查看住户信息及其门禁凭证状态，并能查看物业员工的基础信息、凭证状态和门禁权限范围，支持按需、按类型触发数据同步。
  - 确保设备与平台数据的及时同步与一致性。
  - 支持移动化的社区内容查看、审核、举报处理和用户管理。
  - 提供基础的个人资料管理功能。

- **用户目标 (安装/运维/管理人员):**
  - 随时随地通过手机完成设备的注册、调试和解绑操作。
  - 方便地查看设备的实时状态和基本信息。
  - 能够按位置筛选设备列表，并对选中的设备执行批量远程运维指令。
  - 能够按姓名/手机号搜索或按条件筛选查看小区住户列表及其门禁凭证状态。
  - 能够按姓名/手机号搜索或按条件筛选查看小区员工列表及其门禁凭证状态和门禁权限范围。
  - 能够对选中的住户或员工执行数据下发操作。
  - 使用临时凭证进行设备功能现场验证。
  - 能够查看邻里互助内容列表，进入详情，并对违规帖子或留言进行删除操作。
  - 能够查看用户举报列表，进入详情，并执行回复举报人、警告/禁封被举报人、删除相关内容等处理操作。
  - 能够查看和修改自己的头像、姓名、昵称、登录手机号，并能安全退出登录。

### 1.4 目标用户

- **核心用户群体: 通过【攸家运管】添加的员工**
  - **角色细分 (示例):**
    - **安装工/调试员:** 主要使用设备注册、设备调试、设备解绑功能。
    - **运维工程师:** 主要使用设备列表查看、设备详情查看、设备控制（远程操作）。
    - **数据管理人员/管理员:** 可能使用查看小区住户/员工列表、触发数据下发功能。
    - **内容审核员/社区管理员:** 可能使用邻里互助内容查看与管理、举报管理功能。
  - **特征:** 需要携带移动设备在工作现场（小区、楼栋、设备安装点）或远程进行操作，对App的稳定性、易用性、响应速度和安全性有较高要求。
  - **需求/目标:** 高效完成分配的设备管理或运营管理任务，确保操作准确无误，及时响应现场需求或平台指令。

### 1.5 范围

- **本次版本包含:**
  - 普通账号的手机号+验证码登录 (UC-攸家App-001)。
  - App 自动更新检查与执行 (UC-攸家App-002)。
  - **设备注册:** 门禁(UC-攸家App-003a)、网关(UC-攸家App-003b)、呼叫器(UC-攸家App-003c)的注册流程。
  - **设备调试 (主要针对门禁):** 扫码或位置选设备；查状态；管理临时凭证；远程开门；开关过渡模式；下发数据 (UC-攸家App-004)。
  - **设备解绑:** 扫码或位置选设备；输入原因(可选)；确认解绑 (UC-攸家App-005)。
  - **设备控制:** 按基础设施筛选设备列表；查看列表（含状态、计数、版本）；执行批量远程操作（开关过渡模式、重启、上报数据、下发数据）(UC-攸家App-006)
  - **小区住户查看:** 查看列表；搜索/筛选；数据下发(含类型/设备选择) (UC-攸家App-007)。
  - **小区员工查看:** 查看列表；搜索/筛选；[可能]数据下发；[可能]删除门卡 (UC-攸家App-008)。
  - **邻里互助查看与审核:** 查看列表/详情；[需权限]软删除帖子/评论 (UC-攸家App-009)。
  - **举报管理查看与处理:** 查看列表/详情；[需权限]处理举报(回复/警告/禁封/删内容) (UC-攸家App-010)。
  - **设置:** 查看关于我们、检查更新、注销账户；退出登录 (UC-攸家App-011)。
  - **个人信息管理:** 查看与修改头像/姓名/昵称/手机号； (UC-攸家App-012)。

### 1.6 术语定义

- **攸家 App:** 本文档所描述的移动应用程序。
- **门禁 (门禁设备):** 用于控制人员出入的电子设备，如小区大门、单元门。
- **网关 (网关设备):** 安装到小区住户室内，用于接收门禁发起的呼叫可远程开门的设备。
- **呼叫器 (呼叫器设备):** 安装到小区物业前台，用于接收门禁发起的呼叫可远程开门的设备。
- **设备注册:** 将新的硬件设备添加到系统中，并进行初步配置的过程。
- **设备调试:** 对已注册设备进行功能测试和参数设置的过程。
- **设备控制:** 通过 App 远程操作设备（如：远程开门、固件升级、上报数据、下发数据等）。
- **设备解绑:** 将设备从当前账号或系统中移除。
- **邻里互助** 小区住户可发布的寻人寻物、失物招领、闲置物品等信息。
- **举报管理 :** 处理来自用户的举报信息的模块。

## 2. 用户故事 / Use Cases - 攸家APP

**UC-攸家App-001: 使用手机号和验证码登录**

- **用户故事:** 作为一个已注册的【攸家】App用户，我想要使用手机号和验证码登录应用，并在启动时检查和执行更新，以便开始我的工作。
- **参与者:** 攸家 App 用户
- **前置条件:** 手机号已在【攸家运管】注册并分配权限；App已安装。
- **基本流程:**
  1.  用户打开 App，进入登录页面。
  2.  页面显示手机号输入框、获取验证码按钮、验证码输入框和登录按钮。
  3.  用户输入已在【攸家运管】登记的手机号码。
  4.  用户点击“获取验证码”按钮。
  5.  系统校验手机号格式（长度为11），若合法且已登记，则向该手机号发送短信验证码，按钮变为不可点击状态并开始倒计时 (60秒)。
  6.  用户收到短信，输入正确的 4 位验证码。
  7.  用户点击“登录”按钮。
  8.  系统校验验证码是否正确且未过期。
  9.  校验通过，用户成功登录，跳转至 App 主界面并选中设备注册Tab。
- **异常流程:**
  - 手机号格式错误：点击“获取验证码”时，提示“请输入正确的手机号”。
  - 手机号未注册：点击“获取验证码”时，提示“该手机号未注册”。
  - 验证码发送失败：提示“验证码发送失败，请稍后重试”。
  - 验证码输入错误：点击“登录”时，提示“验证码错误”。
  - 验证码过期：点击“登录”时，提示“验证码已过期，请重新获取”。
  - 网络错误：操作过程中任意网络请求失败，提示“网络连接失败，请检查网络设置”。
  - 倒计时结束前重复点击获取：按钮不可点击。倒计时结束后可再次点击。

**UC-攸家App-002: 检查与执行 App 升级**

- **用户故事:** 作为一个攸家App用户，我希望攸家App 启动时能自动检查更新，并在有新版本时提示我升级，以便我能及时使用最新的功能和修复。
- **参与者:** 攸家App用户
- **前置条件:** 用户打开 App。
- **基本流程:**
  1.  App 启动后，自动向服务器发送请求，检查是否有新版本。
  2.  服务器返回版本信息。
  3.  如果当前版本为最新，则无任何提示，流程结束。
  4.  如果检测到新版本，弹窗提示用户。弹窗内容包括：新版本号 (如 V1.2.0)、更新内容描述、可选升级类型（强制/建议）、“稍后升级”和“立即更新”按钮。
  5.  用户点击“立即更新”。
  6.  App 开始下载更新包，弹窗内显示下载进度条。
  7.  下载完成后，根据操作系统引导用户进行安装。
  8.  安装完成后，App 自动重启或提示用户手动重启。
- **可选流程:**
  - 用户选择“稍后升级”，弹窗关闭，本次启动不再提示。
  - 强制升级：弹窗仅提供“立即更新”按钮，用户不升级则无法继续使用 App。
- **异常流程:**
  - 检查更新失败 (网络错误)：无提示或短暂提示后忽略，不影响 App 正常使用。
  - 下载更新包失败：提示“下载失败，请检查网络或稍后重试”。
  - 设备存储空间不足：提示“设备存储空间不足，无法下载更新”。

**UC-攸家App-003a: 设备注册-注册门禁设备**

- **用户故事:** 作为一个安装人员，我需要使用【攸家】App注册一台新的门禁设备。我需要扫描或输入设备信息，为其命名，选择安装位置等完成注册和激活。
- **参与者:** 攸家 App 安装人员
- **前置条件:** 用户已登录并拥有设备注册权限；新门禁设备已通电并处于待配置状态；安装位置的基础设施信息已在【攸家运管】配置好。
- **基本流程:**
  1.  用户在 App 主界面点击“设备注册”入口，并选择“门禁设备”类型。
  2.  App 进入“门禁注册信息录入”界面。界面顶部显示当前登录用户的信息，下方包含一个二维码扫描区域和设备信息录入区，底部为注册操作按钮栏。
  3.  用户通过扫码，获取“设备编号”，并自动填充到设备编号栏。**[异常/替代流程 R1]**
  4.  用户点击“功能设施类型”栏，弹出“选择功能设施类型”对话框。
  5.  用户选择设备关联的功能设施类型，相关数据有攸家运管平台配置(如：地上单元门、地下单元门、小区大门、车棚门)。**[异常/替代流程 R2]**
  6.  用户点击“确定”隐藏“选择功能设施类型”对话框，功能设施类型已填入。
  7.  用户点击“基数设施”栏，弹出“选择基础设施”对话框。
  8.  用户通过层级列表（小区->楼栋->单元）选择门禁安装的精确位置。**[异常/替代流程 R3]**
  9.  用户点击“确认”隐藏“选择基础设施”对话框，基数设施已填入。
  10. 用户点击“备注”栏，弹出“输入备注”对话框（备注选填）。
  11. 用户根据实际情况输入当前设备需要备注的信息。
  12. 用户点击“确认”隐藏“输出备注”对话框，备注已填入。
  13. 用户选择当前设备使用权限是否开放给小区住户。
  14. 如果开放给用户，显示住户使用范围栏。
  15. 用户点击“住户使用范围栏”，弹出“选择范围”对话框。
  16. 用户通过层级列表（小区->楼栋->单元->楼层->户室）选择当前门禁设备开放给住户的范围。**[异常/替代流程 R4]**
  17. 用户点击“确定”隐藏“选择范围”对话框，范围信息已填入。
  18. 如果不开放给用户，则隐藏住户使用范围栏
  19. 用户点击“确认注册”，弹出“确认注册”对话框。
  20. 用户点击“确认”，门禁注册成功**[异常/替代流程 R5]**。
- **异常/替代流程:**
  - **R1: 扫码失败:** 无法识别二维码。
  - **R2: 功能设施类型选择失败:** 加载功能设施类型列表失败。
  - **R3: 位置选择失败:** 加载基础设施列表失败。
  - **R4: 住户可使用范围选择失败:** 加载住户可使用范围列表失败。
  - **R5: 设备注册失败**
    - **R5.1: 移动端校验失败:**
      - 设备编号为空： 提示“请扫码设备二维码获取设备编号”。
      - 未选择功能设施类型： 提示“请选择功能设施类型”。
      - 未选择基础设施： 提示“请选择基础设施”。
      - 未选择是否开放给住户： 提示“请选择是否开发给住户”。
      - 未选择住户可使用范围： 提示“请选择住户可使用范围”。
    - **R5.2: 保存操作失败:**
      - 网络连接失败或超时： 提示“网络错误，请稍后重试”。
      - 服务器内部错误（数据库写入失败等）： 提示“添加街道失败，请联系管理员”。

**UC-攸家App-003b: 设备注册-注册网关设备**

- **用户故事:** 作为一个安装人员，我需要使用【攸家】App注册一台新的网关设备，输入设备信息，选择安装位置，完成激活。
- **参与者:** 攸家 App 安装人员
- **前置条件:** 用户已登录并拥有设备注册权限；新网关设备已通电。
- **基本流程:**
  1. 用户在 App 主界面点击“设备注册”入口，并选择“网关设备”类型。
  2. App 进入“网关注册信息录入”界面。界面顶部显示当前登录用户的信息，下方包含一个二维码扫描区域和设备信息录入区，底部为注册操作按钮栏。
  3. 用户通过扫码，获取“设备编号”，并自动填充到设备编号栏。**[异常/替代流程 G1]**
  4. 用户点击“基数设施”栏，弹出“选择基础设施”对话框。
  5. 用户通过层级列表（小区->楼栋->单元->楼层->户室）选择网关安装的精确位置。**[异常/替代流程 G2]**
  6. 用户点击“确认”隐藏“选择基础设施”对话框，基数设施已填入。
  7. 用户点击“确认注册”，弹出“确认注册”对话框。
  8. 用户点击“确认”，网关注册成功**[异常/替代流程 G3]**。
- **异常/替代流程:**
  - **G1: 扫码失败:** 无法识别二维码。
  - **G2: 位置选择失败:** 加载基础设施列表失败。
  - **G3: 设备注册失败**
    - **G3.1: 移动端校验失败:**
      - 设备编号为空： 提示“请扫码设备二维码获取设备编号”。
      - 未选择基础设施： 提示“请选择基础设施”。
    - **R3.2: 保存操作失败:**
      - 网络连接失败或超时： 提示“网络错误，请稍后重试”。
      - 服务器内部错误（数据库写入失败等）： 提示“添加街道失败，请联系管理员”。

**UC-攸家App-003c: 设备注册-注册呼叫器设备**

- **用户故事:** 作为一个安装人员，我需要使用【攸家】App注册一台新的呼叫器设备，输入设备信息，选择安装位置，完成激活。
- **参与者:** 攸家 App 安装人员
- **前置条件:** 用户已登录并拥有设备注册权限；新呼叫器设备已通电。
- **基本流程:**
  1. 用户在 App 主界面点击“设备注册”入口，并选择“呼叫器设备”类型。
  2. App 进入“呼叫器注册信息录入”界面。界面顶部显示当前登录用户的信息，下方包含一个二维码扫描区域和设备信息录入区，底部为注册操作按钮栏。
  3. 用户通过扫码，获取“设备编号”，并自动填充到设备编号栏。**[异常/替代流程 C1]**
  4. 用户点击“基数设施”栏，弹出“选择基础设施”对话框。
  5. 用户通过小区列表（小区）选择呼叫器安装的精确位置。**[异常/替代流程 C2]**
  6. 用户点击“确认”隐藏“选择基础设施”对话框，基数设施已填入。
  7. 用户点击“备注”栏，弹出“输入备注”对话框（备注选填）。
  8. 用户根据实际情况输入当前设备需要备注的信息。
  9. 用户点击“确认”隐藏“输出备注”对话框，备注已填入。10.用户点击“确认注册”，弹出“确认注册”对话框。
  10. 用户点击“确认”，呼叫器注册成功**[异常/替代流程 G3]**。
- **异常/替代流程:**
  - **G1: 扫码失败:** 无法识别二维码。
  - **G2: 位置选择失败:** 加载基础设施列表失败。
  - **G3: 设备注册失败**
    - **G3.1: 移动端校验失败:**
      - 设备编号为空： 提示“请扫码设备二维码获取设备编号”。
      - 未选择基础设施： 提示“请选择基础设施”。
    - **R3.2: 保存操作失败:**
      - 网络连接失败或超时： 提示“网络错误，请稍后重试”。
      - 服务器内部错误（数据库写入失败等）： 提示“添加街道失败，请联系管理员”。

**UC-攸家App-004: 设备调试-调试门禁设备**

- **用户故事:** 作为一个安装或运维人员，我想要在【攸家】App 的设备调试功能中，通过扫描设备二维码快速选中要操作的设备或通过门禁安装的位置选中要操作的设备，然后查看它的基本信息、凭证同步状态和我的临时调试凭证情况，并能方便地执行远程开门、开关过渡模式、同步数据、以及添加或删除我自己的临时人脸/门卡/密码等调试操作。
- **参与者:** 攸家 App 安装/运维人员
- **前置条件:** 用户已登录并拥有设备调试权限。
- **基本流程:**
  1.  用户在 App 主界面点击“设备调试”Tab。
  2.  系统进入“设备调试”主界面。界面顶部可能显示当前登录用户的信息，下方包含一个二维码扫描区域和设备信息展示区，底部为调试操作按钮栏。
  3.  **【选择设备】**
      - 用户将手机摄像头对准目标设备上的二维码进行扫描。**[异常/替代流程 D1]**
      - 用户通过层级列表（小区->楼栋->单元）选择门禁安装的位置，根据位置筛选出目标设备。**[异常/替代流程 D2]**
  4.  **【显示设备信息】** 扫描成功并识别到有效设备后或通过位置定位到有效设备后，系统在界面中部区域显示该设备的信息：
      - 基础设施（设备位置）（如：未来城）
      - 设备编号（含复制按钮）
      - 门禁实录 人像/密码/NFC 数
      - 服务实录 人像/密码/NFC 数 （供用户比对数据同步情况）
      - 当前用户在此设备上的调试数据状态（显示已录入的临时密码、门卡号、人像缩略图）。**[异常/替代流程 D7]**
  5.  **【执行调试操作】** 用户根据需要，点击界面底部的操作按钮：
      - **打开过渡模式:**
        - 点击“打开过渡”按钮，App向设备发送打开过渡模式的指令，过渡模式打开成功后门禁设备的默认密码“888888”可打开门禁，所有的NFC（包括未录入的NFC）可打开门禁。APP提示“过渡模式已打开”。**[异常/替代流程 D4]**
      - **关闭开过渡模式:**
        - 点击“关闭过渡”按钮，App向设备发送关闭过渡模式的指令，过渡模式关闭成功后门禁设备的默认密码“888888”不可打开门禁，只有录入的NFC可打开门禁。APP提示“过渡模式已关闭”。**[异常/替代流程 D5]**
      - **远程开门:**
        - 点击“远程开门”按钮，App向设备发送开门指令。**[异常/替代流程 D6]**
      - **下发数据:**
        - 点击“下发数据”按钮，App触发一次服务器到该设备的数据同步（同步小区住户、物业工作人员的密码、NFC、人像数据）。**[异常/替代流程 D7]**
      - **设置/录入临时凭证:**
        - 点击“设置密码”：弹出密码输入框，用户输入6位数字密码后确认，App将该密码作为临时调试密码（24h有效）下发至设备。**[异常/替代流程 D8.1]**
        - 点击“录入门卡”：App提示用户将NFC卡靠近手机感应区，读取卡号后确认，App将该卡号作为临时调试门卡（24h有效）下发至设备。**[异常/替代流程 D8.2]**
        - 点击“录入人像”：App启动前置摄像头，用户按提示完成人脸采集和确认，App将该人像作为临时调试人像（24h有效）下发至设备。**[异常/替代流程 D8.3]**
        - 录入成功后，“调试数据”区域会更新显示对应的凭证信息和选中状态。
      - **删除某一临时凭证:**
        - 点击密码/门卡/人像调试数据旁的“删除”图标。
        - App发送指令，从设备中删除当前用户之前录入的对应类型的临时调试凭证。**[异常/替代流程 D9]**
        - “调试数据”区域更新显示。
      - **清空所有临时凭证:**
        - 点击“清空”按钮，APP发送指令，从设备中清空当前用户录入的所有临时调试凭证（密码、NFC、人像）。 “调试数据”区域更新显示。**[异常/替代流程 D9]**
  6.  **【结束调试】** 用户完成操作后，可扫描其他设备二维码，或导航至App其他功能区。
- **异常/替代流程:**
  - **D1: 通过二维码选择设备失败**
    - 扫描二维码失败（摄像头权限、模糊、非系统二维码）：提示“无法识别二维码，请重试或检查设备”。
    - 扫描成功但设备未在平台注册或ID无效：提示“未找到设备信息，请确认设备已注册”。
  - **D2: 通过位置选择设备失败**
    - 基础设施加载失败。
    - 基础设施没有绑定任何设备：提示“当前位置下没有绑定任何设备”
  - **D3: 加载设备信息失败**
    - 定位设备成功后，从服务器获取设备详细信息（位置、计数、调试数据状态等）时失败（网络/服务器错误）：信息区域显示加载失败。
  - **D4: 打开过渡模式失败**
    - 设备离线：提示“设备离线，操作失败”。
    - 网络错误或服务器指令发送失败：提示“操作失败，请检查网络或稍后重试”。
    - 设备端执行失败或未响应：提示“切换模式失败，请检查设备状态”。
  - **D5: 关闭过渡模式失败**
    - 设备离线：提示“设备离线，操作失败”。
    - 网络错误或服务器指令发送失败：提示“操作失败，请检查网络或稍后重试”。
    - 设备端执行失败或未响应：提示“切换模式失败，请检查设备状态”。
  - **D6: 远程开门失败**
    - 设备离线：提示“设备离线，无法开门”。
    - 网络错误或服务器指令发送失败。
    - 设备端执行开门失败（如门锁故障）。
  - **D7: 下发数据失败**
    - 设备离线：提示“设备离线，无法同步数据”。
    - 网络错误或服务器指令发送失败。
    - 设备端接收或处理数据同步失败。
  - **D8: 设置/录入临时凭证失败**
    - **D8.1 (密码):** 输入密码格式错误（非6位数字）；设备离线；网络/服务器错误；设备端保存失败。
    - **D8.2 (NFC):** 手机无NFC功能或未开启；NFC卡片读取失败/不兼容；设备离线；网络/服务器错误；设备端保存失败。
    - **D8.3 (人像):** 摄像头权限未授予；人脸采集质量不合格（模糊、多人、光线差）；人脸特征提取或上传失败；设备离线；网络/服务器错误；设备端保存失败。
  - **D9: 删除临时凭证失败**
    - 设备离线；网络/服务器错误；设备端删除失败。
  - **通用:**
    - **网络连接中断:** 在执行任何需要与后台或设备通信的操作时发生网络中断。

** UC-攸家App-005: 解绑设备**

- **用户故事:** 作为一个安装或运维人员，当一台设备（无论是门禁、网关还是呼叫器）需要从当前位置移除时，我想要在【攸家】App中通过扫描设备二维码或通过绑定的位置选中它，记录下解绑的原因（可选），然后确认执行解绑操作，使其与系统中的基础设施位置断开关联。
- **参与者:** 攸家 App 安装/运维人员
- **前置条件:** 用户已登录并拥有设备解绑权限。
- **基本流程:**
  1.  用户在 App 主界面点击“设备解绑”Tab。
  2.  系统进入“设备解绑”主界面，界面顶部显示当前登录用户的信息，下发左侧有设备类型选择按钮（门禁、网关、呼叫器），下方右侧包含一个二维码扫描区域和设备信息展示区 ，底部为解绑操作按钮栏。
  3.  **【选择设备】**
      - 用户将手机摄像头对准目标设备上的二维码进行扫描。**[异常/替代流程 U1]**
      - 用户通过层级列表（小区->楼栋->单元->楼层->单元）选择设备安装的位置，根据位置筛选出目标设备。**[异常/替代流程 U2]**
  4.  **【确认信息与输入原因】** 扫描成功并识别到有效设备后或通过位置定位到有效设备后，系统在界面中部区域显示该设备的基本信息：
      - **自动识别并显示设备类型** （对应按钮高亮显示，如：“门禁”）**[异常/替代流程 U3]**。
      - 显示基础设施位置和备注（如：未来城（北门））。
      - 显示设备编号（含复制按钮）。
      - 系统提供一个“备注”文本区域，提示用户“请根据实际情况输入解绑原因（选填）”。用户根据需要在此输入解绑原因。
  5.  **【执行解绑】**
      - 用户核对设备信息无误后，点击页面底部的橙色“确认解绑”按钮。
      - App 向后台服务器发送解绑请求，包含设备编号、设备类型和可选的解绑原因。
      - 后台服务器根据设备编号执行解绑逻辑：解除设备与基础设施的关联；记录解绑日志。**[异常/替代流程 U4]**
  6.  **【结果反馈】**
      - 后台处理成功后，App 界面提示“解绑成功”。用户可以继续扫描下一个设备进行解绑。
- **异常/替代流程:**
  - **U1: 通过二维码选择设备失败**
    - 扫描二维码失败（摄像头权限、模糊、非系统二维码）：提示“无法识别二维码，请重试或检查设备”。
    - 扫描成功但设备未在平台注册或设备编号无效：提示“未找到设备信息，请确认设备已注册”。
  - **U2: 通过位置选择设备失败**
    - 基础设施加载失败。
    - 基础设施没有绑定任何设备：提示“当前位置下没有绑定任何设备”
  - **U3: 加载设备信息失败**
    - 定位设备成功后，从服务器获取设备详细信息（位置、备注）时失败（网络/服务器错误）：信息区域显示加载失败。
  - **U4: 执行解绑失败**
    - 网络连接失败或超时，无法向后台发送请求： 提示“网络错误，请稍后重试”。
    - 服务器内部错误（数据库更新失败、解绑逻辑错误等）： 提示“解绑失败，请联系管理员”。

**UC-攸家App-006:设备控制-远程控制设备**

- **用户故事:** 作为一个【攸家】App的运维人员，我想要按设备所在的具体位置（基础设施）筛选设备列表，查看这些设备的状态和数据同步情况，并能对选中的一个或多个设备执行远程操作，如开关过渡模式、重启设备、要求设备上报数据或触发数据下发到设备。
- **参与者:** 攸家 App 安装/运维人员
- **前置条件:** 用户已登录并拥有设备控制权限。
- **基本流程:**
  1.  用户在 App 主界面点击“设备控制”Tab。
  2.  系统进入“设备控制”页面。
  3.  **【筛选设备列表】**
      - 用户点击顶部的“请选择基础设施”选择器。
      - 用户通过层级选择（小区->楼栋->单元），确定要查看的设备位置范围。**[异常/替代流程 C1]**
      - 系统根据选择刷新下方的设备列表。用户可点击“清空”清除筛选。
  4.  **【查看设备列表】**
      - 设备列表展示符合条件的设备信息，包含：位置（基础设施）、设备编号、网络状态（在线/离线）、软件版本、设备注册时间、门禁实录人像/密码/NFC数、服务实录人像/密码/NFC数，以及一个用于批量操作的复选框。
  5.  **【执行批量远程操作】**
      - 用户通过勾选列表右侧的复选框（或点击底部的“全选”按钮）选择一个或多个目标设备。
      - 用户点击列表下方操作栏中对应的按钮：
        - **打开过渡 / 关闭过渡:** 切换选中设备的过渡模式。**[异常/替代流程 C2]**
        - **远程重启:** 向选中设备发送重启指令。**[异常/替代流程 C3]**
        - **上报数据:** 请求选中设备上报当前凭证数量。**[异常/替代流程 C4]**
        - **下发数据:** 触发服务器向选中设备同步用户数据。**[异常/替代流程 C5]**
      - App 发送指令到后台，后台分发给设备。App 给出操作反馈（如“指令已发送”、“X台成功，Y台失败”）。
- **异常/替代流程:**
  - **C1: 筛选设备失败:**
    - 加载基础设施选项失败（网络/服务器错误）。
    - 选择位置后，加载对应的设备列表失败或超时。
    - 按所选条件未找到任何设备，列表显示为空状态提示。
  - **C2: 过渡模式切换失败:**
    - 未选择任何设备，提示“请先选择设备”。
    - 部分或全部选中设备离线，提示“X台设备离线，无法操作”。
    - 网络错误或服务器错误，指令发送失败。
    - 设备端执行失败或超时未响应。
    - （给出部分成功/失败的反馈）
  - **C3: 远程重启失败:** (类似C2的异常情况：未选择、离线、网络/服务器错误、设备执行失败、部分成功反馈)
  - **C4: 请求上报数据失败:** (类似C2的异常情况：未选择、离线、网络/服务器错误、设备不支持或执行失败、部分成功反馈)
  - **C5: 下发数据失败:** (类似C2的异常情况：未选择、离线、网络/服务器错误、设备执行失败、部分成功反馈)

** UC-攸家App-007:小区住户-查看和筛选小区住户列表并下发数据 **

- **用户故事:** 作为一个需要核对住户信息【攸家】App用户，我想要查看小区住户列表，通过姓名/手机号搜索或按小区/楼栋/单元/楼层/户室筛选。当我需要确保某个或某些住户的凭证在特定设备上生效时，我希望能选择这些住户，然后在一个弹窗中指定要下发的凭证类型（人像、密码、NFC或全部凭证），并勾选需要更新权限的目标门禁设备，最后确认执行下发。
- **参与者:** 攸家 App 用户
- **前置条件:** 用户已登录并拥有小区住户的权限。小区、住户、设备数据已存在。
- **基本流程:**
  1.  用户在 App 主界面点击“小区住户”Tab。
  2.  系统进入“小区住户”列表页面。
  3.  **【查看列表】**
      - App 默认加载并以卡片形式展示第一页小区住户信息。
      - 每个卡片包含：头像占位符、姓名、时间戳（登记时间）、户室、手机号（部分隐藏）、门禁密码状态（已设置/未设置）、人像状态（已录入/未录入）、门卡状态（已录入/未录入 - 若已录入具体录入的门卡以及旁边显示“删除”按钮），以及一个用于批量操作的复选框。**[异常/替代流程 V1]**
      - 用户可以通过上下滑动列表加载更多数据（使用分页）。
  4.  **【搜索与筛选】**
      - 用户在页面顶部的搜索框中输入小区住户姓名或手机号关键字。App在用户确认搜索后刷新列表。用户可点击“清空”清除。
      - 用户点击搜索框右侧的“筛选”图标/按钮。
      - App 弹出筛选条件选择界面，允许用户按小区、楼栋、单元、楼层、户室进行筛选。
      - 用户选择筛选条件后确认，App 返回列表页并刷新。**[异常/替代流程 V2]**
  5.  **【数据下发】**
      - **(a) 触发:** 用户通过点击卡片右上角的复选框选择一个或多个住户（底部有“全选”）。然后点击页面底部的“下发数据”按钮。
      - **(b) 选择数据类型与设备:**
        - 系统弹出“下发数据”模态框。
        - 模态框顶部显示“数据类型:”及复选框：密码、NFC、人像、全部。用户勾选需要下发的类型（可多选，“全部”选中时可能自动勾选其他三个）。默认可能选中“全部”。**[异常/替代流程 V3.1]**
        - 模态框中间以网格形式展示选中的住户（如：张三、李四），以及这些住户各自可访问的门禁设备按钮（如：艾依雅居东北门、1号楼1单元）。
        - 每个住户行首有“全选”复选框，用于全选/取消全选该住户的所有可用设备。
        - 用户点击具体的设备按钮来选择或取消选择该设备（为每个住户\*\*单独勾选其下的设备按钮)。
      - **(c) 确认下发:**
        - **方式一 (精确下发):** 用户完成数据类型和设备选择后，点击右下角的“确定”按钮。**[异常/替代流程 V3.2, V3.3]**
        - **方式二 (一键下发):** 用户直接点击“一键下发”按钮，系统默认下发**全部用户的数据类型**到**所有可选设备**。**[异常/替代流程V3.3]**
      - **(d) 执行与反馈:**
        - App 向后台发送下发指令，包含：选中的住户ID列表、选中的设备ID列表、选中的数据类型。
        - 后台处理指令并分发给设备。
        - App 关闭模态框，并给出操作反馈，如“下发指令已发送”。
      - **(e) 取消下发:** (替代流程) 用户在模态框中点击“取消”按钮，关闭模态框，不下发数据。
  6.  **【删除门卡】**
      - 用户点击某卡片上“门卡：已录入”旁边的“删除”按钮。
      - App 弹出确认框“确定要删除该小区住户的门卡吗？”
      - 用户确认。
      - App 发送删除该小区住户某张门卡的指令。**[异常/替代流程 V4]**
- **异常/替代流程:**
  - **V1: 加载列表失败:**
    - 进入页面或滚动加载/翻页时，加载住户列表数据失败（网络/服务器错误）：显示加载失败提示或无法加载更多。
  - **V2: 搜索/筛选失败:**
    - 搜索/筛选后无匹配结果：列表区域显示“未找到相关住户”或空状态提示。
    - 加载筛选条件（如小区/楼栋列表）失败：筛选功能无法使用或选项为空。
    - 应用筛选条件时查询失败（网络/服务器错误）：列表刷新失败，提示错误。
  - **V3: 数据下发失败**
    - **V3.1: 模态框加载/操作失败:**
      - 触发下发后，加载可选设备列表失败，无法显示模态框或设备列表为空/错误。
      - 在模态框中选择数据类型或设备时发生移动端端脚本错误。
    - **V3.2: 移动端校验失败:**
      - 点击“确定”时，未选择任何住户：提示“请选择要下发数据的用户”。
      - 点击“确定”时，未选择任何数据类型：提示“请选择要下发的数据类型”。
      - 点击“确定”时，已选用户未选择任何目标设备:“请选择【某某】用户要下发的设备”。
    - **V3.3: 后端处理失败:**
      - 后台发送下发指令时网络错误或服务器内部错误。
      - 部分设备执行下发操作失败（设备离线、存储满等），App应给出明确的部分成功/失败反馈（需要后续状态查询或日志）。
  - **V4: 删除门卡失败**
    - 用户取消确认。
    - 网络/服务器错误导致删除指令失败。

**UC-攸家App-008: 小区员工-查看和筛选小区员工列表**

- **用户故事:** 作为一个需要核对物业人员信息及权限状态的【攸家】App用户，我想要查看小区内的物业员工列表，能够通过姓名或手机号搜索，或通过筛选（按小区）定位员工，并直观地看到每个员工的基本信息、门禁凭证录入情况以及他们的门禁权限范围。我还希望能选择员工触发数据下发操作。
- **参与者:** 攸家 App 用户
- **前置条件:** 用户已登录并拥有小区员工权限。小区及员工数据已存在。
- **基本流程:**
  1.  用户在 App 主界面点击“小区员工”Tab。
  2.  系统进入“小区员工”列表页面。
  3.  **【查看列表】**
      - App 默认加载并以卡片形式展示第一页物业员工信息。
      - 每个卡片包含：头像占位符、姓名、时间戳(员工添加时间)、岗位、手机号（部分隐藏）、门禁密码状态（已设置/未设置）、人像状态（已录入/未录入）、门卡状态（已录入/未录入 - 若已录入显示“删除”按钮）、门禁权限范围（列出可通行位置），以及一个用于批量操作的复选框。**[异常/替代流程 S1]**
      - 用户可以通过上下滑动列表加载更多数据（使用分页）。
  4.  **【搜索与筛选】**
      - 用户在页面顶部的搜索框中输入员工姓名或手机号关键字。App在用户确认搜索后刷新列表。用户可点击“清空”清除。
      - 用户点击搜索框右侧的“筛选”图标/按钮。
      - App 弹出筛选条件选择界面，允许用户按小区、楼栋、单元进行筛选。
      - 用户选择筛选条件后确认，App 返回列表页并刷新。**[异常/替代流程 S2]**
  5.  **【数据下发】**
      - **(a) 触发:** 用户通过点击卡片右上角的复选框选择一个或多个员工（底部有“全选”）。然后点击页面底部的“下发数据”按钮。
      - **(b) 选择数据类型与设备:**
        - 系统弹出“下发数据”模态框。
        - 模态框顶部显示“数据类型:”及复选框：密码、NFC、人像、全部。用户勾选需要下发的类型（可多选，“全部”选中时可能自动勾选其他三个）。默认可能选中“全部”。**[异常/替代流程 S3.1]**
        - 模态框中间以网格形式展示选中的员工（如：张三、李四），以及这些员工各自可访问的门禁设备按钮（如：艾依雅居东北门、1号楼1单元）。
        - 每个员工行首有“全选”复选框，用于全选/取消全选该员工的所有可用设备。
        - 用户点击具体的设备按钮来选择或取消选择该设备（为每个员工\*\*单独勾选其下的设备按钮)。
      - **(c) 确认下发:**
        - **方式一 (精确下发):** 用户完成数据类型和设备选择后，点击右下角的“确定”按钮。**[异常/替代流程 S3.2, S3.3]**
        - **方式二 (一键下发):** 用户直接点击“一键下发”按钮，系统默认下发**全部员工的数据类型**到**所有可选设备**。。**[异常/替代流程S3.3]**
      - **(d) 执行与反馈:**
        - App 向后台发送下发指令，包含：选中的员工ID列表、选中的设备ID列表、选中的数据类型。
        - 后台处理指令并分发给设备。
        - App 关闭模态框，并给出操作反馈，如“下发指令已发送”。
      - **(e) 取消下发:** (替代流程) 用户在模态框中点击“取消”按钮，关闭模态框，不下发数据。
  6.  **【删除门卡】**
      - 用户点击卡片上“门卡：已录入”旁边的“删除”按钮。
      - App 弹出确认框“确定要删除该员工的门卡吗？”
      - 用户确认。
      - App 发送删除该员工门卡的指令。**[异常/替代流程 S4]**
- **异常/替代流程:**
  - **S1: 加载列表失败:**
    - 进入页面或滚动加载/翻页时，加载员工列表数据失败（网络/服务器错误）。
  - **S2: 搜索/筛选失败:**
    - 查询无结果：列表区域显示“未找到相关员工”。
    - 加载筛选条件（如小区/楼栋/单元）失败。
    - 应用筛选条件时查询失败（网络/服务器错误）。
  - **S3: 数据下发失败**
    - **S3.1: 模态框加载/操作失败:**
      - 触发下发后，加载可选设备列表失败，无法显示模态框或设备列表为空/错误。
      - 在模态框中选择数据类型或设备时发生移动端端脚本错误。
    - **S3.2: 移动端校验失败:**
      - 点击“确定”时，未选择任何员工：提示“请选择要下发数据的员工”。
      - 点击“确定”时，未选择任何数据类型：提示“请选择要下发的数据类型”。
      - 点击“确定”时，已选员工未选择任何目标设备:“请选择【某某】员工要下发的设备”。
    - **S3.3: 后端处理失败:**
      - 后台发送下发指令时网络错误或服务器内部错误。
      - 部分设备执行下发操作失败（设备离线、存储满等），App应给出明确的部分成功/失败反馈（需要后续状态查询或日志）。
  - **S4: 删除门卡失败：**
    - 用户取消确认。
    - 网络/服务器错误导致删除指令失败。

**UC-攸家App-009: 邻里互助-查看与审核邻里互助内容**

- **用户故事:** 作为一个被授权在【攸家】App中管理邻里互助内容的员工，我想要查看邻里互助板块的帖子列表，能够通过发布人姓名/手机号或筛选查找内容，并能进入帖子详情页查看完整内容、图片/视频附件和所有留言。如果发现帖子或评论内容违规，我需要能执行删除操作（软删除）。
- **参与者:** 攸家 App 用户
- **前置条件:** 用户已登录并拥有邻里互助权限；存在用户发布的邻里互助内容。
- **基本流程:**
  1.  用户在 App 主界面点击“邻里互助”Tab。
  2.  系统进入“邻里互助”列表页面。
  3.  **【查看与筛选列表】**
      - App 加载并以卡片形式展示内容列表（按发布时间倒序），显示状态标识(已删除/正常)、标签（寻人寻物、失物招领、闲置物品）、发布内容、缩略图、发布者信息、发布时间。
      - 用户可通过顶部搜索框按发布人姓名/手机号搜索。
      - 用户可通过点击“筛选”图标，按小区/楼单/单元/楼层/户室筛选。**[异常/替代流程 L1]**
      - 用户通过上滑加载更多查看。
  4.  **【查看详情与审核操作】**
      - 用户点击任意内容卡片。
      - App 跳转到该帖子的详情页面。**[异常/替代流程 L2]**
      - **(a) 查看帖子详情:** 用户查看页面顶部的发布者信息（人像、姓名、户室）、发布时间、完整的正文内容以及图片/视频附件（图片可点击放大，视频可点击播放）。若帖子已被删除，会显示“删帖原因”。
      - **(b) 删除帖子:**
        - 如果帖子状态为“正常”：用户找到并点击页面底部红色的“删帖”按钮。
        - App 弹出确认对话框（强调不可恢复）。用户可能需要输入“删帖原因”。
        - 用户确认删除。**[异常/替代流程 M1]**
        - App 发送指令，后台将帖子标记为已删除。界面刷新，删帖按钮消失，并显示删帖原因（如果输入了）。
      - **(c) 查看与删除评论：**
        - 用户查看帖子下方的“留言”列表（“展开更多”）。
        - 用户浏览每条评论（评论人头像、评论人姓名、评论人昵称、评论时间、内容、图片等）。
        - 如果某条评论状态为“正常”：用户找到该评论右侧红色的“删除”按钮并点击。
        - App 弹出确认删除评论的对话框。
        - 用户确认删除。**[异常/替代流程 M2]**
        - App 发送指令，后台将评论标记为删除。界面刷新，该评论内容被标记为已删除，删除按钮消失。
      - **(d) 导航:** 用户完成查看或审核后，可点击左上角返回箭头返回列表页面。
- **异常/替代流程:**
  - **L1: 搜索/筛选失败:**
    - 查询无结果：列表区域显示“未找到相关内容”。
    - 加载筛选条件选项（如小区、楼栋、单元、楼层、户室）失败。
    - 应用筛选条件时查询失败（网络/服务器错误）。
  - **L2: 加载详情失败:**
    - 点击卡片后加载帖子详情、图片/视频附件或评论列表失败（网络/服务器错误）： 详情页显示加载失败或内容不全。
    - 加载更多评论或评论分页失败。
  - **M1: 删除帖子失败:**
    - 用户在确认对话框中点击“取消”。
    - 网络/服务器错误导致更新帖子状态失败。
    - 帖子状态已被他人修改（例如已被删除），提示“帖子状态已更新，请刷新”。
  - **M2: 删除评论失败:**
    - 用户在确认对话框中点击“取消”。
    - 网络/服务器错误导致更新评论状态失败。
    - 评论状态已被他人修改（例如已被删除），提示“评论状态已更新，请刷新”。
  - **加载列表失败:**
    - 进入页面或加载更多时，加载内容列表失败（网络/服务器错误）。

**UC-攸家App-010: 举报管理-处理举报信息**

- **用户故事:** 作为一个被授权在【攸家】App中处理用户举报的员工，我想要查看用户提交的举报列表，能够按举报人姓名/手机号进行搜索，进入举报详情后，可以查看举报人、被举报人信息、举报类型、说明、内容链接、证据图片/视频，并根据情况执行回复举报人、警告被举报人、禁封被举报人或删除被举报内容的操作。
- **参与者:** 攸家 App 用户 (拥有举报管理权限)
- **前置条件:** 用户已登录并拥有相应权限；存在用户提交的举报记录；举报类型已在【攸家运管】配置。
- **基本流程:**
  1.  用户在 App 主界面点击“举报管理”Tab。
  2.  系统进入“举报管理”列表页面。
  3.  **【查看与搜索举报列表】**
      - App 加载并以卡片形式展示举报列表，显示举报类型、举报人信息、举报内容摘要、状态（未回复/已回复）、举报时间。
      - 用户可在顶部的搜索框输入“举报人手机号或举报人姓名”进行搜索。
      - 用户可点击“清空”清除搜索。
      - 用户可通过上滑加载更多或分页查看记录。**[异常/替代流程 L1]**
  4.  **【查看并处理举报详情】**
      - 用户点击列表中的某条举报记录卡片。
      - App 跳转到“举报管理详情”页面 。**[异常/替代流程 L2]**
      - **(a) 查看信息:** 用户查看页面展示的详细信息：举报时间、举报类型、举报人信息、举报说明、举报内容摘要、举证照片/视频（可点击查看）、已有的回复内容、被举报人信息、已有的警告内容、已有的禁封原因。
      - **(b) 执行处理操作**
        - **回复举报人:** 点击“回复举报人”按钮 -> 弹出输入框 -> 输入回复内容 -> 确认发送。**[异常/替代流程 P1]**
        - **警告被举报人:** 点击“警告被举报人”按钮 -> 弹出输入框要求输入警告内容 -> 确认发送警告。**[异常/替代流程 P2]**
        - **禁封被举报人:** 点击“禁封被举报人”按钮 -> 弹出输入框->输入禁封原因） -> 确认禁封。**[异常/替代流程 P3]**
        - **查看举报内响起:** 点击“举报内容摘要” -> 跳转到“邻里互助详情”页面。
      - **(c) 更新状态:** 执行任一处理操作后，系统后台应将举报状态更新为“已处理”（App 界面可能需要刷新才能看到状态变化或操作历史）。
      - **(d) 返回:** 用户点击左上角返回箭头，返回举报列表页面。
- **异常/替代流程:**
  - **L1: 加载/搜索列表失败:**
    - 进入页面或加载更多时，加载举报列表失败（网络/服务器错误）。
    - 搜索无结果：列表显示为空状态提示。
  - **L2: 加载详情失败:**
    - 点击列表项后，加载举报详情信息（包括证据）失败（网络/服务器错误）。
    - 查看证据图片/视频失败。
  - **P1: 回复举报人失败:**
    - 回复内容为空。
    - 发送回复时网络/服务器错误。
  - **P2: 警告被举报人失败:**
    - 警告内容为空。
    - 发送警告或更新用户警告次数时网络/服务器错误。
    - 用户取消确认。
  - **P3: 禁封被举报人失败:**
    - 禁封原因为空。
    - 更新用户状态时网络/服务器错误。
    - 用户取消确认。
    - 尝试禁封已被禁封的用户（按钮应置灰或提示）。

**UC-攸家App-011: 设置-访问和使用设置功能**

- **用户故事:** 作为一个 攸家App 用户，我想要访问设置页面，执行如查看“关于我们”、检查更新（手动入口）、或者注销账户等操作。
- **参与者:** App 用户
- **前置条件:** 用户已登录 App，并进入“设置”页面。
- **基本流程:**
  1.  用户进入“设置”页面。
  2.  页面显示设置项列表：
      - 关于我们：点击后进入“关于我们”页面，显示 App Logo、版本号、服务协议、隐私政策入口。
      - 检查更新：点击后手动触发 UC-攸家-002 的检查更新流程。
      - 注销账户：点击后进入注销账户说明/申请页面，告知注销后果和流程。
  3.  用户点击底部“退出登录”按钮，弹出确认框，确认后返回到登录页面 (UC-攸家-001)。
- **异常流程:**
  - 访问服务协议/隐私政策失败 (网络错误)：提示加载失败。

**UC-攸家App-012:我的- 查看与管理个人信息**

- **用户故事:** 作为一个【攸家】App用户，我想要能方便地查看我的个人资料，包括头像、姓名、手机号和昵称，并且能够更换我的头像、修改姓名和昵称，以及在需要时修改我的登录手机号（通过验证旧手机号）或安全退出登录。
- **参与者:** 攸家 App 用户 (所有已登录用户)
- **前置条件:** 用户已登录【攸家】App。
- **基本流程:**
  1.  用户在App主界面点击“我的头像”图标。
  2.  系统进入“我的”页面。
  3.  **【查看信息】** 页面顶部显示用户当前的头像，下方列表显示用户的姓名、手机号（部分隐藏）、昵称。
  4.  **【更换头像】**
      - 用户点击头像图片或“更换头像”文字/按钮。
      - App 弹出操作菜单，提供“拍照”和“从相册选择”选项。**[异常/替代流程 P1.1]**
      - 用户选择其中一种方式获取新图片。
      - App 提供图片裁剪功能。用户调整裁剪框后确认。**[异常/替代流程 P1.2]**
      - App 上传裁剪后的图片到服务器。**[异常/替代流程 P1.3]**
      - 上传成功后，个人信息页面的头像更新。
  5.  **【修改姓名】**
      - 用户点击“姓名”所在行（显示当前姓名和 ">" 图标）。
      - App 跳转到“修改姓名”页面或弹出输入框，预填当前姓名。
      - 用户输入新姓名后点击“保存”或“完成”。**[异常/替代流程 P2]**
      - 保存成功后，App 返回个人信息页面，姓名更新。
  6.  **【修改昵称】**
      - 用户点击“昵称”所在行（显示当前昵称或“暂无”和 ">" 图标）。
      - App 跳转到“修改昵称”页面或弹出输入框。
      - 用户输入新昵称后点击“保存”或“完成”。**[异常/替代流程 P3]**
      - 保存成功后，App 返回个人信息页面，昵称更新。
  7.  **【修改手机号】**
      - 用户点击“手机号”所在行（显示部分隐藏的手机号和 ">" 图标）。
      - App 进入修改手机号流程：
        - (a) 验证旧手机号（发送验证码到旧号，用户输入验证）。**[异常/替代流程 P4.1]**
        - (b) 输入新手机号。
        - (c) 验证新手机号（发送验证码到新号，用户输入验证）。**[异常/替代流程 P4.2, P4.3]**
        - (d) 确认修改。**[异常/替代流程 P4.4]**
      - 修改成功后，App 可能提示重新登录或返回个人信息页面，手机号显示更新。
  8.  **【退出登录】**
      - 用户在个人信息页面（或通过顶部的“设置”入口进入的设置页面）找到并点击“退出登录”按钮。
      - App 弹出确认退出对话框。
      - 用户确认退出。**[异常/替代流程 P5]**
      - App 清除本地登录状态（Token），返回到登录页面 (UC-攸家-001)。
- **异常/替代流程:**
  - **P1: 更换头像失败**
    - **P1.1: 获取图片失败:** App 无相机/相册权限；用户取消拍照/选择；读取相册失败。
    - **P1.2: 图片裁剪失败:** 裁剪工具出错；用户取消裁剪。
    - **P1.3: 图片上传失败:** 网络错误；图片过大/格式不支持；服务器处理失败。
  - **P2: 修改姓名失败**
    - **P2.1: 前端校验失败:** 姓名为空；姓名过长或包含非法字符。
    - **P2.2: 保存失败:** 网络/服务器错误。
    - **P2.3: 用户取消:** 用户在编辑页面点击返回或取消。
  - **P3: 修改昵称失败**
    - **P3.1: 前端校验失败:** 昵称过长或包含非法字符。
    - **P3.2: 保存失败:** 网络/服务器错误。
    - **P3.3: 用户取消:** 用户在编辑页面点击返回或取消。
  - **P4: 修改手机号失败**
    - **P4.1: 旧手机号验证失败:** 获取验证码失败（网络、服务、频率）；验证码错误/过期。
    - **P4.2: 新手机号验证失败:** 获取验证码失败（网络、服务、频率）；验证码错误/过期。
    - **P4.3: 新手机号已被占用:** 后台校验发现新手机号已被其他账号注册/绑定。
    - **P4.4: 最终确认修改失败:** 网络/服务器错误。
  - **P5: 退出登录失败:**
    - 网络错误导致退出指令发送失败。
  - **加载个人信息失败:**
    - 进入“我的”页面时，加载头像、姓名、手机号、昵称等信息失败（网络/服务器错误）。

## 3. 功能需求 (Functional Requirements)

### 3.1 登录与更新 (对应 UC-攸家App-001, UC-攸家App-002)

- **3.1.1 功能描述:** 提供安全的员工登录机制及App自动更新检查。
- **3.1.2 需求详述:**

  - **界面元素:** 登录页（手机号、验证码、获取按钮、登录按钮）、更新提示框。

  ![图片](./img/登录-验证码.png)![图片](./img/登录-指纹验证.png)

  - **交互逻辑:** (见 UC-攸家-001, UC-攸家-002) 启动检查更新 -> 手机号+验证码登录。
  - **业务规则:** 账号来自【攸家运管】；验证码规则同攸家运管平台；Token有效期30天；支持强制/非强制更新。
  - **权限控制:** 登录成功后获取【攸家】App的权限。
  - **异常处理:** (UC-攸家-001, UC-攸家-002)。

### 3.2 设备注册 (对应 UC-攸家App-003a, 003b, 003c)

- **3.2.1 功能描述:** 支持安装人员通过App注册多种类型的设备（门禁、网关、呼叫器等）。
- **3.2.2 需求详述 (门禁注册):**

  - **界面元素:** 类型选择、扫码/信息录入（功能设施类型、基础设施、设备编号、是否开放给住户、住户可用范围、备注）。

  ![图片](./img/设备注册/门禁注册/门禁注册-首页.png)![图片](./img/设备注册/门禁注册/门禁注册-选择功能设施类型.png)![图片](./img/设备注册/门禁注册/门禁注册-选择基础设施类型.png)![图片](./img/设备注册/门禁注册/门禁注册-选择基础设施.png)![图片](./img/设备注册/门禁注册/门禁注册-备注.png)![图片](./img/设备注册/门禁注册/门禁注册-选择住户使用范围.png)

  - **交互逻辑:** (见 UC-攸家-003a) 扫码获取设备编号 -> 选设备功能设施类型->选位置->选是否开放给住户->选开发给哪些住户 ->输入备注-> 确认注册。
  - **权限控制:** 需要“设备注册”权限。
  - **异常处理:** (见 UC-攸家-003a)。

- **3.2.3 需求详述 (网关注册):**

  - **界面元素:** 类型选择、扫码/信息录入（基础设施、设备编号）。

  ![图片](./img/设备注册/网关注册/网关注册-首页.png) ![图片](./img/设备注册/网关注册/网关注册-选择基础设施.png)

  - **交互逻辑:** (见 UC-攸家-003b) 扫码获取设备编号 -> 选位置 -> 确认注册。
  - **权限控制:** 需要“设备注册”权限。
  - **异常处理:** (见 UC-攸家-003b)。

- **3.2.4 需求详述 (呼叫器注册):**

  - **界面元素:** 类型选择、扫码/信息录入（基础设施、设备编号、备注）。

  ![图片](./img/设备注册/呼叫器注册/呼叫器注册-首页.png) ![图片](./img/设备注册/呼叫器注册/呼叫器注册-选择基础设施.png)![图片](./img/设备注册/呼叫器注册/呼叫器注册-备注.png)

  - **交互逻辑:** (见 UC-攸家-003c) 扫码获取设备编号 -> 选位置->输入备注-> 确认注册。
  - **权限控制:** 需要“设备注册”权限。
  - **异常处理:** (UC-攸家-003c)。

### 3.3 设备调试 (对应 UC-攸家App-004)

- **3.3.1 功能描述:** 提供对单个已注册设备(门禁)的调试功能集合，包括状态查看、远程操作和临时凭证管理。
- **3.3.2 需求详述:**

  - **界面元素:** 设备调试主界面；顶部导航栏；二维码扫描区域/位置选择入口；设备信息展示区（位置、编号、实录/应录计数、调试数据状态<密码/门卡/人像>）；底部操作按钮栏（打开/关闭过渡、远程开门、下发数据、设置密码、录入门卡、录入人像、清除/删除凭证）。

  ![图片](./img/设备调试/首页-设备调试.png)![图片](./img/设备调试/调试-选择基础设施.png)![图片](./img/设备调试/调试-选择设备.png)![图片](./img/设备调试/调试-设置密码.png)![图片](./img/设备调试/调试-录入门卡.png)![图片](./img/设备调试/调试-录入人像-step1.png)![图片](./img/设备调试/调试-录入人像-step2.png)![图片](./img/设备调试/调试-录入人像-提示.png)![图片](./img/设备调试/调试-人像大图.png)

  - **交互逻辑:** (见 UC-攸家-004) 进入调试Tab -> 扫描设备二维码或按位置选择 -> 查看设备信息及调试凭证状态 -> 点击底部按钮执行相应调试操作。
  - **业务规则:** 通过扫描二维码或位置选择调试设备；显示设备端与服务器端凭证计数对比；调试数据指当前App用户在此设备上的临时凭证（24小时有效）；临时凭证的添加会覆盖同类型旧凭证；各项远程操作需设备在线（通常）。
  - **数据需求:** 设备ID/SN/编号、设备位置、设备网络状态、设备当前模式、设备端/服务器端人像/密码/NFC计数、当前用户在该设备上的临时调试凭证（密码值、NFC卡号、人像特征URL）、操作结果反馈。
  - **权限控制:** 需要“设备调试”权限，且对选定设备有操作权。
  - **异常处理:** (见 UC-攸家-004)。

### 3.4 设备解绑 (对应 UC-攸家App-005)

- **3.4.1 功能描述:** 支持安装/运维人员通过App扫描设备二维码或按位置选择设备，将设备与系统中关联的基础设施位置解绑。
- **3.4.2 需求详述:**

  - **界面元素:** 设备解绑主界面；顶部导航栏；二维码扫描区域/位置选择入口；设备信息展示区（类型标签、基础设施位置、设备编号<含复制>）；解绑原因输入框（选填）；确认解绑按钮。

  ![图片](./img/设备解绑/首页-设备解绑.png)

  - **交互逻辑:** (见 UC-攸家-005) 进入解绑功能 -> 扫描设备二维码或按位置选择 -> 系统识别并显示设备类型及信息 -> 用户（可选）输入解绑原因 -> 点击确认解绑 -> 系统处理并反馈结果。
  - **业务规则:** 通过扫描二维码或位置选择设备；适用于已注册的门禁、网关、呼叫器等支持解绑的设备类型；解绑原因可选填；解绑成功后设备与基础设施关联解除；需记录解绑日志。
  - **数据需求:** 设备ID/二维码信息、设备类型、基础设施位置、设备编号、解绑原因（可选）。
  - **权限控制:** 需要“设备解绑”权限，且对选定设备有操作权。
  - **异常处理:** (见 UC-攸家-005)。

### 3.6 设备控制 (对应 UC-攸家App-006)

- **3.6.1 功能描述:** 提供按位置筛选设备列表，并对选中的一个或多个设备执行远程管理操作的功能。
- **3.6.2 需求详述:**

  - **界面元素:** 设备控制主界面；基础设施位置选择器；设备列表（含复选框、位置、编号、状态、计数、版本等）；底部批量操作按钮栏（开/关过渡、重启、上报、下发）。

  ![图片](./img/设备控制/首页-设备控制.png)

  - **交互逻辑:** (见 UC-攸家-006) 选择位置筛选 -> 查看设备列表 -> 勾选设备 -> 点击底部按钮执行批量操作。
  - **业务规则:** 列表根据位置筛选动态刷新；批量操作作用于所有选中设备；需明确各操作对设备状态的要求。
  - **数据需求:** 设备列表信息、基础设施层级数据。
  - **权限控制:** 需要“设备控制”权限。
  - **异常处理:** ( UC-攸家-006)。

### 3.7 用户信息查看 (对应 UC-攸家App-007, UC-攸家App-008)

- **3.7.1 功能描述:** 提供查看所负责小区内的住户和物业员工基本信息列表的功能，并支持对住户或员工执行数据下发操作。
- **3.7.2 需求详述 (小区住户查看与筛选):**

  - **界面元素:** 列表页（搜索框、筛选按钮、住户卡片列表<含凭证状态、复选框>、批量下发按钮）；下发数据模态框（数据类型选择、住户-设备网格、确认/取消按钮）。

  ![图片](./img/小区住户/首页-小区住户.png)![图片](./img/小区住户/住户-筛选.png)![图片](./img/小区住户/住户-下发数据.png)

  - **交互逻辑:** (见 UC-攸家-007) 查看/搜索/筛选列表；勾选住户触发下发；在模态框中选择数据类型和目标设备；确认下发。
  - **业务规则:** 列表展示关键信息和凭证状态；下发可选凭证类型；设备列表为可访问设备。
  - **数据需求:** 住户ID、姓名、手机号、户室、身份、登记方式、凭证状态、时间；可访问设备列表。
  - **权限控制:** 需要“小区住户”权限。
  - **异常处理:** (UC-攸家-007)。

- **3.7.3 需求详述 (小区员工查看与筛选):**

  - **界面元素:** 列表页（搜索框、筛选按钮、员工卡片列表<含岗位、凭证状态、权限范围、复选框>、批量下发按钮）；下发数据模态框。

  ![图片](./img/小区员工/首页-小区员工.png)![图片](./img/小区员工/员工-筛选.png)![图片](./img/小区员工/员工-下发数据.png)

  - **交互逻辑:** (见 UC-攸家-008) 查看/搜索/筛选列表；勾选员工触发下发；在模态框选类型和设备；确认下发；[可选]删除门卡。
  - **业务规则:** 列表展示关键信息、凭证状态、权限范围；下发可选类型。
  - **数据需求:** 员工ID、姓名、手机号、小区/组织、岗位、凭证状态、权限范围、添加时间；可访问设备列表。
  - **权限控制:** 需要“小区员工”权限。
  - **异常处理:** (UC-攸家-008)。

### 3.8 内容/举报 (对应 UC-攸家App-009, UC-攸家App-010)

- **3.8.1 功能描述:** 提供移动端的内容查看与审核、举报查看与处理功能。
- **3.8.2 需求详述 (邻里互助查看与审核):**

  - **界面元素:** 列表页；详情页（含评论列表、删帖/删评论操作）。

  ![图片](./img/邻里互助/首页-邻里互助.png)![图片](./img/邻里互助/邻里互助-留言详情.png)![图片](./img/邻里互助/邻里互助-删帖.png)

  - **交互逻辑:** (见 UC-攸家-009) 查看/搜索/筛选列表；进入详情；查看帖子/评论；执行软删除。
  - **业务规则:** 列表展示概要；详情展示完整信息；删除需权限且为软删除。
  - **数据需求:** 帖子信息；评论信息；状态。
  - **权限控制:** 需要“邻里互助”权限。
  - **异常处理:** (UC-攸家-009)。

- **3.8.3 需求详述 (举报查看与处理):**

  - **界面元素:** 列表页；详情页（含举报信息、被举报人信息、内容信息、处理操作按钮）。

  ![图片](./img/举报管理/首页-举报管理.png)![图片](./img/举报管理/举报管理-详情.png)![图片](./img/举报管理/举报管理-回复_警告/禁封.png)

  - **交互逻辑:** (见 UC-攸家-010) 查看/筛选列表；进入详情；查看信息；执行处理操作（回复/警告/禁封/删除内容）。
  - **业务规则:** 提供多种处理手段；操作结果更新举报状态。
  - **数据需求:** 举报记录详情、处理历史。
  - **权限控制:** 需要“举报管理”权限。
  - **异常处理:** (见 UC-攸家-010)。

### 3.9 设置(对应 UC-攸家App-011)

- **3.9.1 功能描述:** 提供查看关于我们、检查更新、注销账户、退出登录功能。
- **3.9.2 需求详述:**

  - **界面元素:** 查看关于我们入口、检查更新入口、注销账户入口、退出登录按钮。

  ![图片](./img/设置/设置.png)![图片](./img/设置/设置-关于我们.png)![图片](./img/设置/设置-注销账户.png)

  - **交互逻辑:** (见 UC-攸家-011)
  - **权限控制:** 无特殊权限要求。
  - **异常处理:** (见 UC-攸家-011)。

### 3.10 个人信息管理 (对应 UC-攸家App-012)

- **3.10.1 功能描述:** 提供用户查看和管理个人基本信息（头像、姓名、昵称、手机号）。
- **3.10.2 需求详述:**
  - **界面元素:** 个人信息页面；修改姓名/昵称/手机号的二级页面/弹窗；图片选择器/裁剪器。
  ![图片](./img/我的/我的.png)![图片](./img/我的/我的-修改手机号.png)
  - **交互逻辑:** (见 UC-攸家-012)。
  - **业务规则:** 用户只能修改自身信息；修改手机号需验证。
  - **数据需求:** 用户ID、头像URL、姓名、手机号、昵称。
  - **权限控制:** 无特殊权限要求。
  - **异常处理:** (见 UC-攸家-012)。

## 4. 非功能需求 (Non-Functional Requirements)

- **4.1 性能需求:**
  - **响应时间:** 核心操作（如登录、远程开门、设备状态刷新）响应时间 < 1.5 秒；列表数据加载时间 < 3 秒（首屏）。
  - **并发用户数:** 支持至少 100 个管理用户同时在线操作（需压力测试验证）。
  - **资源消耗:** App 安装包大小 < 50MB (iOS/Android 分别考虑)；正常使用时内存占用和 CPU 消耗在合理范围，无明显卡顿或发热。
- **4.2 可用性需求:**
  - **易学性:** 新用户在无指导情况下，5 分钟内能完成登录、查看设备列表、执行一次远程开门操作。
  - **易用性:** 常用操作（设备控制、查看人员、处理举报）路径清晰，步骤不超过 3 步。界面元素符合用户预期。
  - **用户满意度:** (目标) V1.0 发布后 1 个月内，通过用户调研，核心功能满意度达到 80% 以上。
- **4.3 可靠性需求:**
  - **稳定性:** App 核心功能 Crash 率 < 0.1%。
  - **数据一致性:** App 显示的状态/数据与后台、设备状态保持最终一致（允许网络延迟）。
  - **错误恢复:** 网络中断后恢复时，App 能自动重连或提示用户手动刷新。操作失败有明确提示。
- **4.4 安全性需求:**
  - **传输安全:** App 与后台 API 通信必须使用 HTTPS 加密。
  - **数据安全:** 用户密码（若有）、敏感配置信息（如设备密钥）需加密存储。验证码短信内容避免包含敏感信息。导出数据需进行权限校验。
  - **认证安全:** 防止暴力破解登录（如验证码尝试次数限制）。登录状态 Session/Token 有效期及刷新机制。
  - **访问控制:** 严格执行基于角色的权限控制（即使 V1.0 简化，后台需支持）。
  - **防逆向:** 进行基本的代码混淆和加固。
- **4.5 可维护性需求:**
  - **代码规范:** 遵循团队统一的 iOS/Android 编码规范。关键代码有清晰注释。
  - **模块化:** 功能模块清晰分离，降低耦合度。
  - **配置化:** 服务器地址、关键参数（如验证码有效期、重试次数）应可配置。
- **4.6 可扩展性需求:**
  - **架构设计:** 考虑未来接入更多类型的智能设备。API 设计遵循 RESTful 风格，易于扩展。
  - **人员/设备类型:** 注册流程和参数配置应易于扩展支持新的类型和属性。
- **4.7 兼容性需求:**
  - **操作系统:** 支持 iOS 13.0 及以上版本，Android 6.0 及以上版本。
  - **设备:** 兼容主流手机品牌和屏幕尺寸（如 iPhone 8 及以上，主流 Android 厂商近 3 年发布的手机）。完成在主要分辨率上的测试。

## 6. 界面与交互设计 (UI/UX Design)

- **6.1 设计原则:**
  - **一致性:** 遵循统一的设计风格、色彩（主色调用橙色）、字体、图标和布局。
  - **清晰性:** 信息层级分明，重要信息突出。操作按钮语义明确。
  - **效率性:** 针对管理后台特性，优化高频操作路径，减少点击次数。
  - **反馈及时:** 用户操作后（尤其耗时或后台操作），给予明确的加载、成功或失败反馈。
  - **容错性:** 提供撤销或确认步骤（如删除、解绑操作），减少误操作风险。输入校验友好。
- **6.2 整体交互流程:**
  - App 启动 -> 检查更新 -> (若未登录) 登录页 -> (若已登录) 主界面。
  - 主界面采用底部 Tab 导航（推测，原型未完全展示），包含“设备”、“人员”（住户/员工可能合并或二级入口）、“社区”（互助/举报可能合并或二级入口）、“我的”等主要模块。
  - 各模块内采用列表 -> 详情 -> 操作/编辑 的标准流程。
  - 复杂操作（如注册、筛选、参数设置）使用弹窗或新页面承载。
