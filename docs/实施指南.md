# 攸家 App V2.0 实施指南

## 快速开始

### 1. 环境准备

确保你的开发环境已安装以下工具：

- **Node.js**: 18.0.0 或更高版本
- **npm**: 9.0.0 或更高版本
- **Expo CLI**: 最新版本
- **Git**: 用于版本控制

```bash
# 检查版本
node --version
npm --version
npx expo --version
```

### 2. 项目初始化

```bash
# 1. 克隆项目（如果是从现有仓库）
git clone <repository-url>
cd YouJia

# 2. 运行项目结构初始化脚本
npm run setup

# 3. 安装依赖
npm install

# 4. 配置环境变量
cp .env.example .env
# 编辑 .env 文件，填入正确的配置信息

# 5. 启动开发服务器
npm start
```

### 3. 开发环境配置

#### 3.1 VS Code 推荐插件

在项目根目录创建 `.vscode/extensions.json`：

```json
{
  "recommendations": [
    "ms-vscode.vscode-typescript-next",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-eslint",
    "expo.vscode-expo-tools",
    "ms-vscode.vscode-react-native"
  ]
}
```

#### 3.2 VS Code 工作区设置

创建 `.vscode/settings.json`：

```json
{
  "typescript.preferences.importModuleSpecifier": "relative",
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "files.associations": {
    "*.tsx": "typescriptreact"
  }
}
```

## 开发流程

### 1. 功能开发流程

#### 1.1 创建功能分支

```bash
# 从 develop 分支创建功能分支
git checkout develop
git pull origin develop
git checkout -b feature/device-register

# 或者从 main 分支创建热修复分支
git checkout main
git pull origin main
git checkout -b hotfix/login-bug-fix
```

#### 1.2 开发步骤

1. **分析需求**: 仔细阅读 PRD 中对应的用例描述
2. **设计接口**: 定义组件 Props 和 API 接口类型
3. **编写测试**: 先写测试用例（TDD 方式）
4. **实现功能**: 编写组件和业务逻辑
5. **集成测试**: 确保功能正常工作
6. **代码审查**: 自我审查代码质量

#### 1.3 代码提交

```bash
# 添加文件
git add .

# 提交代码（遵循提交规范）
git commit -m "feat(device): add device registration functionality

- Add device registration form component
- Implement QR code scanning
- Add device type selection
- Add location picker component

Closes #123"

# 推送到远程分支
git push origin feature/device-register
```

### 2. 测试策略

#### 2.1 单元测试

```bash
# 运行所有测试
npm test

# 监听模式运行测试
npm run test:watch

# 生成覆盖率报告
npm run test:coverage
```

#### 2.2 测试文件组织

```
__tests__/
├── components/
│   ├── ui/
│   │   └── Button.test.tsx
│   └── business/
│       └── DeviceCard.test.tsx
├── hooks/
│   └── useAuth.test.ts
├── services/
│   └── api/
│       └── device.test.ts
└── utils/
    └── validation.test.ts
```

#### 2.3 测试示例

```typescript
// __tests__/components/ui/Button.test.tsx
import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import Button from '@/components/ui/Button';

describe('Button Component', () => {
  it('should render correctly', () => {
    const { getByText } = render(
      <Button title="Test Button" />
    );

    expect(getByText('Test Button')).toBeTruthy();
  });

  it('should call onPress when pressed', () => {
    const onPress = jest.fn();
    const { getByText } = render(
      <Button title="Test Button" onPress={onPress} />
    );

    fireEvent.press(getByText('Test Button'));
    expect(onPress).toHaveBeenCalledTimes(1);
  });

  it('should be disabled when disabled prop is true', () => {
    const onPress = jest.fn();
    const { getByText } = render(
      <Button title="Test Button" onPress={onPress} disabled />
    );

    fireEvent.press(getByText('Test Button'));
    expect(onPress).not.toHaveBeenCalled();
  });
});
```

### 3. 代码质量保证

#### 3.1 代码检查

```bash
# ESLint 检查
npm run lint

# 自动修复 ESLint 问题
npm run lint:fix

# TypeScript 类型检查
npm run type-check
```

#### 3.2 Git Hooks 配置

安装 Husky：

```bash
# 安装 husky
npm install --save-dev husky

# 初始化 husky
npx husky install

# 添加 pre-commit hook
npx husky add .husky/pre-commit "npx lint-staged"

# 添加 commit-msg hook
npx husky add .husky/commit-msg "npx commitlint --edit $1"
```

## 部署流程

### 1. 构建配置

#### 1.1 开发构建

```bash
# 构建开发版本
npm run build:android -- --profile development
npm run build:ios -- --profile development
```

#### 1.2 预览构建

```bash
# 构建预览版本
npm run build:android -- --profile preview
npm run build:ios -- --profile preview
```

#### 1.3 生产构建

```bash
# 构建生产版本
npm run build:android -- --profile production
npm run build:ios -- --profile production
```

### 2. 发布流程

#### 2.1 内部测试发布

```bash
# 发布到内部测试
eas build --platform all --profile preview
```

#### 2.2 应用商店发布

```bash
# 构建生产版本
eas build --platform all --profile production

# 提交到应用商店
npm run submit:android
npm run submit:ios
```

## 常见问题解决

### 1. 开发环境问题

#### 1.1 Metro 缓存问题

```bash
# 清除 Metro 缓存
npx expo start --clear

# 或者
npx react-native start --reset-cache
```

#### 1.2 依赖安装问题

```bash
# 清除 node_modules 和重新安装
rm -rf node_modules package-lock.json
npm install

# 或者使用 npm ci（推荐用于 CI/CD）
npm ci
```

#### 1.3 iOS 模拟器问题

```bash
# 重置 iOS 模拟器
xcrun simctl erase all

# 重新安装 iOS 依赖
cd ios && pod install && cd ..
```

### 2. 构建问题

#### 2.1 Android 构建失败

- 检查 `android/gradle.properties` 配置
- 确保 Java 版本兼容性
- 清除 Gradle 缓存：`cd android && ./gradlew clean`

#### 2.2 iOS 构建失败

- 检查 Xcode 版本兼容性
- 更新 CocoaPods：`cd ios && pod update`
- 检查证书和配置文件

### 3. 性能问题

#### 3.1 启动速度优化

- 使用 Expo 的 lazy loading
- 优化图片资源大小
- 减少初始化时的同步操作

#### 3.2 内存使用优化

- 使用 React.memo 优化组件渲染
- 及时清理事件监听器
- 优化图片缓存策略

## 团队协作

### 1. 代码审查清单

- [ ] 代码符合项目规范
- [ ] 有适当的测试覆盖
- [ ] 没有明显的性能问题
- [ ] 错误处理完善
- [ ] 类型定义正确
- [ ] 注释清晰易懂

### 2. 发布检查清单

- [ ] 所有测试通过
- [ ] 代码审查完成
- [ ] 功能测试完成
- [ ] 性能测试通过
- [ ] 安全检查完成
- [ ] 文档更新完成

### 3. 沟通协作

- 使用 GitHub Issues 跟踪问题
- 使用 Pull Request 进行代码审查
- 定期进行技术分享和代码回顾
- 及时更新项目文档

## 总结

本实施指南提供了攸家 App V2.0 项目的完整开发流程，包括环境配置、开发流程、测试策略、部署流程和问题解决方案。遵循这些指南可以确保项目的高质量交付和团队的高效协作。
