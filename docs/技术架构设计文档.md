# 攸家 App V2.0 技术架构设计文档

## 1. 项目概述

攸家App是一个基于React Native + Expo技术栈的移动端门禁管理系统，面向小区管理人员（设备安装人员、运维调试人员）提供智能硬件设备管理、人员信息管理、社区内容审核等功能。

## 2. 技术栈选型

### 2.1 核心技术栈

- **框架**: React Native 0.79.2 + Expo SDK 53
- **路由**: Expo Router (基于文件系统的路由)
- **语言**: TypeScript 5.8.3
- **状态管理**: Zustand + React Query (TanStack Query)
- **UI组件库**: Tamagui
- **网络请求**: Axios + React Query
- **本地存储**: Expo SecureStore + AsyncStorage
- **相机/扫码**: Expo Camera + Expo BarCodeScanner
- **图片处理**: Expo ImagePicker + Expo ImageManipulator
- **推送通知**: Expo Notifications
- **设备信息**: Expo Device + Expo Constants

### 2.2 开发工具

- **代码规范**: ESLint + Prettier
- **Git钩子**: <PERSON><PERSON> + lint-staged
- **测试**: Jest + React Native Testing Library
- **调试**: Flipper + React Native Debugger
- **构建**: EAS Build
- **部署**: EAS Submit

## 3. 项目目录结构

```
YouJia/
├── app/                          # Expo Router 路由目录
│   ├── (auth)/                   # 认证相关路由组
│   │   ├── login.tsx            # 登录页面
│   │   └── _layout.tsx          # 认证布局
│   ├── (tabs)/                   # 主要功能Tab路由组
│   │   ├── device-register/     # 设备注册模块
│   │   │   ├── index.tsx        # 设备注册首页
│   │   │   ├── door-lock.tsx    # 门禁注册
│   │   │   ├── gateway.tsx      # 网关注册
│   │   │   └── caller.tsx       # 呼叫器注册
│   │   ├── device-debug/        # 设备调试模块
│   │   │   ├── index.tsx        # 设备调试首页
│   │   │   └── [deviceId].tsx   # 设备调试详情
│   │   ├── device-unbind/       # 设备解绑模块
│   │   │   └── index.tsx        # 设备解绑页面
│   │   ├── device-control/      # 设备控制模块
│   │   │   └── index.tsx        # 设备控制页面
│   │   ├── residents/           # 小区住户模块
│   │   │   ├── index.tsx        # 住户列表
│   │   │   └── [residentId].tsx # 住户详情
│   │   ├── staff/               # 小区员工模块
│   │   │   ├── index.tsx        # 员工列表
│   │   │   └── [staffId].tsx    # 员工详情
│   │   ├── community/           # 社区管理模块
│   │   │   ├── mutual-aid/      # 邻里互助
│   │   │   │   ├── index.tsx    # 互助列表
│   │   │   │   └── [postId].tsx # 互助详情
│   │   │   └── reports/         # 举报管理
│   │   │       ├── index.tsx    # 举报列表
│   │   │       └── [reportId].tsx # 举报详情
│   │   ├── profile/             # 个人中心模块
│   │   │   ├── index.tsx        # 个人信息
│   │   │   ├── edit-profile.tsx # 编辑资料
│   │   │   └── settings.tsx     # 设置页面
│   │   └── _layout.tsx          # Tab布局
│   ├── _layout.tsx              # 根布局
│   └── +not-found.tsx           # 404页面
├── src/                         # 源代码目录
│   ├── components/              # 通用组件
│   │   ├── ui/                  # 基础UI组件
│   │   │   ├── Button.tsx       # 按钮组件
│   │   │   ├── Input.tsx        # 输入框组件
│   │   │   ├── Modal.tsx        # 模态框组件
│   │   │   ├── Loading.tsx      # 加载组件
│   │   │   ├── Toast.tsx        # 提示组件
│   │   │   └── index.ts         # 组件导出
│   │   ├── business/            # 业务组件
│   │   │   ├── DeviceCard.tsx   # 设备卡片
│   │   │   ├── UserCard.tsx     # 用户卡片
│   │   │   ├── QRScanner.tsx    # 二维码扫描
│   │   │   ├── ImagePicker.tsx  # 图片选择器
│   │   │   ├── LocationPicker.tsx # 位置选择器
│   │   │   └── DataSyncModal.tsx # 数据下发模态框
│   │   └── layout/              # 布局组件
│   │       ├── Header.tsx       # 页面头部
│   │       ├── TabBar.tsx       # 底部导航
│   │       └── Container.tsx    # 页面容器
│   ├── services/                # 服务层
│   │   ├── api/                 # API接口
│   │   │   ├── auth.ts          # 认证接口
│   │   │   ├── device.ts        # 设备接口
│   │   │   ├── user.ts          # 用户接口
│   │   │   ├── community.ts     # 社区接口
│   │   │   └── index.ts         # 接口导出
│   │   ├── http/                # HTTP客户端
│   │   │   ├── client.ts        # Axios配置
│   │   │   ├── interceptors.ts  # 请求拦截器
│   │   │   └── types.ts         # 请求类型定义
│   │   └── storage/             # 本地存储
│   │       ├── auth.ts          # 认证信息存储
│   │       ├── cache.ts         # 缓存管理
│   │       └── index.ts         # 存储导出
│   ├── stores/                  # 状态管理
│   │   ├── auth.ts              # 认证状态
│   │   ├── device.ts            # 设备状态
│   │   ├── user.ts              # 用户状态
│   │   ├── app.ts               # 应用状态
│   │   └── index.ts             # Store导出
│   ├── hooks/                   # 自定义Hooks
│   │   ├── useAuth.ts           # 认证Hook
│   │   ├── useDevice.ts         # 设备Hook
│   │   ├── usePermissions.ts    # 权限Hook
│   │   ├── useCamera.ts         # 相机Hook
│   │   └── index.ts             # Hook导出
│   ├── utils/                   # 工具函数
│   │   ├── validation.ts        # 表单验证
│   │   ├── format.ts            # 数据格式化
│   │   ├── constants.ts         # 常量定义
│   │   ├── permissions.ts       # 权限工具
│   │   ├── device.ts            # 设备工具
│   │   └── index.ts             # 工具导出
│   ├── types/                   # 类型定义
│   │   ├── auth.ts              # 认证类型
│   │   ├── device.ts            # 设备类型
│   │   ├── user.ts              # 用户类型
│   │   ├── community.ts         # 社区类型
│   │   ├── api.ts               # API类型
│   │   └── index.ts             # 类型导出
│   └── config/                  # 配置文件
│       ├── env.ts               # 环境配置
│       ├── api.ts               # API配置
│       ├── theme.ts             # 主题配置
│       └── constants.ts         # 应用常量
├── assets/                      # 静态资源
│   ├── images/                  # 图片资源
│   │   ├── icons/               # 图标
│   │   ├── logos/               # Logo
│   │   └── placeholders/        # 占位图
│   ├── fonts/                   # 字体文件
│   └── data/                    # 静态数据
├── docs/                        # 文档目录
│   ├── 技术架构设计文档.md      # 本文档
│   ├── API接口文档.md           # API文档
│   ├── 开发规范.md              # 开发规范
│   └── 部署指南.md              # 部署指南
├── scripts/                     # 脚本文件
│   ├── reset-project.js         # 重置项目
│   └── build.js                 # 构建脚本
├── __tests__/                   # 测试文件
│   ├── components/              # 组件测试
│   ├── services/                # 服务测试
│   ├── utils/                   # 工具测试
│   └── setup.ts                 # 测试配置
├── .env.example                 # 环境变量示例
├── .gitignore                   # Git忽略文件
├── .eslintrc.js                 # ESLint配置
├── .prettierrc                  # Prettier配置
├── babel.config.js              # Babel配置
├── metro.config.js              # Metro配置
├── tsconfig.json                # TypeScript配置
├── app.json                     # Expo配置
├── eas.json                     # EAS配置
├── package.json                 # 项目依赖
└── README.md                    # 项目说明
```

## 4. 核心架构设计

### 4.1 分层架构

```
┌─────────────────────────────────────┐
│           Presentation Layer        │  # 展示层 (React Native Components)
├─────────────────────────────────────┤
│           Business Logic Layer      │  # 业务逻辑层 (Hooks + Stores)
├─────────────────────────────────────┤
│           Service Layer             │  # 服务层 (API + Storage)
├─────────────────────────────────────┤
│           Infrastructure Layer      │  # 基础设施层 (HTTP + Native APIs)
└─────────────────────────────────────┘
```

### 4.2 数据流架构

```
UI Components → Custom Hooks → Zustand Stores → API Services → Backend
      ↑                                                            ↓
React Query Cache ←─────────────────────────────────────────── HTTP Client
```

## 5. 技术选型详细说明

### 5.1 状态管理 - Zustand + React Query

**选择理由**:

- Zustand: 轻量级、TypeScript友好、无样板代码
- React Query: 强大的服务端状态管理、自动缓存、后台更新

**使用场景**:

- Zustand: 管理客户端状态（用户信息、应用配置、UI状态）
- React Query: 管理服务端状态（API数据、缓存、同步）

### 5.2 UI组件库 - NativeBase

**选择理由**:

- 基于React Native构建，性能优秀
- 提供丰富的组件和主题系统
- 支持深度定制和扩展
- 良好的TypeScript支持

### 5.3 网络请求 - Axios + React Query

**选择理由**:

- Axios: 功能强大的HTTP客户端，支持拦截器、取消请求等
- React Query: 提供缓存、重试、后台更新等高级功能

### 5.4 本地存储 - Expo SecureStore + AsyncStorage

**使用场景**:

- SecureStore: 存储敏感信息（Token、密码等）
- AsyncStorage: 存储一般配置信息和缓存数据

## 6. 核心功能模块设计

### 6.1 认证模块 (Authentication)

**功能**: 手机号+验证码登录、Token管理、权限控制
**技术实现**:

- 使用Expo SecureStore存储Token
- Zustand管理认证状态
- HTTP拦截器自动添加Token和处理过期

### 6.2 设备管理模块 (Device Management)

**功能**: 设备注册、调试、解绑、控制
**技术实现**:

- 二维码扫描使用Expo BarCodeScanner
- 设备状态实时更新使用WebSocket或轮询
- 批量操作使用Promise.allSettled处理

### 6.3 用户管理模块 (User Management)

**功能**: 住户/员工信息查看、数据下发
**技术实现**:

- 虚拟列表优化大数据渲染
- 搜索防抖优化性能
- 批量选择和操作

### 6.4 社区管理模块 (Community Management)

**功能**: 邻里互助内容审核、举报处理
**技术实现**:

- 图片/视频预览和播放
- 富文本内容展示
- 操作权限控制

### 6.5 个人中心模块 (Profile)

**功能**: 个人信息管理、设置
**技术实现**:

- 头像上传使用Expo ImagePicker
- 图片裁剪使用Expo ImageManipulator
- 手机号修改需要验证码验证

## 7. 数据模型设计

### 7.1 用户相关类型

```typescript
// 用户基础信息
interface User {
  id: string;
  name: string;
  phone: string;
  avatar?: string;
  nickname?: string;
  role: UserRole;
  permissions: Permission[];
  createdAt: string;
  updatedAt: string;
}

// 用户角色
enum UserRole {
  INSTALLER = 'installer', // 安装人员
  MAINTAINER = 'maintainer', // 运维人员
  ADMIN = 'admin', // 管理员
  AUDITOR = 'auditor', // 审核员
}

// 权限定义
enum Permission {
  DEVICE_REGISTER = 'device:register',
  DEVICE_DEBUG = 'device:debug',
  DEVICE_UNBIND = 'device:unbind',
  DEVICE_CONTROL = 'device:control',
  USER_VIEW = 'user:view',
  COMMUNITY_AUDIT = 'community:audit',
  REPORT_HANDLE = 'report:handle',
}
```

### 7.2 设备相关类型

```typescript
// 设备基础信息
interface Device {
  id: string;
  deviceNumber: string;
  deviceType: DeviceType;
  status: DeviceStatus;
  location: Location;
  version: string;
  isOnline: boolean;
  credentials: DeviceCredentials;
  createdAt: string;
  updatedAt: string;
}

// 设备类型
enum DeviceType {
  DOOR_LOCK = 'door_lock', // 门禁
  GATEWAY = 'gateway', // 网关
  CALLER = 'caller', // 呼叫器
}

// 设备状态
enum DeviceStatus {
  NORMAL = 'normal', // 正常
  OFFLINE = 'offline', // 离线
  FAULT = 'fault', // 故障
  MAINTENANCE = 'maintenance', // 维护中
}

// 位置信息
interface Location {
  community: string; // 小区
  building?: string; // 楼栋
  unit?: string; // 单元
  floor?: string; // 楼层
  room?: string; // 房间
}

// 设备凭证信息
interface DeviceCredentials {
  passwordCount: number; // 密码数量
  nfcCount: number; // NFC卡数量
  faceCount: number; // 人脸数量
  serverPasswordCount: number; // 服务器密码数量
  serverNfcCount: number; // 服务器NFC数量
  serverFaceCount: number; // 服务器人脸数量
}
```

## 8. API接口设计规范

### 8.1 RESTful API设计

**基础URL**: `https://api.youjia.com/v1`

**通用响应格式**:

```typescript
interface ApiResponse<T = any> {
  code: number; // 状态码
  message: string; // 消息
  data: T; // 数据
  timestamp: number; // 时间戳
}

interface PaginatedResponse<T> {
  items: T[]; // 数据列表
  total: number; // 总数
  page: number; // 当前页
  pageSize: number; // 页大小
  hasMore: boolean; // 是否有更多
}
```

### 8.2 主要接口列表

```typescript
// 认证接口
POST /auth/send-code          // 发送验证码
POST /auth/login              // 登录
POST /auth/refresh            // 刷新Token
POST /auth/logout             // 登出

// 设备接口
GET  /devices                 // 获取设备列表
POST /devices                 // 注册设备
GET  /devices/:id             // 获取设备详情
PUT  /devices/:id             // 更新设备信息
DELETE /devices/:id           // 解绑设备
POST /devices/:id/control     // 控制设备
POST /devices/batch-control   // 批量控制设备

// 用户接口
GET  /residents               // 获取住户列表
GET  /staff                   // 获取员工列表
POST /users/sync-data         // 数据下发

// 社区接口
GET  /community/posts         // 获取邻里互助列表
GET  /community/posts/:id     // 获取帖子详情
DELETE /community/posts/:id   // 删除帖子
GET  /reports                 // 获取举报列表
POST /reports/:id/handle      // 处理举报
```

## 9. 开发规范

### 9.1 代码规范

**命名约定**:

- 文件名: 使用PascalCase (如: `DeviceCard.tsx`)
- 组件名: 使用PascalCase (如: `DeviceCard`)
- 变量/函数名: 使用camelCase (如: `deviceList`)
- 常量: 使用UPPER_SNAKE_CASE (如: `API_BASE_URL`)
- 类型/接口: 使用PascalCase (如: `Device`, `ApiResponse`)

**文件组织原则**:

- 每个文件只导出一个主要组件/函数
- 相关的类型定义放在同一文件或专门的types文件中
- 工具函数按功能分类组织
- 组件按业务模块分类组织

**代码风格**:

- 使用TypeScript严格模式
- 优先使用函数组件和Hooks
- 使用ESLint + Prettier保证代码风格一致
- 组件props必须定义TypeScript接口

### 9.2 Git工作流

**分支策略**:

- `main`: 主分支，用于生产环境
- `develop`: 开发分支，用于集成测试
- `feature/*`: 功能分支，用于开发新功能
- `hotfix/*`: 热修复分支，用于紧急修复

**提交规范**:

```
<type>(<scope>): <subject>

<body>

<footer>
```

**Type类型**:

- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

### 9.3 测试规范

**测试策略**:

- 单元测试: 覆盖工具函数和业务逻辑
- 组件测试: 覆盖UI组件的渲染和交互
- 集成测试: 覆盖关键业务流程
- E2E测试: 覆盖核心用户场景

**测试工具**:

- Jest: 测试框架
- React Native Testing Library: 组件测试
- MSW: API Mock
- Detox: E2E测试

## 10. 性能优化策略

### 10.1 渲染性能优化

- 使用React.memo优化组件重渲染
- 使用useMemo和useCallback缓存计算结果
- 长列表使用FlatList的虚拟化
- 图片使用Expo Image的优化加载

### 10.2 网络性能优化

- 使用React Query的缓存机制
- 实现请求去重和防抖
- 图片压缩和懒加载
- 分页加载大数据集

### 10.3 包体积优化

- 使用Expo的按需加载
- 移除未使用的依赖
- 图片资源优化压缩
- 代码分割和懒加载

## 11. 安全策略

### 11.1 数据安全

- 敏感信息使用Expo SecureStore加密存储
- API通信使用HTTPS
- Token自动刷新机制
- 请求签名验证

### 11.2 权限控制

- 基于角色的权限系统
- 页面级权限控制
- 操作级权限验证
- 敏感操作二次确认

### 11.3 输入验证

- 前端表单验证
- API参数校验
- XSS防护
- SQL注入防护

## 12. 实施计划

### 12.1 开发阶段划分

**第一阶段 (2周)**: 基础架构搭建

- [ ] 项目初始化和配置
- [ ] 基础组件库搭建
- [ ] 路由和导航配置
- [ ] 状态管理配置
- [ ] HTTP客户端配置
- [ ] 认证模块开发

**第二阶段 (3周)**: 核心功能开发

- [ ] 设备注册功能 (UC-003a/b/c)
- [ ] 设备调试功能 (UC-004)
- [ ] 设备解绑功能 (UC-005)
- [ ] 设备控制功能 (UC-006)

**第三阶段 (2周)**: 用户管理功能

- [ ] 小区住户管理 (UC-007)
- [ ] 小区员工管理 (UC-008)
- [ ] 数据下发功能

**第四阶段 (2周)**: 社区管理功能

- [ ] 邻里互助管理 (UC-009)
- [ ] 举报管理功能 (UC-010)

**第五阶段 (1周)**: 个人中心和设置

- [ ] 个人信息管理 (UC-012)
- [ ] 应用设置功能 (UC-011)
- [ ] 自动更新功能 (UC-002)

**第六阶段 (1周)**: 测试和优化

- [ ] 单元测试编写
- [ ] 集成测试
- [ ] 性能优化
- [ ] 安全测试

### 12.2 优先级排序

**高优先级** (核心业务功能):

1. 用户登录认证 (UC-001)
2. 设备注册 (UC-003a/b/c)
3. 设备调试 (UC-004)
4. 设备控制 (UC-006)

**中优先级** (重要管理功能): 5. 设备解绑 (UC-005) 6. 小区住户管理 (UC-007) 7. 小区员工管理 (UC-008) 8. 个人信息管理 (UC-012)

**低优先级** (辅助功能): 9. 邻里互助管理 (UC-009) 10. 举报管理 (UC-010) 11. 应用设置 (UC-011) 12. 自动更新 (UC-002)

### 12.3 技术风险评估

**高风险**:

- 设备通信协议的兼容性
- 大量设备的并发控制
- 复杂权限系统的实现

**中风险**:

- 二维码扫描在不同设备上的兼容性
- 图片/视频处理的性能问题
- 网络不稳定环境下的数据同步

**低风险**:

- UI组件的适配问题
- 基础功能的实现
- 常规的CRUD操作

## 13. 总结

本技术架构设计文档为攸家App V2.0提供了完整的技术方案，包括：

1. **清晰的目录结构**: 按功能模块和技术层次组织，便于开发和维护
2. **合理的技术选型**: 基于React Native + Expo的现代化技术栈
3. **完善的架构设计**: 分层架构确保代码的可维护性和可扩展性
4. **详细的开发规范**: 保证团队协作的一致性和代码质量
5. **可行的实施计划**: 按优先级分阶段实施，降低开发风险

该架构设计充分考虑了PRD中的所有功能需求，为项目的成功实施提供了坚实的技术基础。
