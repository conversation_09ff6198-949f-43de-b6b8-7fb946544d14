# 攸家 App 开发规范

## 1. 代码规范

### 1.1 命名规范

#### 文件命名

- **组件文件**: 使用 PascalCase，如 `DeviceCard.tsx`
- **Hook文件**: 使用 camelCase，以 `use` 开头，如 `useAuth.ts`
- **工具文件**: 使用 camelCase，如 `validation.ts`
- **类型文件**: 使用 camelCase，如 `device.types.ts`
- **常量文件**: 使用 camelCase，如 `constants.ts`

#### 变量和函数命名

```typescript
// ✅ 正确
const deviceList = [];
const isLoading = false;
const handleSubmit = () => {};
const getUserInfo = async () => {};

// ❌ 错误
const device_list = [];
const IsLoading = false;
const HandleSubmit = () => {};
const get_user_info = async () => {};
```

#### 常量命名

```typescript
// ✅ 正确
const API_BASE_URL = 'https://api.youjia.com';
const MAX_RETRY_COUNT = 3;
const DEVICE_TYPES = {
  DOOR_LOCK: 'door_lock',
  GATEWAY: 'gateway',
  CALLER: 'caller',
} as const;

// ❌ 错误
const apiBaseUrl = 'https://api.youjia.com';
const maxRetryCount = 3;
```

#### 组件命名

```typescript
// ✅ 正确
const DeviceCard: React.FC<DeviceCardProps> = ({ device }) => {
  return <View>...</View>;
};

// ❌ 错误
const deviceCard = ({ device }) => {
  return <View>...</View>;
};
```

### 1.2 TypeScript 规范

#### 接口定义

```typescript
// ✅ 正确 - 使用 interface 定义对象类型
interface Device {
  id: string;
  name: string;
  type: DeviceType;
  isOnline: boolean;
}

interface DeviceCardProps {
  device: Device;
  onPress?: (device: Device) => void;
}

// ✅ 正确 - 使用 type 定义联合类型
type DeviceType = 'door_lock' | 'gateway' | 'caller';
type LoadingState = 'idle' | 'loading' | 'success' | 'error';
```

#### 函数类型定义

```typescript
// ✅ 正确
const handleDeviceSelect = (device: Device): void => {
  // 处理逻辑
};

const fetchDevices = async (): Promise<Device[]> => {
  // 异步逻辑
  return [];
};

// Hook 类型定义
const useDevices = (): {
  devices: Device[];
  loading: boolean;
  error: string | null;
  refetch: () => void;
} => {
  // Hook 逻辑
};
```

#### 泛型使用

```typescript
// ✅ 正确
interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
}

const useApi = <T>(
  url: string
): {
  data: T | null;
  loading: boolean;
  error: string | null;
} => {
  // Hook 逻辑
};
```

### 1.3 组件规范

#### 函数组件结构

```typescript
import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { Device } from '@/types/device';

interface DeviceCardProps {
  device: Device;
  onPress?: (device: Device) => void;
  disabled?: boolean;
}

const DeviceCard: React.FC<DeviceCardProps> = ({
  device,
  onPress,
  disabled = false
}) => {
  // 1. Hooks
  const [isSelected, setIsSelected] = useState(false);

  // 2. 副作用
  useEffect(() => {
    // 副作用逻辑
  }, [device.id]);

  // 3. 事件处理函数
  const handlePress = () => {
    if (!disabled && onPress) {
      onPress(device);
    }
  };

  // 4. 渲染逻辑
  return (
    <TouchableOpacity onPress={handlePress} disabled={disabled}>
      <View>
        <Text>{device.name}</Text>
        <Text>{device.type}</Text>
      </View>
    </TouchableOpacity>
  );
};

export default DeviceCard;
```

#### Props 默认值

```typescript
// ✅ 正确 - 使用默认参数
const Button: React.FC<ButtonProps> = ({
  title,
  variant = 'primary',
  disabled = false,
  onPress,
}) => {
  // 组件逻辑
};

// ❌ 错误 - 使用 defaultProps
Button.defaultProps = {
  variant: 'primary',
  disabled: false,
};
```

### 1.4 Hook 规范

#### 自定义 Hook 结构

```typescript
import { useState, useEffect } from 'react';
import { deviceApi } from '@/services/api/device';

interface UseDevicesOptions {
  autoFetch?: boolean;
  filters?: DeviceFilters;
}

const useDevices = (options: UseDevicesOptions = {}) => {
  const { autoFetch = true, filters } = options;

  // 状态定义
  const [devices, setDevices] = useState<Device[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 业务逻辑
  const fetchDevices = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await deviceApi.getDevices(filters);
      setDevices(response.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取设备列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 副作用
  useEffect(() => {
    if (autoFetch) {
      fetchDevices();
    }
  }, [autoFetch, filters]);

  // 返回值
  return {
    devices,
    loading,
    error,
    refetch: fetchDevices,
  };
};

export default useDevices;
```

## 2. 文件组织规范

### 2.1 导入顺序

```typescript
// 1. React 相关
import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';

// 2. 第三方库
import { useQuery } from '@tanstack/react-query';
import { useNavigation } from '@react-navigation/native';

// 3. 内部模块 (按路径层级排序)
import { Device } from '@/types/device';
import { deviceApi } from '@/services/api/device';
import { useAuth } from '@/hooks/useAuth';
import Button from '@/components/ui/Button';

// 4. 相对路径导入
import './DeviceCard.styles';
```

### 2.2 导出规范

```typescript
// ✅ 正确 - 默认导出组件
const DeviceCard: React.FC<DeviceCardProps> = () => {
  // 组件逻辑
};

export default DeviceCard;

// ✅ 正确 - 命名导出类型和工具
export type { DeviceCardProps };
export { deviceUtils };

// ✅ 正确 - index.ts 文件中的重新导出
export { default as DeviceCard } from './DeviceCard';
export { default as DeviceList } from './DeviceList';
export type { DeviceCardProps, DeviceListProps } from './types';
```

### 2.3 文件夹结构规范

```
src/components/
├── ui/                    # 基础UI组件
│   ├── Button/
│   │   ├── Button.tsx
│   │   ├── Button.types.ts
│   │   ├── Button.styles.ts
│   │   └── index.ts
│   └── index.ts
├── business/              # 业务组件
│   ├── DeviceCard/
│   │   ├── DeviceCard.tsx
│   │   ├── DeviceCard.types.ts
│   │   └── index.ts
│   └── index.ts
└── index.ts
```

## 3. 样式规范

### 3.1 样式组织

```typescript
import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#ffffff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
  },
  // 状态相关样式
  disabled: {
    opacity: 0.5,
  },
  selected: {
    backgroundColor: '#e3f2fd',
    borderColor: '#2196f3',
  },
});

export default styles;
```

### 3.2 主题使用

```typescript
import { useTheme } from '@/hooks/useTheme';

const DeviceCard: React.FC<DeviceCardProps> = () => {
  const theme = useTheme();

  const styles = StyleSheet.create({
    container: {
      backgroundColor: theme.colors.surface,
      borderColor: theme.colors.border,
      borderRadius: theme.borderRadius.md,
    },
    text: {
      color: theme.colors.text.primary,
      fontSize: theme.typography.body.fontSize,
    },
  });

  return (
    <View style={styles.container}>
      <Text style={styles.text}>设备名称</Text>
    </View>
  );
};
```

## 4. 错误处理规范

### 4.1 错误边界

```typescript
import React from 'react';
import { View, Text } from 'react-native';

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

class ErrorBoundary extends React.Component<
  React.PropsWithChildren<{}>,
  ErrorBoundaryState
> {
  constructor(props: React.PropsWithChildren<{}>) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <View>
          <Text>出现了错误，请稍后重试</Text>
        </View>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
```

### 4.2 异步错误处理

```typescript
const useAsyncOperation = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const execute = async <T>(operation: () => Promise<T>): Promise<T | null> => {
    try {
      setLoading(true);
      setError(null);
      const result = await operation();
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '操作失败';
      setError(errorMessage);
      console.error('Async operation failed:', err);
      return null;
    } finally {
      setLoading(false);
    }
  };

  return { execute, loading, error };
};
```

## 5. 测试规范

### 5.1 组件测试

```typescript
import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import DeviceCard from '../DeviceCard';
import { mockDevice } from '@/test/mocks/device';

describe('DeviceCard', () => {
  it('should render device information correctly', () => {
    const { getByText } = render(
      <DeviceCard device={mockDevice} />
    );

    expect(getByText(mockDevice.name)).toBeTruthy();
    expect(getByText(mockDevice.type)).toBeTruthy();
  });

  it('should call onPress when pressed', () => {
    const onPress = jest.fn();
    const { getByTestId } = render(
      <DeviceCard
        device={mockDevice}
        onPress={onPress}
        testID="device-card"
      />
    );

    fireEvent.press(getByTestId('device-card'));
    expect(onPress).toHaveBeenCalledWith(mockDevice);
  });

  it('should not call onPress when disabled', () => {
    const onPress = jest.fn();
    const { getByTestId } = render(
      <DeviceCard
        device={mockDevice}
        onPress={onPress}
        disabled
        testID="device-card"
      />
    );

    fireEvent.press(getByTestId('device-card'));
    expect(onPress).not.toHaveBeenCalled();
  });
});
```

### 5.2 Hook 测试

```typescript
import { renderHook, act } from '@testing-library/react-hooks';
import { useDevices } from '../useDevices';
import { deviceApi } from '@/services/api/device';

jest.mock('@/services/api/device');

describe('useDevices', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should fetch devices on mount', async () => {
    const mockDevices = [mockDevice];
    (deviceApi.getDevices as jest.Mock).mockResolvedValue({
      data: mockDevices,
    });

    const { result, waitForNextUpdate } = renderHook(() =>
      useDevices({ autoFetch: true })
    );

    expect(result.current.loading).toBe(true);

    await waitForNextUpdate();

    expect(result.current.loading).toBe(false);
    expect(result.current.devices).toEqual(mockDevices);
    expect(result.current.error).toBeNull();
  });

  it('should handle fetch error', async () => {
    const errorMessage = 'Network error';
    (deviceApi.getDevices as jest.Mock).mockRejectedValue(
      new Error(errorMessage)
    );

    const { result, waitForNextUpdate } = renderHook(() =>
      useDevices({ autoFetch: true })
    );

    await waitForNextUpdate();

    expect(result.current.loading).toBe(false);
    expect(result.current.devices).toEqual([]);
    expect(result.current.error).toBe(errorMessage);
  });
});
```

## 6. 性能优化规范

### 6.1 组件优化

```typescript
import React, { memo, useMemo, useCallback } from 'react';

interface DeviceListProps {
  devices: Device[];
  onDeviceSelect: (device: Device) => void;
}

const DeviceList: React.FC<DeviceListProps> = memo(({
  devices,
  onDeviceSelect
}) => {
  // 使用 useMemo 缓存计算结果
  const sortedDevices = useMemo(() => {
    return devices.sort((a, b) => a.name.localeCompare(b.name));
  }, [devices]);

  // 使用 useCallback 缓存事件处理函数
  const handleDevicePress = useCallback((device: Device) => {
    onDeviceSelect(device);
  }, [onDeviceSelect]);

  return (
    <FlatList
      data={sortedDevices}
      keyExtractor={(item) => item.id}
      renderItem={({ item }) => (
        <DeviceCard
          device={item}
          onPress={handleDevicePress}
        />
      )}
      // 性能优化配置
      removeClippedSubviews
      maxToRenderPerBatch={10}
      windowSize={10}
      initialNumToRender={10}
    />
  );
});

DeviceList.displayName = 'DeviceList';

export default DeviceList;
```

### 6.2 图片优化

```typescript
import { Image } from 'expo-image';

const OptimizedImage: React.FC<ImageProps> = ({
  source,
  style,
  ...props
}) => {
  return (
    <Image
      source={source}
      style={style}
      // 性能优化配置
      cachePolicy="memory-disk"
      contentFit="cover"
      transition={200}
      {...props}
    />
  );
};
```

这个开发规范文档涵盖了代码规范、文件组织、样式规范、错误处理、测试规范和性能优化等方面，为团队提供了统一的开发标准。
