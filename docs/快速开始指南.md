# 攸家 App V2.0 快速开始指南

## 🚀 项目简介

攸家App是一个基于React Native + Expo的移动端门禁管理系统，面向小区管理人员提供设备管理、用户管理、社区内容审核等功能。

## 📋 前置要求

### 开发环境

- **Node.js**: 18.0.0 或更高版本
- **npm**: 9.0.0 或更高版本
- **Git**: 用于版本控制
- **VS Code**: 推荐的代码编辑器

### 移动开发环境

- **iOS**: Xcode 14+ (仅限 macOS)
- **Android**: Android Studio + Android SDK
- **Expo CLI**: 最新版本

### 验证环境

```bash
# 检查 Node.js 版本
node --version

# 检查 npm 版本
npm --version

# 检查 Expo CLI
npx expo --version

# 检查 Git
git --version
```

## 🛠️ 项目初始化

### 1. 克隆项目

```bash
# 克隆项目仓库
git clone <repository-url>
cd YouJia

# 或者如果是新项目
# npx create-expo-app YouJia --template blank-typescript
```

### 2. 运行初始化脚本

```bash
# 运行项目结构初始化脚本
npm run setup
```

这个脚本会自动创建：

- 完整的目录结构
- 基础配置文件
- 类型定义文件
- 示例代码文件

### 3. 安装依赖

```bash
# 安装项目依赖
npm install
```

### 4. 配置环境变量

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量文件
# 填入正确的 API 地址和其他配置
```

`.env` 文件示例：

```env
EXPO_PUBLIC_API_BASE_URL=https://api.youjia.com/v1
EXPO_PUBLIC_APP_NAME=攸家App
DEBUG=true
```

### 5. 启动开发服务器

```bash
# 启动 Expo 开发服务器
npm start

# 或者直接启动特定平台
npm run ios     # iOS 模拟器
npm run android # Android 模拟器
```

## 📱 在设备上运行

### iOS 设备

1. 在 App Store 下载 **Expo Go** 应用
2. 使用 Expo Go 扫描终端中显示的二维码
3. 应用将在设备上加载和运行

### Android 设备

1. 在 Google Play 下载 **Expo Go** 应用
2. 使用 Expo Go 扫描终端中显示的二维码
3. 应用将在设备上加载和运行

### 模拟器运行

```bash
# iOS 模拟器 (仅限 macOS)
npm run ios

# Android 模拟器
npm run android
```

## 🏗️ 项目结构概览

```
YouJia/
├── app/                    # Expo Router 路由目录
│   ├── (auth)/            # 认证相关页面
│   ├── (tabs)/            # 主要功能页面
│   └── _layout.tsx        # 根布局
├── src/                   # 源代码目录
│   ├── components/        # 组件库
│   ├── services/          # 服务层 (API, 存储等)
│   ├── stores/            # 状态管理
│   ├── hooks/             # 自定义 Hooks
│   ├── utils/             # 工具函数
│   ├── types/             # TypeScript 类型定义
│   └── config/            # 配置文件
├── assets/                # 静态资源
├── docs/                  # 项目文档
├── __tests__/             # 测试文件
└── scripts/               # 脚本文件
```

## 🔧 开发工具配置

### VS Code 配置

项目已包含推荐的 VS Code 配置：

1. **推荐插件** (`.vscode/extensions.json`):

   - TypeScript 支持
   - ESLint 代码检查
   - Prettier 代码格式化
   - Expo 工具支持

2. **工作区设置** (`.vscode/settings.json`):
   - 自动格式化
   - 保存时修复 ESLint 问题
   - TypeScript 路径映射

### Git Hooks 配置

```bash
# 安装 Git hooks
npx husky install

# 添加 pre-commit hook
npx husky add .husky/pre-commit "npx lint-staged"
```

## 📊 开发进度跟踪

项目提供了自动化的进度跟踪工具：

```bash
# 查看整体开发进度
npm run progress:report

# 查看下一步建议
npm run progress:next

# 标记任务完成
npm run progress complete <任务ID>
```

## 🧪 测试和质量检查

### 运行测试

```bash
# 运行所有测试
npm test

# 监听模式运行测试
npm run test:watch

# 生成测试覆盖率报告
npm run test:coverage
```

### 代码质量检查

```bash
# ESLint 检查
npm run lint

# 自动修复 ESLint 问题
npm run lint:fix

# TypeScript 类型检查
npm run type-check
```

## 🏗️ 构建和部署

### 开发构建

```bash
# 构建开发版本
npm run build:android -- --profile development
npm run build:ios -- --profile development
```

### 生产构建

```bash
# 构建生产版本
npm run build:android -- --profile production
npm run build:ios -- --profile production
```

### 发布到应用商店

```bash
# 提交到 Google Play
npm run submit:android

# 提交到 App Store
npm run submit:ios
```

## 📚 重要文档

- **[技术架构设计文档](./技术架构设计文档.md)** - 详细的技术架构说明
- **[开发规范](./开发规范.md)** - 代码规范和最佳实践
- **[开发实施计划](./开发实施计划.md)** - 详细的开发计划
- **[开发任务清单](./开发任务清单.md)** - 具体的任务清单

## 🚨 常见问题

### 1. Metro 缓存问题

```bash
# 清除 Metro 缓存
npx expo start --clear
```

### 2. 依赖安装问题

```bash
# 清除 node_modules 重新安装
rm -rf node_modules package-lock.json
npm install
```

### 3. iOS 模拟器问题

```bash
# 重置 iOS 模拟器
xcrun simctl erase all
```

### 4. Android 构建问题

```bash
# 清除 Gradle 缓存
cd android && ./gradlew clean && cd ..
```

### 5. TypeScript 错误

```bash
# 重启 TypeScript 服务
# 在 VS Code 中: Cmd/Ctrl + Shift + P -> "TypeScript: Restart TS Server"
```

## 🎯 下一步

1. **阅读技术文档**: 熟悉项目架构和开发规范
2. **查看任务清单**: 了解具体的开发任务
3. **开始开发**: 按照开发实施计划开始编码
4. **跟踪进度**: 使用进度跟踪工具监控开发进度

## 📞 获取帮助

- **技术问题**: 查看项目文档或提交 Issue
- **开发规范**: 参考开发规范文档
- **进度跟踪**: 使用 `npm run progress` 命令

---

🎉 **恭喜！** 你已经成功设置了攸家App开发环境。现在可以开始愉快的编码之旅了！
