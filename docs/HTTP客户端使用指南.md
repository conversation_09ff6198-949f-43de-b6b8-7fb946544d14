# HTTP客户端使用指南

## 概述

攸家App的HTTP客户端基于Axios构建，提供了完整的请求/响应拦截器、错误处理、重试机制等功能。

## 快速开始

### 基本使用

```typescript
import { httpClient } from '@/services/http';

// GET请求
const response = await httpClient.get('/api/users');

// POST请求
const user = await httpClient.post('/api/users', {
  name: '张三',
  phone: '13800138000',
});

// PUT请求
const updatedUser = await httpClient.put('/api/users/1', {
  name: '李四',
});

// DELETE请求
await httpClient.delete('/api/users/1');
```

### 使用API服务基类

```typescript
import { BaseApiService } from '@/services/api';

class UserService extends BaseApiService {
  async getUsers() {
    return this.get<User[]>('/users');
  }

  async createUser(userData: CreateUserRequest) {
    return this.post<User>('/users', userData);
  }

  async uploadAvatar(file: FormData, onProgress?: (progress: number) => void) {
    return this.upload<{ url: string }>('/users/avatar', file, onProgress);
  }
}

export const userService = new UserService();
```

## 功能特性

### 1. 自动请求拦截

- **请求ID追踪**: 每个请求自动添加唯一ID用于日志追踪
- **时间戳**: 自动添加请求时间戳
- **认证Token**: 自动添加Authorization头
- **参数清理**: 自动移除undefined字段

### 2. 智能响应处理

- **统一错误处理**: 自动将Axios错误转换为标准化的HttpError
- **Token自动刷新**: 401错误时自动尝试刷新Token
- **重试机制**: 网络错误和5xx错误自动重试
- **响应格式化**: 统一处理API响应格式

### 3. 错误类型系统

```typescript
import { HttpError, HttpErrorType } from '@/services/http';

try {
  await httpClient.get('/api/data');
} catch (error) {
  if (error instanceof HttpError) {
    switch (error.type) {
      case HttpErrorType.AUTH_ERROR:
        // 处理认证错误
        break;
      case HttpErrorType.NETWORK_ERROR:
        // 处理网络错误
        break;
      case HttpErrorType.SERVER_ERROR:
        // 处理服务器错误
        break;
    }
  }
}
```

## 配置选项

### 创建自定义客户端

```typescript
import { HttpClient } from '@/services/http';

const customClient = new HttpClient({
  baseURL: 'https://api.custom.com',
  timeout: 15000,
  headers: {
    'X-Custom-Header': 'value',
  },
  requestInterceptor: {
    addRequestId: true,
    addTimestamp: true,
    logRequests: true,
  },
  responseInterceptor: {
    enableRetry: true,
    maxRetryCount: 3,
    retryDelay: 1000,
    logResponses: true,
  },
});
```

### 拦截器配置

```typescript
import {
  setupRequestInterceptor,
  setupResponseInterceptor,
} from '@/services/http';

// 自定义请求拦截器
setupRequestInterceptor(axiosInstance, {
  addRequestId: false, // 不添加请求ID
  addTimestamp: true, // 添加时间戳
  logRequests: false, // 不记录请求日志
});

// 自定义响应拦截器
setupResponseInterceptor(axiosInstance, {
  enableRetry: true, // 启用重试
  maxRetryCount: 5, // 最大重试5次
  retryDelay: 2000, // 重试延迟2秒
  logResponses: true, // 记录响应日志
});
```

## Token管理

### 设置认证Token

```typescript
import { httpClient } from '@/services/http';

// 设置Token
httpClient.setAuthToken('your-jwt-token');

// 清除Token
httpClient.clearAuthToken();

// 设置自定义请求头
httpClient.setDefaultHeader('X-API-Key', 'your-api-key');

// 移除请求头
httpClient.removeDefaultHeader('X-API-Key');
```

### 自动Token刷新

Token刷新逻辑在拦截器中自动处理，当收到401错误时会尝试刷新Token并重新发送原请求。

## 错误处理最佳实践

### 1. 使用try-catch处理错误

```typescript
import { HttpError, HttpErrorType, formatErrorMessage } from '@/services/http';

async function fetchUserData() {
  try {
    const users = await httpClient.get('/api/users');
    return users.data;
  } catch (error) {
    if (error instanceof HttpError) {
      // 显示用户友好的错误信息
      console.error(formatErrorMessage(error));

      // 根据错误类型进行不同处理
      if (error.type === HttpErrorType.AUTH_ERROR) {
        // 跳转到登录页
        router.push('/login');
      }
    }
    throw error;
  }
}
```

### 2. 全局错误处理

```typescript
// 在应用启动时设置全局错误处理
import { httpClient } from '@/services/http';

httpClient.getInstance().interceptors.response.use(
  response => response,
  error => {
    // 全局错误处理逻辑
    if (error instanceof HttpError) {
      // 显示Toast提示
      showToast(formatErrorMessage(error));
    }
    return Promise.reject(error);
  }
);
```

## 文件上传下载

### 文件上传

```typescript
import { BaseApiService } from '@/services/api';

class FileService extends BaseApiService {
  async uploadFile(file: File, onProgress?: (progress: number) => void) {
    const formData = new FormData();
    formData.append('file', file);

    return this.upload<{ url: string }>('/upload', formData, onProgress);
  }
}
```

### 文件下载

```typescript
class FileService extends BaseApiService {
  async downloadFile(fileId: string, onProgress?: (progress: number) => void) {
    const blob = await this.download(`/files/${fileId}`, {}, onProgress);

    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'filename.pdf';
    a.click();
    window.URL.revokeObjectURL(url);
  }
}
```

## 调试和日志

### 开发环境日志

在开发环境下，HTTP客户端会自动记录详细的请求和响应日志：

```
🚀 HTTP Request: {
  method: 'POST',
  url: '/api/users',
  headers: { ... },
  data: { ... }
}

✅ HTTP Response: {
  status: 200,
  url: '/api/users',
  data: { ... }
}
```

### 生产环境

生产环境下日志会被自动关闭，只保留错误日志。

## 性能优化

### 1. 请求去重

```typescript
// 使用React Query进行请求去重和缓存
import { useQuery } from '@tanstack/react-query';

function useUsers() {
  return useQuery({
    queryKey: ['users'],
    queryFn: () => httpClient.get('/api/users'),
    staleTime: 5 * 60 * 1000, // 5分钟内不重新请求
  });
}
```

### 2. 请求取消

```typescript
import { httpClient } from '@/services/http';

const controller = new AbortController();

try {
  const response = await httpClient.get('/api/data', {
    signal: controller.signal,
  });
} catch (error) {
  if (error.name === 'AbortError') {
    console.log('请求已取消');
  }
}

// 取消请求
controller.abort();
```

## 常见问题

### Q: 如何处理大文件上传？

A: 使用分片上传，将大文件分成多个小块分别上传：

```typescript
async function uploadLargeFile(file: File) {
  const chunkSize = 1024 * 1024; // 1MB
  const chunks = Math.ceil(file.size / chunkSize);

  for (let i = 0; i < chunks; i++) {
    const start = i * chunkSize;
    const end = Math.min(start + chunkSize, file.size);
    const chunk = file.slice(start, end);

    const formData = new FormData();
    formData.append('chunk', chunk);
    formData.append('chunkIndex', i.toString());
    formData.append('totalChunks', chunks.toString());

    await httpClient.post('/upload/chunk', formData);
  }
}
```

### Q: 如何实现请求缓存？

A: 建议使用React Query或SWR等数据获取库来实现请求缓存和状态管理。

### Q: 如何处理并发请求限制？

A: 可以使用p-limit库来限制并发请求数量：

```typescript
import pLimit from 'p-limit';

const limit = pLimit(3); // 最多3个并发请求

const requests = urls.map(url => limit(() => httpClient.get(url)));

const results = await Promise.all(requests);
```
