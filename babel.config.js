module.exports = function (api) {
  api.cache(true);

  return {
    presets: ['babel-preset-expo'],
    plugins: [
      [
        'module-resolver',
        {
          root: ['./src'],
          alias: {
            '@': './src',
          },
          extensions: ['.ios.js', '.android.js', '.js', '.ts', '.tsx', '.json'],
        },
      ],
      // 优化编译性能
      [
        '@babel/plugin-transform-runtime',
        {
          helpers: true,
          regenerator: false,
        },
      ],
      // Web端模块系统修复插件 - 始终包含以确保兼容性
      ['@babel/plugin-syntax-import-meta'],
      ['@babel/plugin-syntax-dynamic-import'],
    ],
  };
};
