#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/* eslint-env node */
/* eslint-disable no-undef */

/**
 * 攸家App TODO清单管理工具
 * 用于更新和跟踪TODO清单的完成状态
 */

const TODO_FILE_PATH = path.join(__dirname, '../docs/TODO清单.md');

/**
 * 解析TODO清单文件
 */
function parseTodoFile() {
  const TODO_FILE_PATH_2 = path.join(__dirname, '../docs/TODO清单-后续阶段.md');

  if (!fs.existsSync(TODO_FILE_PATH)) {
    console.error('❌ TODO清单文件不存在:', TODO_FILE_PATH);
    process.exit(1);
  }

  let content = fs.readFileSync(TODO_FILE_PATH, 'utf8');

  // 如果后续阶段文件存在，合并内容
  if (fs.existsSync(TODO_FILE_PATH_2)) {
    const content2 = fs.readFileSync(TODO_FILE_PATH_2, 'utf8');
    content += '\n' + content2;
  }

  const lines = content.split('\n');

  const stats = {
    totalTasks: 0,
    completedTasks: 0,
    stages: {},
    tasks: [],
  };

  let currentStage = null;
  let currentTask = null;

  lines.forEach((line, index) => {
    // 匹配阶段标题
    const stageMatch = line.match(
      /^## (阶段[一二三四五六]：.*) \((第\d+-?\d*周)\)$/
    );
    if (stageMatch) {
      currentStage = {
        name: stageMatch[1],
        duration: stageMatch[2],
        totalTasks: 0,
        completedTasks: 0,
        tasks: [],
      };
      stats.stages[stageMatch[1]] = currentStage;
      return;
    }

    // 匹配任务标题
    const taskMatch = line.match(
      /^### 📋 (任务\d+\.\d+：.*) \((\d+天) \| (\d+小时)\)$/
    );
    if (taskMatch) {
      currentTask = {
        name: taskMatch[1],
        duration: taskMatch[2],
        hours: taskMatch[3],
        totalSubtasks: 0,
        completedSubtasks: 0,
        subtasks: [],
      };
      if (currentStage) {
        currentStage.tasks.push(currentTask);
      }
      stats.tasks.push(currentTask);
      return;
    }

    // 匹配任务项
    const todoMatch = line.match(/^- \[([ x])\] (.+)$/);
    if (todoMatch) {
      const isCompleted = todoMatch[1] === 'x';
      const taskText = todoMatch[2];

      const subtask = {
        text: taskText,
        completed: isCompleted,
        lineNumber: index + 1,
      };

      stats.totalTasks++;
      if (isCompleted) {
        stats.completedTasks++;
      }

      if (currentTask) {
        currentTask.totalSubtasks++;
        if (isCompleted) {
          currentTask.completedSubtasks++;
        }
        currentTask.subtasks.push(subtask);
      }

      if (currentStage) {
        currentStage.totalTasks++;
        if (isCompleted) {
          currentStage.completedTasks++;
        }
      }
    }
  });

  return { stats, content, lines };
}

/**
 * 生成进度条
 */
function generateProgressBar(completed, total, length = 30) {
  const percentage = total > 0 ? Math.round((completed / total) * 100) : 0;
  const filled = Math.round((percentage / 100) * length);
  const empty = length - filled;

  const filledChar = '█';
  const emptyChar = '░';

  return `[${filledChar.repeat(filled)}${emptyChar.repeat(empty)}] ${percentage}% (${completed}/${total})`;
}

/**
 * 更新TODO清单文件中的进度统计
 */
function updateProgressInFile() {
  const { stats, content } = parseTodoFile();

  let updatedContent = content;

  // 更新总体进度
  const totalProgressRegex = /总体进度: \[.*?\] \d+% \(\d+\/\d+\)/;
  const totalProgressBar = generateProgressBar(
    stats.completedTasks,
    stats.totalTasks,
    30
  );
  updatedContent = updatedContent.replace(
    totalProgressRegex,
    `总体进度: ${totalProgressBar}`
  );

  // 更新已完成任务数
  const completedRegex = /\*\*已完成\*\*: \d+个/;
  updatedContent = updatedContent.replace(
    completedRegex,
    `**已完成**: ${stats.completedTasks}个`
  );

  // 更新进度百分比
  const progressRegex = /\*\*进度\*\*: \d+%/;
  const overallProgress = Math.round(
    (stats.completedTasks / stats.totalTasks) * 100
  );
  updatedContent = updatedContent.replace(
    progressRegex,
    `**进度**: ${overallProgress}%`
  );

  // 更新各阶段进度
  Object.entries(stats.stages).forEach(([stageName, stage]) => {
    const stageProgressRegex = new RegExp(
      `(\\*\\*阶段进度\\*\\*: )\\[.*?\\] \\d+% \\(\\d+\\/\\d+\\)`,
      'g'
    );
    const stageProgressBar = generateProgressBar(
      stage.completedTasks,
      stage.totalTasks,
      30
    );

    // 找到对应阶段的进度行并更新
    const stageLines = updatedContent.split('\n');
    let inTargetStage = false;

    for (let i = 0; i < stageLines.length; i++) {
      if (stageLines[i].includes(stageName)) {
        inTargetStage = true;
      } else if (inTargetStage && stageLines[i].match(/^## /)) {
        inTargetStage = false;
      }

      if (inTargetStage && stageLines[i].includes('**阶段进度**:')) {
        stageLines[i] = stageLines[i].replace(
          stageProgressRegex,
          `$1${stageProgressBar}`
        );
        break;
      }
    }

    updatedContent = stageLines.join('\n');
  });

  // 写入更新后的内容
  fs.writeFileSync(TODO_FILE_PATH, updatedContent, 'utf8');

  return stats;
}

/**
 * 显示TODO清单统计信息
 */
function showStats() {
  const { stats } = parseTodoFile();

  console.log('📊 攸家App TODO清单统计\n');
  console.log('='.repeat(50));

  const overallProgress = Math.round(
    (stats.completedTasks / stats.totalTasks) * 100
  );
  console.log(
    `📈 总体进度: ${overallProgress}% (${stats.completedTasks}/${stats.totalTasks})`
  );
  console.log(
    `${generateProgressBar(stats.completedTasks, stats.totalTasks, 40)}\n`
  );

  console.log('📋 各阶段进度:');
  console.log('-'.repeat(50));

  Object.entries(stats.stages).forEach(([stageName, stage]) => {
    const stageProgress = Math.round(
      (stage.completedTasks / stage.totalTasks) * 100
    );
    console.log(`\n🎯 ${stageName}`);
    console.log(
      `   进度: ${stageProgress}% (${stage.completedTasks}/${stage.totalTasks})`
    );
    console.log(
      `   ${generateProgressBar(stage.completedTasks, stage.totalTasks, 30)}`
    );

    // 显示任务详情
    stage.tasks.forEach((task) => {
      const taskProgress = Math.round(
        (task.completedSubtasks / task.totalSubtasks) * 100
      );
      const status =
        taskProgress === 100 ? '✅' : taskProgress > 0 ? '🔄' : '⏳';
      console.log(
        `   ${status} ${task.name}: ${taskProgress}% (${task.completedSubtasks}/${task.totalSubtasks})`
      );
    });
  });

  console.log('\n' + '='.repeat(50));

  // 显示下一步建议
  showNextTasks(stats);
}

/**
 * 显示下一步建议
 */
function showNextTasks(stats) {
  console.log('\n🎯 下一步建议:');

  // 找到第一个未完成的任务
  for (const task of stats.tasks) {
    if (task.completedSubtasks < task.totalSubtasks) {
      console.log(`📌 优先完成: ${task.name}`);
      console.log(`   预估工时: ${task.duration} (${task.hours}小时)`);
      console.log(
        `   完成进度: ${task.completedSubtasks}/${task.totalSubtasks}`
      );

      // 显示未完成的子任务
      const incompleteTasks = task.subtasks.filter(
        (subtask) => !subtask.completed
      );
      if (incompleteTasks.length > 0) {
        console.log('   待完成子任务:');
        incompleteTasks.slice(0, 3).forEach((subtask) => {
          console.log(`   - ${subtask.text}`);
        });
        if (incompleteTasks.length > 3) {
          console.log(`   - ... 还有 ${incompleteTasks.length - 3} 个子任务`);
        }
      }
      return;
    }
  }

  console.log('🎉 所有任务已完成！');
}

/**
 * 标记任务完成
 */
function markTaskCompleted(taskPattern) {
  const { content } = parseTodoFile();
  const lines = content.split('\n');

  let found = false;
  let updatedLines = lines.map((line) => {
    if (line.includes(taskPattern) && line.match(/^- \[ \]/)) {
      found = true;
      return line.replace('- [ ]', '- [x]');
    }
    return line;
  });

  if (found) {
    fs.writeFileSync(TODO_FILE_PATH, updatedLines.join('\n'), 'utf8');
    console.log(`✅ 已标记任务完成: ${taskPattern}`);

    // 更新进度统计
    updateProgressInFile();
  } else {
    console.log(`❌ 未找到匹配的任务: ${taskPattern}`);
  }
}

/**
 * 标记任务未完成
 */
function markTaskIncomplete(taskPattern) {
  const { content } = parseTodoFile();
  const lines = content.split('\n');

  let found = false;
  let updatedLines = lines.map((line) => {
    if (line.includes(taskPattern) && line.match(/^- \[x\]/)) {
      found = true;
      return line.replace('- [x]', '- [ ]');
    }
    return line;
  });

  if (found) {
    fs.writeFileSync(TODO_FILE_PATH, updatedLines.join('\n'), 'utf8');
    console.log(`⏳ 已标记任务未完成: ${taskPattern}`);

    // 更新进度统计
    updateProgressInFile();
  } else {
    console.log(`❌ 未找到匹配的任务: ${taskPattern}`);
  }
}

/**
 * 主函数
 */
function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  switch (command) {
    case 'stats':
    case 's':
      showStats();
      break;

    case 'update':
    case 'u':
      console.log('🔄 更新TODO清单进度统计...');
      const stats = updateProgressInFile();
      console.log(
        `✅ 进度统计已更新: ${stats.completedTasks}/${stats.totalTasks} 任务完成`
      );
      break;

    case 'complete':
    case 'c':
      const taskToComplete = args.slice(1).join(' ');
      if (taskToComplete) {
        markTaskCompleted(taskToComplete);
      } else {
        console.log(
          '请指定要标记完成的任务，例如: npm run todo complete "1.1.1 验证开发环境"'
        );
      }
      break;

    case 'incomplete':
    case 'i':
      const taskToIncomplete = args.slice(1).join(' ');
      if (taskToIncomplete) {
        markTaskIncomplete(taskToIncomplete);
      } else {
        console.log(
          '请指定要标记未完成的任务，例如: npm run todo incomplete "1.1.1 验证开发环境"'
        );
      }
      break;

    default:
      console.log('攸家App TODO清单管理工具\n');
      console.log('使用方法:');
      console.log('  npm run todo stats     # 显示统计信息');
      console.log('  npm run todo update    # 更新进度统计');
      console.log('  npm run todo complete <任务>  # 标记任务完成');
      console.log('  npm run todo incomplete <任务> # 标记任务未完成');
      console.log('\n示例:');
      console.log('  npm run todo stats');
      console.log('  npm run todo complete "1.1.1 验证开发环境"');
      console.log('  npm run todo update');
  }
}

// 执行主函数
if (require.main === module) {
  main();
}

module.exports = {
  parseTodoFile,
  updateProgressInFile,
  showStats,
  markTaskCompleted,
  markTaskIncomplete,
};
