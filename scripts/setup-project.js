#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * 攸家App项目结构初始化脚本
 * 根据技术架构文档创建完整的目录结构
 */

// 项目目录结构定义
const directories = [
  // 源代码目录
  'src',
  'src/components',
  'src/components/ui',
  'src/components/business',
  'src/components/layout',

  // 服务层
  'src/services',
  'src/services/api',
  'src/services/http',
  'src/services/storage',

  // 状态管理
  'src/stores',

  // 自定义Hooks
  'src/hooks',

  // 工具函数
  'src/utils',

  // 类型定义
  'src/types',

  // 配置文件
  'src/config',

  // 路由目录 (Expo Router)
  'app/(auth)',
  'app/(tabs)',
  'app/(tabs)/device-register',
  'app/(tabs)/device-debug',
  'app/(tabs)/device-unbind',
  'app/(tabs)/device-control',
  'app/(tabs)/residents',
  'app/(tabs)/staff',
  'app/(tabs)/community',
  'app/(tabs)/community/mutual-aid',
  'app/(tabs)/community/reports',
  'app/(tabs)/profile',

  // 资源目录
  'assets/images/icons',
  'assets/images/logos',
  'assets/images/placeholders',
  'assets/data',

  // 测试目录
  '__tests__',
  '__tests__/components',
  '__tests__/services',
  '__tests__/utils',
  '__tests__/hooks',
  '__tests__/mocks',

  // 文档目录
  'docs',
];

// 需要创建的文件及其内容
const files = {
  // 基础配置文件
  '.env.example': `# 环境配置示例
API_BASE_URL=https://api.youjia.com/v1
APP_NAME=攸家App
APP_VERSION=2.0.0

# 调试配置
DEBUG=false
LOG_LEVEL=info

# 第三方服务配置
# SENTRY_DSN=your_sentry_dsn_here
# ANALYTICS_KEY=your_analytics_key_here
`,

  '.eslintrc.js': `module.exports = {
  extends: ['expo', '@react-native-community'],
  parser: '@typescript-eslint/parser',
  plugins: ['@typescript-eslint'],
  rules: {
    '@typescript-eslint/no-unused-vars': 'error',
    '@typescript-eslint/explicit-function-return-type': 'off',
    '@typescript-eslint/explicit-module-boundary-types': 'off',
    'react-hooks/exhaustive-deps': 'warn',
    'react-native/no-inline-styles': 'warn',
    'react-native/no-color-literals': 'warn',
  },
  overrides: [
    {
      files: ['**/__tests__/**/*', '**/*.test.*'],
      env: {
        jest: true,
      },
    },
  ],
};
`,

  '.prettierrc': `{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2,
  "useTabs": false
}
`,

  'metro.config.js': `const { getDefaultConfig } = require('expo/metro-config');

const config = getDefaultConfig(__dirname);

// 添加路径别名支持
config.resolver.alias = {
  '@': './src',
};

module.exports = config;
`,

  // TypeScript配置
  'tsconfig.json': `{
  "extends": "expo/tsconfig.base",
  "compilerOptions": {
    "strict": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@/components/*": ["./src/components/*"],
      "@/services/*": ["./src/services/*"],
      "@/hooks/*": ["./src/hooks/*"],
      "@/utils/*": ["./src/utils/*"],
      "@/types/*": ["./src/types/*"],
      "@/config/*": ["./src/config/*"],
      "@/stores/*": ["./src/stores/*"]
    }
  },
  "include": [
    "**/*.ts",
    "**/*.tsx",
    ".expo/types/**/*.ts",
    "expo-env.d.ts"
  ]
}
`,

  // 基础类型定义
  'src/types/index.ts': `export * from './auth';
export * from './device';
export * from './user';
export * from './community';
export * from './api';
`,

  'src/types/auth.ts': `export interface LoginRequest {
  phone: string;
  code: string;
}

export interface LoginResponse {
  token: string;
  refreshToken: string;
  user: User;
}

export interface User {
  id: string;
  name: string;
  phone: string;
  avatar?: string;
  nickname?: string;
  role: UserRole;
  permissions: Permission[];
  createdAt: string;
  updatedAt: string;
}

export enum UserRole {
  INSTALLER = 'installer',
  MAINTAINER = 'maintainer',
  ADMIN = 'admin',
  AUDITOR = 'auditor'
}

export enum Permission {
  DEVICE_REGISTER = 'device:register',
  DEVICE_DEBUG = 'device:debug',
  DEVICE_UNBIND = 'device:unbind',
  DEVICE_CONTROL = 'device:control',
  USER_VIEW = 'user:view',
  COMMUNITY_AUDIT = 'community:audit',
  REPORT_HANDLE = 'report:handle'
}
`,

  'src/types/device.ts': `export interface Device {
  id: string;
  deviceNumber: string;
  deviceType: DeviceType;
  status: DeviceStatus;
  location: Location;
  version: string;
  isOnline: boolean;
  credentials: DeviceCredentials;
  createdAt: string;
  updatedAt: string;
}

export enum DeviceType {
  DOOR_LOCK = 'door_lock',
  GATEWAY = 'gateway',
  CALLER = 'caller'
}

export enum DeviceStatus {
  NORMAL = 'normal',
  OFFLINE = 'offline',
  FAULT = 'fault',
  MAINTENANCE = 'maintenance'
}

export interface Location {
  community: string;
  building?: string;
  unit?: string;
  floor?: string;
  room?: string;
}

export interface DeviceCredentials {
  passwordCount: number;
  nfcCount: number;
  faceCount: number;
  serverPasswordCount: number;
  serverNfcCount: number;
  serverFaceCount: number;
}
`,

  'src/types/api.ts': `export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
  timestamp: number;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  hasMore: boolean;
}

export interface ApiError {
  code: number;
  message: string;
  details?: any;
}
`,

  // 配置文件
  'src/config/env.ts': `import Constants from 'expo-constants';

export const ENV = {
  API_BASE_URL: process.env.EXPO_PUBLIC_API_BASE_URL || 'https://api.youjia.com/v1',
  APP_NAME: process.env.EXPO_PUBLIC_APP_NAME || '攸家App',
  APP_VERSION: Constants.expoConfig?.version || '2.0.0',
  DEBUG: process.env.NODE_ENV === 'development',
};
`,

  'src/config/theme.ts': `export const theme = {
  colors: {
    primary: '#FF6B35',
    secondary: '#2196F3',
    success: '#4CAF50',
    warning: '#FF9800',
    error: '#F44336',
    info: '#2196F3',
    
    background: '#FFFFFF',
    surface: '#F5F5F5',
    
    text: {
      primary: '#212121',
      secondary: '#757575',
      disabled: '#BDBDBD',
    },
    
    border: '#E0E0E0',
    divider: '#EEEEEE',
  },
  
  typography: {
    h1: { fontSize: 24, fontWeight: '700' },
    h2: { fontSize: 20, fontWeight: '600' },
    h3: { fontSize: 18, fontWeight: '600' },
    body: { fontSize: 16, fontWeight: '400' },
    caption: { fontSize: 14, fontWeight: '400' },
    small: { fontSize: 12, fontWeight: '400' },
  },
  
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
  },
  
  borderRadius: {
    sm: 4,
    md: 8,
    lg: 12,
    xl: 16,
  },
};

export type Theme = typeof theme;
`,

  // 基础组件
  'src/components/ui/index.ts': `export { default as Button } from './Button';
export { default as Input } from './Input';
export { default as Modal } from './Modal';
export { default as Loading } from './Loading';
export { default as Toast } from './Toast';
`,

  // 工具函数
  'src/utils/index.ts': `export * from './validation';
export * from './format';
export * from './constants';
export * from './permissions';
export * from './device';
`,

  'src/utils/constants.ts': `export const DEVICE_TYPES = {
  DOOR_LOCK: 'door_lock',
  GATEWAY: 'gateway',
  CALLER: 'caller',
} as const;

export const USER_ROLES = {
  INSTALLER: 'installer',
  MAINTAINER: 'maintainer',
  ADMIN: 'admin',
  AUDITOR: 'auditor',
} as const;

export const PERMISSIONS = {
  DEVICE_REGISTER: 'device:register',
  DEVICE_DEBUG: 'device:debug',
  DEVICE_UNBIND: 'device:unbind',
  DEVICE_CONTROL: 'device:control',
  USER_VIEW: 'user:view',
  COMMUNITY_AUDIT: 'community:audit',
  REPORT_HANDLE: 'report:handle',
} as const;
`,

  // 测试配置
  '__tests__/setup.ts': `import 'react-native-gesture-handler/jestSetup';

// Mock Expo modules
jest.mock('expo-constants', () => ({
  expoConfig: {
    version: '2.0.0',
  },
}));

jest.mock('expo-secure-store', () => ({
  getItemAsync: jest.fn(),
  setItemAsync: jest.fn(),
  deleteItemAsync: jest.fn(),
}));

// Mock React Navigation
jest.mock('@react-navigation/native', () => ({
  useNavigation: () => ({
    navigate: jest.fn(),
    goBack: jest.fn(),
  }),
  useRoute: () => ({
    params: {},
  }),
}));

// Global test utilities
global.console = {
  ...console,
  warn: jest.fn(),
  error: jest.fn(),
};
`,

  // README更新
  'README.md': `# 攸家 App V2.0

基于 React Native + Expo 的门禁管理系统移动端应用。

## 功能特性

- 🔐 手机号验证码登录
- 📱 设备注册、调试、解绑、控制
- 👥 小区住户和员工管理
- 🏘️ 邻里互助内容审核
- 📋 举报管理和处理
- ⚙️ 个人信息管理和设置

## 技术栈

- **框架**: React Native 0.79.2 + Expo SDK 53
- **语言**: TypeScript 5.8.3
- **路由**: Expo Router
- **状态管理**: Zustand + React Query
- **UI组件**: NativeBase
- **网络请求**: Axios

## 开发环境

\`\`\`bash
# 安装依赖
npm install

# 启动开发服务器
npm start

# iOS 开发
npm run ios

# Android 开发
npm run android
\`\`\`

## 项目结构

详见 [技术架构设计文档](./docs/技术架构设计文档.md)

## 开发规范

详见 [开发规范文档](./docs/开发规范.md)

## 许可证

MIT License
`,
};

/**
 * 创建目录
 */
function createDirectories() {
  console.log('🏗️  创建项目目录结构...');

  directories.forEach((dir) => {
    const dirPath = path.join(process.cwd(), dir);
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
      console.log(`✅ 创建目录: ${dir}`);
    } else {
      console.log(`⏭️  目录已存在: ${dir}`);
    }
  });
}

/**
 * 创建文件
 */
function createFiles() {
  console.log('\n📄 创建配置文件...');

  Object.entries(files).forEach(([filePath, content]) => {
    const fullPath = path.join(process.cwd(), filePath);
    const dir = path.dirname(fullPath);

    // 确保目录存在
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    // 创建文件（如果不存在）
    if (!fs.existsSync(fullPath)) {
      fs.writeFileSync(fullPath, content, 'utf8');
      console.log(`✅ 创建文件: ${filePath}`);
    } else {
      console.log(`⏭️  文件已存在: ${filePath}`);
    }
  });
}

/**
 * 主函数
 */
function main() {
  console.log('🚀 开始初始化攸家App项目结构...\n');

  try {
    createDirectories();
    createFiles();

    console.log('\n🎉 项目结构初始化完成！');
    console.log('\n📋 下一步操作:');
    console.log('1. 安装项目依赖: npm install');
    console.log('2. 配置环境变量: 复制 .env.example 为 .env 并填写配置');
    console.log('3. 启动开发服务器: npm start');
    console.log('4. 查看技术文档: docs/技术架构设计文档.md');
    console.log('5. 查看开发规范: docs/开发规范.md');
  } catch (error) {
    console.error('❌ 初始化失败:', error.message);
    process.exit(1);
  }
}

// 执行主函数
if (require.main === module) {
  main();
}

module.exports = { createDirectories, createFiles };
