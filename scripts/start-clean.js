#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🧹 清理缓存和临时文件...');

// 要清理的目录和文件
const pathsToClean = [
  '.expo',
  '.metro-cache',
  'tmp',
  'node_modules/.cache',
  'node_modules/metro-cache',
];

// 清理指定路径
pathsToClean.forEach(cleanPath => {
  const fullPath = path.join(process.cwd(), cleanPath);
  if (fs.existsSync(fullPath)) {
    console.log(`删除: ${cleanPath}`);
    try {
      execSync(`rm -rf "${fullPath}"`, { stdio: 'inherit' });
    } catch (error) {
      console.warn(`警告: 无法删除 ${cleanPath}:`, error.message);
    }
  }
});

console.log('✅ 缓存清理完成');
console.log('🚀 启动开发服务器...');

// 启动Expo开发服务器
try {
  execSync('npx expo start --clear', { stdio: 'inherit' });
} catch (error) {
  console.error('❌ 启动失败:', error.message);
  process.exit(1);
}
