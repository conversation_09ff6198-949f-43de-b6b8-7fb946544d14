{"editor.codeActionsOnSave": {"source.fixAll": "explicit", "source.organizeImports": "explicit", "source.sortMembers": "explicit"}, "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "typescript.preferences.importModuleSpecifier": "relative", "typescript.suggest.autoImports": true, "typescript.updateImportsOnFileMove.enabled": "always", "emmet.includeLanguages": {"typescript": "typescriptreact", "javascript": "javascriptreact"}, "files.associations": {"*.tsx": "typescriptreact", "*.ts": "typescript"}, "search.exclude": {"**/node_modules": true, "**/dist": true, "**/.expo": true, "**/ios": true, "**/android": true}}