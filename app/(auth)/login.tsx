import { router } from 'expo-router';
import { useEffect, useState } from 'react';
import {
    Alert,
    KeyboardAvoidingView,
    Platform,
    ScrollView,
    StyleSheet,
    Text,
    View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { Button, Input } from '@/components/ui';
import { theme } from '@/config/theme';
import { useAuthStore } from '@/stores/auth';
import { UserStatus } from '@/types/user';

export default function LoginScreen() {
  const [phone, setPhone] = useState('');
  const [code, setCode] = useState('');
  const [countdown, setCountdown] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  // 自动更新相关状态
  const [updateModalVisible, setUpdateModalVisible] = useState(false);
  const [updateInfo, setUpdateInfo] = useState<{
    version: string;
    description: string;
    isForced: boolean;
    downloadProgress?: number;
    isDownloading?: boolean;
  } | null>(null);

  const { login, setLoading, setPermissions, setRoles } = useAuthStore();

  // 检查应用更新
  const checkForUpdates = async () => {
    try {
      // TODO: 调用真实的更新检查API
      // const response = await updateApi.checkUpdate();

      // 模拟更新检查
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 模拟有新版本的情况（30%概率）
      const hasUpdate = Math.random() < 0.3;

      if (hasUpdate) {
        const mockUpdateInfo = {
          version: 'V2.1.0',
          description: '• 优化登录体验\n• 修复已知问题\n• 提升性能表现',
          isForced: Math.random() < 0.2, // 20%概率为强制更新
        };

        setUpdateInfo(mockUpdateInfo);
        setUpdateModalVisible(true);
      }
    } catch (error) {
      console.log('检查更新失败:', error);
      // 更新检查失败不影响正常使用
    }
  };

  // 组件挂载时检查更新
  useEffect(() => {
    checkForUpdates();
  }, []);

  // 处理立即更新
  const handleUpdateNow = async () => {
    if (!updateInfo) return;

    try {
      setUpdateInfo(prev => prev ? { ...prev, isDownloading: true, downloadProgress: 0 } : null);

      // 模拟下载进度
      for (let i = 0; i <= 100; i += 10) {
        await new Promise(resolve => setTimeout(resolve, 200));
        setUpdateInfo(prev => prev ? { ...prev, downloadProgress: i } : null);
      }

      // 模拟安装
      Alert.alert(
        '下载完成',
        '应用将重启以完成更新',
        [
          {
            text: '确定',
            onPress: () => {
              setUpdateModalVisible(false);
              // TODO: 实际应用中这里会重启应用
              Alert.alert('提示', '更新完成！（模拟）');
            }
          }
        ]
      );
    } catch (error) {
      Alert.alert('错误', '下载失败，请检查网络或稍后重试');
      setUpdateInfo(prev => prev ? { ...prev, isDownloading: false } : null);
    }
  };

  // 处理稍后更新
  const handleUpdateLater = () => {
    if (updateInfo?.isForced) {
      Alert.alert('提示', '此为强制更新，必须更新后才能继续使用');
      return;
    }
    setUpdateModalVisible(false);
  };

  // 手机号验证
  const validatePhone = (phoneNumber: string) => {
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phoneNumber);
  };

  // 获取验证码
  const handleGetCode = async () => {
    if (!validatePhone(phone)) {
      Alert.alert('提示', '请输入正确的手机号');
      return;
    }

    try {
      setIsLoading(true);
      // TODO: 调用获取验证码API
      // await authApi.sendCode(phone);

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 开始倒计时
      setCountdown(60);
      const timer = setInterval(() => {
        setCountdown(prev => {
          if (prev <= 1) {
            clearInterval(timer);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      Alert.alert('成功', '验证码已发送');
    } catch {
      Alert.alert('错误', '发送验证码失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  // 登录
  const handleLogin = async () => {
    if (!validatePhone(phone)) {
      Alert.alert('提示', '请输入正确的手机号');
      return;
    }

    if (!code || code.length !== 4) {
      Alert.alert('提示', '请输入4位验证码');
      return;
    }

    try {
      setLoading(true);
      // TODO: 调用登录API
      // const response = await authApi.login(phone, code);

      // 模拟API调用和登录成功
      await new Promise(resolve => setTimeout(resolve, 1500));

      // 模拟用户数据
      const mockUser = {
        id: '1',
        username: phone,
        realName: '张三',
        phone: phone,
        avatar: '',
        status: UserStatus.NORMAL,
        roles: ['admin'],
        permissions: [
          'device:register',
          'device:debug',
          'device:unbind',
          'device:control',
          'residents:view',
          'staff:view',
          'community:manage'
        ],
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString(),
      };

      const mockToken = 'mock-jwt-token-' + Date.now();

      // 登录成功
      login(mockToken, mockUser);

      // 设置用户权限和角色
      setPermissions(mockUser.permissions);
      setRoles(mockUser.roles);

      // 跳转到主页，默认选中运维管理Tab
      router.replace('/(tabs)/operations' as any);
    } catch {
      Alert.alert('错误', '登录失败，请检查验证码');
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={styles.keyboardView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps='handled'
        >
          {/* Logo和标题 */}
          <View style={styles.header}>
            <View style={styles.logoContainer}>
              <Text style={styles.logoText}>攸家</Text>
            </View>
            <Text style={styles.title}>攸家设备管理系统</Text>
            <Text style={styles.subtitle}>智能门禁管理，让社区更安全</Text>
            <Text style={styles.loginHint}>请使用手机号和验证码登录</Text>
          </View>

          {/* 登录表单 */}
          <View style={styles.form}>
            <Input
              label='手机号'
              value={phone}
              onChangeText={setPhone}
              placeholder='请输入手机号'
              keyboardType='phone-pad'
              maxLength={11}
              required
            />

            <View style={styles.codeContainer}>
              <View style={styles.codeInput}>
                <Input
                  label='验证码'
                  value={code}
                  onChangeText={setCode}
                  placeholder='请输入验证码'
                  keyboardType='number-pad'
                  maxLength={4}
                  required
                />
              </View>
              <Button
                variant='outline'
                size='medium'
                onPress={handleGetCode}
                disabled={!validatePhone(phone) || countdown > 0 || isLoading}
                loading={isLoading}
                containerStyle={styles.codeButton}
              >
                {countdown > 0 ? `${countdown}s` : '获取验证码'}
              </Button>
            </View>

            <Button
              variant='primary'
              size='large'
              onPress={handleLogin}
              disabled={!validatePhone(phone) || !code}
              containerStyle={styles.loginButton}
            >
              登录
            </Button>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>

      {/* 更新提示模态框 */}
      {updateModalVisible && updateInfo && (
        <View style={styles.updateModalOverlay}>
          <View style={styles.updateModal}>
            <Text style={styles.updateTitle}>发现新版本</Text>
            <Text style={styles.updateVersion}>{updateInfo.version}</Text>
            <Text style={styles.updateDescription}>{updateInfo.description}</Text>

            {updateInfo.isDownloading && (
              <View style={styles.progressContainer}>
                <Text style={styles.progressText}>
                  下载中... {updateInfo.downloadProgress || 0}%
                </Text>
                <View style={styles.progressBar}>
                  <View
                    style={[
                      styles.progressFill,
                      { width: `${updateInfo.downloadProgress || 0}%` }
                    ]}
                  />
                </View>
              </View>
            )}

            {!updateInfo.isDownloading && (
              <View style={styles.updateButtons}>
                {!updateInfo.isForced && (
                  <Button
                    variant="outline"
                    size="medium"
                    onPress={handleUpdateLater}
                    containerStyle={styles.updateButton}
                  >
                    稍后升级
                  </Button>
                )}
                <Button
                  variant="primary"
                  size="medium"
                  onPress={handleUpdateNow}
                  containerStyle={styles.updateButton}
                >
                  立即更新
                </Button>
              </View>
            )}
          </View>
        </View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.surface,
  },
  keyboardView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: theme.spacing.lg,
  },
  header: {
    alignItems: 'center',
    marginBottom: theme.spacing.xl * 2,
  },
  logoContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: theme.colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: theme.spacing.lg,
    shadowColor: theme.colors.primary,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  logoText: {
    fontSize: 32,
    fontWeight: 'bold',
    color: theme.colors.white,
  },
  title: {
    fontSize: 26,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginBottom: theme.spacing.md,
  },
  loginHint: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    opacity: 0.8,
  },
  form: {
    gap: theme.spacing.lg,
    backgroundColor: theme.colors.white,
    padding: theme.spacing.lg,
    borderRadius: theme.borderRadius.lg,
    shadowColor: theme.colors.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  codeContainer: {
    flexDirection: 'row',
    gap: theme.spacing.md,
    alignItems: 'flex-end',
  },
  codeInput: {
    flex: 1,
  },
  codeButton: {
    minWidth: 110,
    marginBottom: 2,
  },
  loginButton: {
    marginTop: theme.spacing.lg,
  },
  // 更新模态框样式
  updateModalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  updateModal: {
    backgroundColor: theme.colors.white,
    margin: theme.spacing.lg,
    padding: theme.spacing.lg,
    borderRadius: theme.borderRadius.lg,
    shadowColor: theme.colors.black,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 8,
    minWidth: 280,
  },
  updateTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.text,
    textAlign: 'center',
    marginBottom: theme.spacing.sm,
  },
  updateVersion: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.primary,
    textAlign: 'center',
    marginBottom: theme.spacing.md,
  },
  updateDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    lineHeight: 20,
    marginBottom: theme.spacing.lg,
  },
  progressContainer: {
    marginBottom: theme.spacing.lg,
  },
  progressText: {
    fontSize: 14,
    color: theme.colors.text,
    textAlign: 'center',
    marginBottom: theme.spacing.sm,
  },
  progressBar: {
    height: 4,
    backgroundColor: theme.colors.surface,
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: theme.colors.primary,
    borderRadius: 2,
  },
  updateButtons: {
    flexDirection: 'row',
    gap: theme.spacing.md,
  },
  updateButton: {
    flex: 1,
  },
});
