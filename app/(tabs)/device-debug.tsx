import { Ionicons } from '@expo/vector-icons';
import React, { useState } from 'react';
import {
    Alert,
    ScrollView,
    StyleSheet,
    Text,
    View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { LocationPath, LocationPicker, QRScanner } from '@/components/business';
import { Button } from '@/components/ui';
import { theme } from '@/config/theme';
import { MockDevice, mockDevices, mockLocationData, mockUserDebugData } from '@/data/mockData';

interface DebugCredentials {
  password?: string;
  nfcCard?: string;
  faceImage?: string;
}

export default function DeviceDebugScreen() {
  const [selectedDevice, setSelectedDevice] = useState<MockDevice | null>(null);
  const [showQRScanner, setShowQRScanner] = useState(false);
  const [showLocationPicker, setShowLocationPicker] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [debugCredentials, setDebugCredentials] = useState<DebugCredentials>({});
  const [showPasswordInput, setShowPasswordInput] = useState(false);
  const [passwordInput, setPasswordInput] = useState('');

  // 处理二维码扫描结果
  const handleQRScanSuccess = (data: string) => {
    const device = mockDevices.find(d => d.deviceNumber === data);
    if (device) {
      setSelectedDevice(device);
      loadDebugCredentials(device.id);
    } else {
      Alert.alert('错误', '未找到设备信息，请确认设备已注册');
    }
    setShowQRScanner(false);
  };

  // 处理位置选择
  const handleLocationSelect = (path: LocationPath[]) => {
    const locationStr = path.map(item => item.name).join(' ');
    const device = mockDevices.find(d => d.location === locationStr);
    if (device) {
      setSelectedDevice(device);
      loadDebugCredentials(device.id);
    } else {
      Alert.alert('提示', '当前位置下没有绑定任何设备');
    }
    setShowLocationPicker(false);
  };

  // 加载调试凭证
  const loadDebugCredentials = (deviceId: string) => {
    const userDebugData = mockUserDebugData.find(d => d.deviceId === deviceId);
    if (userDebugData) {
      setDebugCredentials({
        password: userDebugData.password,
        nfcCard: userDebugData.nfcCard,
        faceImage: userDebugData.faceImage,
      });
    } else {
      setDebugCredentials({});
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* 头部 */}
        <View style={styles.header}>
          <Text style={styles.title}>设备调试</Text>
          <Text style={styles.subtitle}>扫码或选择位置来调试门禁设备</Text>
        </View>

        {/* 设备选择 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>选择设备</Text>
          <View style={styles.deviceSelectContainer}>
            <Button
              variant="outline"
              size="medium"
              onPress={() => setShowQRScanner(true)}
              containerStyle={styles.selectButton}
            >
              <Ionicons name="qr-code" size={20} color={theme.colors.primary} />
              <Text style={styles.selectButtonText}>扫描二维码</Text>
            </Button>
            <Button
              variant="outline"
              size="medium"
              onPress={() => setShowLocationPicker(true)}
              containerStyle={styles.selectButton}
            >
              <Ionicons name="location" size={20} color={theme.colors.primary} />
              <Text style={styles.selectButtonText}>选择位置</Text>
            </Button>
          </View>
        </View>

        {/* 设备信息 */}
        {selectedDevice && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>设备信息</Text>
            <View style={styles.deviceInfo}>
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>基础设施：</Text>
                <Text style={styles.infoValue}>{selectedDevice.location}</Text>
              </View>
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>设备编号：</Text>
                <Text style={styles.infoValue}>{selectedDevice.deviceNumber}</Text>
              </View>
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>状态：</Text>
                <View style={styles.statusContainer}>
                  <View style={[styles.statusDot, { backgroundColor: selectedDevice.status === 'online' ? '#4CAF50' : '#F44336' }]} />
                  <Text style={styles.statusText}>{selectedDevice.status === 'online' ? '在线' : '离线'}</Text>
                </View>
              </View>

              {/* 数据统计 */}
              <View style={styles.dataStats}>
                <Text style={styles.statsTitle}>数据统计</Text>
                <View style={styles.statsRow}>
                  <View style={styles.statsItem}>
                    <Text style={styles.statsLabel}>门禁实录</Text>
                    <Text style={styles.statsValue}>
                      人像 {selectedDevice.deviceData.faces} | 密码 {selectedDevice.deviceData.passwords} | NFC {selectedDevice.deviceData.nfc}
                    </Text>
                  </View>
                  <View style={styles.statsItem}>
                    <Text style={styles.statsLabel}>服务实录</Text>
                    <Text style={styles.statsValue}>
                      人像 {selectedDevice.serverData.faces} | 密码 {selectedDevice.serverData.passwords} | NFC {selectedDevice.serverData.nfc}
                    </Text>
                  </View>
                </View>
              </View>
            </View>
          </View>
        )}

        {/* 调试操作 */}
        {selectedDevice && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>调试操作</Text>
            <View style={styles.operationGrid}>
              <Button
                variant="primary"
                size="medium"
                onPress={() => Alert.alert('功能演示', '远程开门功能')}
                containerStyle={styles.operationButton}
              >
                <Ionicons name="lock-open" size={20} color={theme.colors.white} />
                <Text style={styles.operationButtonText}>远程开门</Text>
              </Button>

              <Button
                variant="outline"
                size="medium"
                onPress={() => Alert.alert('功能演示', '打开过渡模式')}
                containerStyle={styles.operationButton}
              >
                <Ionicons name="toggle" size={20} color={theme.colors.primary} />
                <Text style={styles.operationButtonTextOutline}>打开过渡</Text>
              </Button>

              <Button
                variant="outline"
                size="medium"
                onPress={() => Alert.alert('功能演示', '下发数据功能')}
                containerStyle={styles.operationButton}
              >
                <Ionicons name="sync" size={20} color={theme.colors.primary} />
                <Text style={styles.operationButtonTextOutline}>下发数据</Text>
              </Button>

              <Button
                variant="outline"
                size="medium"
                onPress={() => Alert.alert('功能演示', '设置密码功能')}
                containerStyle={styles.operationButton}
              >
                <Ionicons name="keypad" size={20} color={theme.colors.primary} />
                <Text style={styles.operationButtonTextOutline}>设置密码</Text>
              </Button>
            </View>
          </View>
        )}

        {/* 空状态 */}
        {!selectedDevice && (
          <View style={styles.emptyState}>
            <Ionicons name="build-outline" size={64} color={theme.colors.textSecondary} />
            <Text style={styles.emptyStateText}>请先选择要调试的设备</Text>
            <Text style={styles.emptyStateSubtext}>通过扫码或选择位置来选择设备</Text>
          </View>
        )}
      </ScrollView>

      {/* 二维码扫描器 */}
      <QRScanner
        visible={showQRScanner}
        onScanSuccess={handleQRScanSuccess}
        onClose={() => setShowQRScanner(false)}
        hint="请将设备二维码放入扫描框内"
      />

      {/* 位置选择器 */}
      <LocationPicker
        visible={showLocationPicker}
        title="选择设备位置"
        data={mockLocationData}
        onSelect={handleLocationSelect}
        onCancel={() => setShowLocationPicker(false)}
        maxLevel={3}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: theme.spacing.lg,
    backgroundColor: theme.colors.white,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: theme.spacing.xs,
  },
  subtitle: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  section: {
    backgroundColor: theme.colors.white,
    marginTop: theme.spacing.md,
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.lg,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: theme.spacing.lg,
  },
  deviceSelectContainer: {
    flexDirection: 'row',
    gap: theme.spacing.md,
  },
  selectButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.sm,
  },
  selectButtonText: {
    fontSize: 14,
    color: theme.colors.primary,
    fontWeight: '600',
  },
  deviceInfo: {
    gap: theme.spacing.md,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  infoLabel: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    minWidth: 80,
  },
  infoValue: {
    fontSize: 16,
    color: theme.colors.text,
    flex: 1,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.xs,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  statusText: {
    fontSize: 16,
    color: theme.colors.text,
  },
  dataStats: {
    marginTop: theme.spacing.md,
    padding: theme.spacing.md,
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.md,
  },
  statsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
  },
  statsRow: {
    gap: theme.spacing.sm,
  },
  statsItem: {
    gap: theme.spacing.xs,
  },
  statsLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  statsValue: {
    fontSize: 14,
    color: theme.colors.text,
  },
  operationGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: theme.spacing.md,
  },
  operationButton: {
    flex: 1,
    minWidth: '45%',
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.sm,
  },
  operationButtonText: {
    fontSize: 14,
    color: theme.colors.white,
    fontWeight: '600',
  },
  operationButtonTextOutline: {
    fontSize: 14,
    color: theme.colors.primary,
    fontWeight: '600',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: theme.spacing.xl * 2,
    gap: theme.spacing.md,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
  },
});
