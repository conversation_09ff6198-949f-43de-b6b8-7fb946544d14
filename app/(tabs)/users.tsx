import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React from 'react';
import {
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { theme } from '@/config/theme';
import { PERMISSIONS, usePermissions } from '@/utils/permissions';

interface UserModule {
  id: string;
  title: string;
  description: string;
  icon: keyof typeof Ionicons.glyphMap;
  route: string;
  permission: string;
  color: string;
  count?: number;
}

export default function UsersScreen() {
  const { hasPermission } = usePermissions();

  const userModules: UserModule[] = [
    {
      id: 'residents',
      title: '小区住户',
      description: '查看住户信息，管理门禁权限',
      icon: 'home',
      route: '/residents',
      permission: PERMISSIONS.RESIDENTS_VIEW,
      color: '#4CAF50',
      count: 156,
    },
    {
      id: 'staff',
      title: '小区员工',
      description: '查看员工信息，管理工作权限',
      icon: 'person',
      route: '/staff',
      permission: PERMISSIONS.STAFF_VIEW,
      color: '#2196F3',
      count: 23,
    },
  ];

  const handleModulePress = (module: UserModule) => {
    if (hasPermission(module.permission)) {
      router.push(module.route as any);
    }
  };

  const availableModules = userModules.filter(module => 
    hasPermission(module.permission)
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* 头部 */}
        <View style={styles.header}>
          <Text style={styles.title}>用户管理</Text>
          <Text style={styles.subtitle}>住户和员工信息管理</Text>
        </View>

        {/* 功能模块 */}
        <View style={styles.modulesContainer}>
          {availableModules.map((module) => (
            <TouchableOpacity
              key={module.id}
              style={styles.moduleCard}
              onPress={() => handleModulePress(module)}
              activeOpacity={0.7}
            >
              <View style={[styles.iconContainer, { backgroundColor: module.color }]}>
                <Ionicons
                  name={module.icon}
                  size={32}
                  color={theme.colors.white}
                />
              </View>
              <View style={styles.moduleContent}>
                <View style={styles.moduleHeader}>
                  <Text style={styles.moduleTitle}>{module.title}</Text>
                  {module.count && (
                    <View style={styles.countBadge}>
                      <Text style={styles.countText}>{module.count}</Text>
                    </View>
                  )}
                </View>
                <Text style={styles.moduleDescription}>{module.description}</Text>
              </View>
              <Ionicons
                name="chevron-forward"
                size={20}
                color={theme.colors.textSecondary}
              />
            </TouchableOpacity>
          ))}
        </View>

        {/* 快捷操作 */}
        <View style={styles.quickActionsContainer}>
          <Text style={styles.sectionTitle}>快捷操作</Text>
          <View style={styles.quickActionsGrid}>
            {hasPermission(PERMISSIONS.RESIDENTS_VIEW) && (
              <TouchableOpacity
                style={styles.quickActionCard}
                onPress={() => router.push('/residents')}
              >
                <Ionicons name="search" size={24} color={theme.colors.primary} />
                <Text style={styles.quickActionText}>搜索住户</Text>
              </TouchableOpacity>
            )}
            
            {hasPermission(PERMISSIONS.RESIDENTS_VIEW) && (
              <TouchableOpacity
                style={styles.quickActionCard}
                onPress={() => router.push('/residents')}
              >
                <Ionicons name="sync" size={24} color={theme.colors.primary} />
                <Text style={styles.quickActionText}>数据下发</Text>
              </TouchableOpacity>
            )}
            
            {hasPermission(PERMISSIONS.STAFF_VIEW) && (
              <TouchableOpacity
                style={styles.quickActionCard}
                onPress={() => router.push('/staff')}
              >
                <Ionicons name="people" size={24} color={theme.colors.primary} />
                <Text style={styles.quickActionText}>员工管理</Text>
              </TouchableOpacity>
            )}
          </View>
        </View>

        {/* 统计信息 */}
        <View style={styles.statsContainer}>
          <Text style={styles.sectionTitle}>用户统计</Text>
          <View style={styles.statsGrid}>
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>156</Text>
              <Text style={styles.statLabel}>住户总数</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>23</Text>
              <Text style={styles.statLabel}>员工总数</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>89%</Text>
              <Text style={styles.statLabel}>凭证录入率</Text>
            </View>
          </View>
        </View>

        {/* 最近活动 */}
        <View style={styles.recentContainer}>
          <Text style={styles.sectionTitle}>最近活动</Text>
          <View style={styles.activityList}>
            <View style={styles.activityItem}>
              <View style={styles.activityIcon}>
                <Ionicons name="person-add" size={16} color={theme.colors.primary} />
              </View>
              <View style={styles.activityContent}>
                <Text style={styles.activityTitle}>新增住户</Text>
                <Text style={styles.activityDesc}>张三 - 1号楼2单元301室</Text>
              </View>
              <Text style={styles.activityTime}>2小时前</Text>
            </View>
            
            <View style={styles.activityItem}>
              <View style={styles.activityIcon}>
                <Ionicons name="sync" size={16} color={theme.colors.success} />
              </View>
              <View style={styles.activityContent}>
                <Text style={styles.activityTitle}>数据下发</Text>
                <Text style={styles.activityDesc}>批量下发住户凭证到门禁设备</Text>
              </View>
              <Text style={styles.activityTime}>4小时前</Text>
            </View>
            
            <View style={styles.activityItem}>
              <View style={styles.activityIcon}>
                <Ionicons name="card" size={16} color={theme.colors.warning} />
              </View>
              <View style={styles.activityContent}>
                <Text style={styles.activityTitle}>门卡删除</Text>
                <Text style={styles.activityDesc}>李四的门卡已被删除</Text>
              </View>
              <Text style={styles.activityTime}>1天前</Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: theme.spacing.lg,
    backgroundColor: theme.colors.white,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: theme.spacing.xs,
  },
  subtitle: {
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  modulesContainer: {
    padding: theme.spacing.lg,
    gap: theme.spacing.md,
  },
  moduleCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: theme.spacing.lg,
    backgroundColor: theme.colors.white,
    borderRadius: theme.borderRadius.lg,
    shadowColor: theme.colors.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  iconContainer: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: theme.spacing.md,
  },
  moduleContent: {
    flex: 1,
  },
  moduleHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.xs,
  },
  moduleTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    flex: 1,
  },
  countBadge: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.borderRadius.sm,
  },
  countText: {
    fontSize: 12,
    fontWeight: '600',
    color: theme.colors.white,
  },
  moduleDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    lineHeight: 20,
  },
  quickActionsContainer: {
    padding: theme.spacing.lg,
    backgroundColor: theme.colors.white,
    marginTop: theme.spacing.md,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: theme.spacing.lg,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    gap: theme.spacing.md,
  },
  quickActionCard: {
    flex: 1,
    alignItems: 'center',
    padding: theme.spacing.lg,
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  quickActionText: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
    marginTop: theme.spacing.sm,
    textAlign: 'center',
  },
  statsContainer: {
    padding: theme.spacing.lg,
    backgroundColor: theme.colors.white,
    marginTop: theme.spacing.md,
  },
  statsGrid: {
    flexDirection: 'row',
    gap: theme.spacing.md,
  },
  statCard: {
    flex: 1,
    alignItems: 'center',
    padding: theme.spacing.lg,
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.md,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.primary,
    marginBottom: theme.spacing.xs,
  },
  statLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
  },
  recentContainer: {
    padding: theme.spacing.lg,
    backgroundColor: theme.colors.white,
    marginTop: theme.spacing.md,
  },
  activityList: {
    gap: theme.spacing.md,
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: theme.spacing.md,
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.md,
  },
  activityIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: theme.colors.white,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: theme.spacing.md,
  },
  activityContent: {
    flex: 1,
  },
  activityTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: theme.spacing.xs,
  },
  activityDesc: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  activityTime: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
});
