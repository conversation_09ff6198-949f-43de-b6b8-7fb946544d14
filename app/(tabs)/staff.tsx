import { Ionicons } from '@expo/vector-icons';
import React, { useCallback, useEffect, useState } from 'react';
import {
    <PERSON><PERSON>,
    FlatList,
    RefreshControl,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import {
    DataSyncModal,
    SearchFilter,
    StaffCard
} from '@/components/business';
import { Loading } from '@/components/ui';
import { theme } from '@/config/theme';
import { mockStaff } from '@/data/mockData';
import {
    DataType,
    Staff,
    UserSearchParams
} from '@/types/user';

export default function StaffScreen() {
  const [staff, setStaff] = useState<Staff[]>([]);
  const [filteredStaff, setFilteredStaff] = useState<Staff[]>([]);
  const [selectedStaff, setSelectedStaff] = useState<Staff[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [showDataSyncModal, setShowDataSyncModal] = useState(false);
  const [searchParams, setSearchParams] = useState<UserSearchParams>({
    page: 1,
    pageSize: 20,
  });

  // 模拟加载数据
  const loadStaff = useCallback(async (refresh = false) => {
    if (refresh) {
      setRefreshing(true);
    } else {
      setLoading(true);
    }

    try {
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1000));
      setStaff(mockStaff);
    } catch (error) {
      Alert.alert('错误', '加载员工数据失败');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, []);

  // 初始化加载
  useEffect(() => {
    loadStaff();
  }, [loadStaff]);

  // 筛选和搜索
  useEffect(() => {
    let filtered = [...staff];

    // 关键词搜索
    if (searchParams.keyword) {
      const keyword = searchParams.keyword.toLowerCase();
      filtered = filtered.filter(staffMember =>
        staffMember.realName.toLowerCase().includes(keyword) ||
        staffMember.phone.includes(keyword) ||
        staffMember.position.toLowerCase().includes(keyword)
      );
    }

    // 位置筛选（员工主要按组织筛选）
    if (searchParams.community) {
      filtered = filtered.filter(staffMember =>
        staffMember.organization === searchParams.community
      );
    }

    setFilteredStaff(filtered);
  }, [staff, searchParams]);

  // 处理员工选择
  const handleStaffSelect = (staffMember: Staff) => {
    setSelectedStaff(prev => {
      const isSelected = prev.some(s => s.id === staffMember.id);
      if (isSelected) {
        return prev.filter(s => s.id !== staffMember.id);
      } else {
        return [...prev, staffMember];
      }
    });
  };

  // 全选/取消全选
  const handleSelectAll = () => {
    if (selectedStaff.length === filteredStaff.length) {
      setSelectedStaff([]);
    } else {
      setSelectedStaff([...filteredStaff]);
    }
  };

  // 删除门卡
  const handleDeleteCard = async (staffMember: Staff, cardNumber: string) => {
    Alert.alert(
      '确认删除',
      `确定要删除${staffMember.realName}的门卡 ${cardNumber} 吗？`,
      [
        { text: '取消', style: 'cancel' },
        {
          text: '删除',
          style: 'destructive',
          onPress: async () => {
            try {
              // 模拟API调用
              await new Promise(resolve => setTimeout(resolve, 500));

              // 更新本地数据
              setStaff(prev => prev.map(s => {
                if (s.id === staffMember.id) {
                  return {
                    ...s,
                    nfcCards: s.nfcCards.filter(card => card.cardNumber !== cardNumber)
                  };
                }
                return s;
              }));

              Alert.alert('成功', '门卡删除成功');
            } catch (error) {
              Alert.alert('错误', '删除门卡失败');
            }
          }
        }
      ]
    );
  };

  // 数据下发
  const handleDataSync = async (userIds: string[], dataTypes: DataType[], deviceIds: string[]) => {
    try {
      setShowDataSyncModal(false);

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1500));

      Alert.alert('成功', `数据下发指令已发送\n员工数量: ${userIds.length}\n设备数量: ${deviceIds.length}`);

      // 清空选择
      setSelectedStaff([]);
    } catch (error) {
      Alert.alert('错误', '数据下发失败');
    }
  };

  // 渲染员工卡片
  const renderStaffCard = ({ item }: { item: Staff }) => (
    <StaffCard
      staff={item}
      selected={selectedStaff.some(s => s.id === item.id)}
      onSelect={handleStaffSelect}
      onDeleteCard={handleDeleteCard}
    />
  );

  // 渲染空状态
  const renderEmptyComponent = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="person-outline" size={64} color={theme.colors.textSecondary} />
      <Text style={styles.emptyText}>暂无员工数据</Text>
      <Text style={styles.emptySubtext}>请检查筛选条件或稍后重试</Text>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>小区员工</Text>
        </View>
        <Loading type="fullscreen" text="加载员工数据中..." />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>小区员工</Text>
      </View>

      <View style={styles.content}>
        {/* 搜索筛选 */}
        <View style={styles.searchSection}>
          <SearchFilter
            searchParams={searchParams}
            onSearchParamsChange={setSearchParams}
            placeholder="搜索员工姓名、手机号或岗位"
          />
        </View>

        {/* 员工列表 */}
        <FlatList
          data={filteredStaff}
          renderItem={renderStaffCard}
          keyExtractor={item => item.id}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={() => loadStaff(true)}
              colors={[theme.colors.primary]}
            />
          }
          ListEmptyComponent={renderEmptyComponent}
          contentContainerStyle={filteredStaff.length === 0 ? styles.emptyList : undefined}
        />

        {/* 底部操作栏 */}
        {selectedStaff.length > 0 && (
          <View style={styles.bottomBar}>
            <View style={styles.selectionInfo}>
              <TouchableOpacity
                style={styles.selectAllButton}
                onPress={handleSelectAll}
                activeOpacity={0.7}
              >
                <Text style={styles.selectAllText}>
                  {selectedStaff.length === filteredStaff.length ? '取消全选' : '全选'}
                </Text>
              </TouchableOpacity>
              <Text style={styles.selectionCount}>
                已选择 {selectedStaff.length} 个员工
              </Text>
            </View>
            <TouchableOpacity
              style={styles.syncButton}
              onPress={() => setShowDataSyncModal(true)}
              activeOpacity={0.7}
            >
              <Text style={styles.syncButtonText}>下发数据</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>

      {/* 数据下发模态框 */}
      <DataSyncModal
        visible={showDataSyncModal}
        selectedUsers={selectedStaff}
        userType="staff"
        onClose={() => setShowDataSyncModal(false)}
        onConfirm={handleDataSync}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    padding: theme.spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
    backgroundColor: theme.colors.surface,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  content: {
    flex: 1,
  },
  searchSection: {
    padding: theme.spacing.lg,
    backgroundColor: theme.colors.white,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.divider,
  },
  emptyList: {
    flexGrow: 1,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: theme.spacing.lg,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    marginTop: theme.spacing.md,
  },
  emptySubtext: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginTop: theme.spacing.sm,
  },
  bottomBar: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: theme.spacing.lg,
    backgroundColor: theme.colors.white,
    borderTopWidth: 1,
    borderTopColor: theme.colors.divider,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  selectionInfo: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  selectAllButton: {
    marginRight: theme.spacing.md,
  },
  selectAllText: {
    fontSize: 14,
    color: theme.colors.primary,
    fontWeight: '500',
  },
  selectionCount: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  syncButton: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
  },
  syncButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.white,
  },
});
