import { Ionicons } from '@expo/vector-icons';
import { router, useLocalSearchParams } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
    Alert,
    Image,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { MediaPreview, ReportActionPanel } from '@/components/business';
import { Loading } from '@/components/ui';
import { theme } from '@/config/theme';
import { mockReports } from '@/data/mockData';
import { PERMISSIONS, usePermissions } from '@/utils/permissions';

export default function ReportDetailScreen() {
  const { reportId } = useLocalSearchParams<{ reportId: string }>();
  const { hasPermission } = usePermissions();
  const [report, setReport] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  // 加载举报详情
  useEffect(() => {
    const loadReportDetail = async () => {
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 500));
        
        const foundReport = mockReports.find(r => r.id === reportId);
        if (foundReport) {
          setReport(foundReport);
        } else {
          Alert.alert('错误', '举报不存在');
          router.back();
        }
      } catch (error) {
        Alert.alert('错误', '加载举报详情失败');
        router.back();
      } finally {
        setLoading(false);
      }
    };

    if (reportId) {
      loadReportDetail();
    }
  }, [reportId]);

  // 处理举报
  const handleProcessReport = async (type: 'reply' | 'warning' | 'ban' | 'delete-content', content: string) => {
    if (!hasPermission(PERMISSIONS.REPORTS_MANAGE)) {
      Alert.alert('提示', '您没有处理举报的权限');
      return;
    }

    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 更新本地数据
      setReport((prev: any) => ({
        ...prev,
        status: type === 'reply' ? 'processing' : 'completed',
        processHistory: [
          ...prev.processHistory,
          {
            id: `process_${Date.now()}`,
            type,
            content,
            processor: {
              id: 'current_user',
              realName: '当前用户',
            },
            processTime: new Date().toLocaleString('zh-CN'),
          }
        ]
      }));
      
      Alert.alert('成功', '举报处理成功');
    } catch (error) {
      Alert.alert('错误', '处理举报失败');
    }
  };

  // 查看举报内容
  const handleViewContent = () => {
    if (report.contentLink) {
      // 跳转到相关内容页面
      Alert.alert('提示', `跳转到：${report.contentLink}`);
    } else {
      Alert.alert('提示', '无相关内容链接');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return theme.colors.warning;
      case 'processing':
        return theme.colors.info;
      case 'completed':
        return theme.colors.success;
      default:
        return theme.colors.textSecondary;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return '待处理';
      case 'processing':
        return '处理中';
      case 'completed':
        return '已完成';
      default:
        return status;
    }
  };

  const getProcessTypeText = (type: string) => {
    switch (type) {
      case 'reply':
        return '回复举报人';
      case 'warning':
        return '警告被举报人';
      case 'ban':
        return '禁封被举报人';
      case 'delete-content':
        return '删除相关内容';
      default:
        return type;
    }
  };

  const getProcessTypeColor = (type: string) => {
    switch (type) {
      case 'reply':
        return theme.colors.info;
      case 'warning':
        return theme.colors.warning;
      case 'ban':
        return theme.colors.error;
      case 'delete-content':
        return theme.colors.error;
      default:
        return theme.colors.textSecondary;
    }
  };

  // 渲染处理历史
  const renderProcessHistory = () => {
    if (report.processHistory.length === 0) {
      return (
        <View style={styles.emptyHistory}>
          <Text style={styles.emptyHistoryText}>暂无处理记录</Text>
        </View>
      );
    }

    return (
      <View style={styles.historyList}>
        {report.processHistory.map((record: any, index: number) => (
          <View key={record.id} style={styles.historyItem}>
            <View style={styles.historyHeader}>
              <View style={[styles.historyTypeBadge, { backgroundColor: getProcessTypeColor(record.type) }]}>
                <Text style={styles.historyTypeText}>{getProcessTypeText(record.type)}</Text>
              </View>
              <Text style={styles.historyTime}>{record.processTime}</Text>
            </View>
            <Text style={styles.historyContent}>{record.content}</Text>
            <Text style={styles.historyProcessor}>处理人：{record.processor.realName}</Text>
          </View>
        ))}
      </View>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <Loading type="fullscreen" text="加载举报详情中..." />
      </SafeAreaView>
    );
  }

  if (!report) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>举报不存在</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* 头部导航 */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>举报详情</Text>
        <View style={styles.headerRight} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* 举报基本信息 */}
        <View style={styles.reportContainer}>
          {/* 状态和类型 */}
          <View style={styles.reportHeader}>
            <View style={styles.typeBadge}>
              <Text style={styles.typeText}>{report.type}</Text>
            </View>
            <View style={[styles.statusBadge, { backgroundColor: getStatusColor(report.status) }]}>
              <Text style={styles.statusText}>{getStatusText(report.status)}</Text>
            </View>
          </View>

          {/* 举报时间 */}
          <Text style={styles.reportTime}>举报时间：{report.reportTime}</Text>

          {/* 举报人信息 */}
          <View style={styles.userSection}>
            <Text style={styles.userSectionTitle}>举报人信息</Text>
            <View style={styles.userInfo}>
              {report.reporter.avatar ? (
                <Image source={{ uri: report.reporter.avatar }} style={styles.userAvatar} />
              ) : (
                <View style={styles.userAvatarPlaceholder}>
                  <Ionicons name="person" size={20} color={theme.colors.textSecondary} />
                </View>
              )}
              <View style={styles.userDetails}>
                <Text style={styles.userName}>{report.reporter.realName}</Text>
                <Text style={styles.userPhone}>{report.reporter.phone}</Text>
              </View>
            </View>
          </View>

          {/* 被举报人信息 */}
          <View style={styles.userSection}>
            <Text style={styles.userSectionTitle}>被举报人信息</Text>
            <View style={styles.userInfo}>
              {report.reported.avatar ? (
                <Image source={{ uri: report.reported.avatar }} style={styles.userAvatar} />
              ) : (
                <View style={styles.userAvatarPlaceholder}>
                  <Ionicons name="person" size={20} color={theme.colors.textSecondary} />
                </View>
              )}
              <View style={styles.userDetails}>
                <Text style={styles.userName}>{report.reported.realName}</Text>
                <Text style={styles.userPhone}>{report.reported.phone}</Text>
              </View>
            </View>
          </View>

          {/* 举报内容 */}
          <View style={styles.contentSection}>
            <Text style={styles.contentSectionTitle}>举报内容</Text>
            <Text style={styles.contentSummary}>{report.contentSummary}</Text>
            <Text style={styles.description}>{report.description}</Text>
          </View>

          {/* 证据材料 */}
          {(report.evidenceImages.length > 0 || report.evidenceVideos.length > 0) && (
            <View style={styles.evidenceSection}>
              <Text style={styles.evidenceSectionTitle}>证据材料</Text>
              <MediaPreview 
                images={report.evidenceImages} 
                videos={report.evidenceVideos}
                maxDisplay={6}
                columns={3}
              />
            </View>
          )}
        </View>

        {/* 处理历史 */}
        <View style={styles.historyContainer}>
          <Text style={styles.historyTitle}>处理历史</Text>
          {renderProcessHistory()}
        </View>

        {/* 处理操作面板 */}
        <ReportActionPanel
          report={report}
          onProcess={handleProcessReport}
          onViewContent={handleViewContent}
        />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: theme.spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
    backgroundColor: theme.colors.white,
  },
  backButton: {
    padding: theme.spacing.xs,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  headerRight: {
    width: 32,
  },
  content: {
    flex: 1,
  },
  reportContainer: {
    backgroundColor: theme.colors.white,
    padding: theme.spacing.lg,
    marginBottom: theme.spacing.md,
  },
  reportHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: theme.spacing.md,
  },
  typeBadge: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 4,
    borderRadius: theme.borderRadius.sm,
  },
  typeText: {
    fontSize: 12,
    color: theme.colors.white,
    fontWeight: '500',
  },
  statusBadge: {
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 4,
    borderRadius: theme.borderRadius.sm,
  },
  statusText: {
    fontSize: 12,
    color: theme.colors.white,
    fontWeight: '500',
  },
  reportTime: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing.lg,
  },
  userSection: {
    marginBottom: theme.spacing.lg,
  },
  userSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  userAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: theme.spacing.md,
  },
  userAvatarPlaceholder: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.surface,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: theme.spacing.md,
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.text,
    marginBottom: 2,
  },
  userPhone: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  contentSection: {
    marginBottom: theme.spacing.lg,
  },
  contentSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
  },
  contentSummary: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
  },
  description: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    lineHeight: 20,
  },
  evidenceSection: {
    marginBottom: theme.spacing.lg,
  },
  evidenceSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
  },
  historyContainer: {
    backgroundColor: theme.colors.white,
    padding: theme.spacing.lg,
    marginBottom: theme.spacing.md,
  },
  historyTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: theme.spacing.lg,
  },
  emptyHistory: {
    alignItems: 'center',
    padding: theme.spacing.lg,
  },
  emptyHistoryText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  historyList: {
    gap: theme.spacing.md,
  },
  historyItem: {
    padding: theme.spacing.md,
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.md,
    borderLeftWidth: 4,
    borderLeftColor: theme.colors.primary,
  },
  historyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.sm,
  },
  historyTypeBadge: {
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 2,
    borderRadius: theme.borderRadius.sm,
  },
  historyTypeText: {
    fontSize: 12,
    color: theme.colors.white,
    fontWeight: '500',
  },
  historyTime: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  historyContent: {
    fontSize: 14,
    color: theme.colors.text,
    lineHeight: 20,
    marginBottom: theme.spacing.sm,
  },
  historyProcessor: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: theme.spacing.lg,
  },
  errorText: {
    fontSize: 18,
    color: theme.colors.textSecondary,
  },
});
