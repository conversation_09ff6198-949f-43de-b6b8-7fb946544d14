import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React, { useCallback, useEffect, useState } from 'react';
import {
    Alert,
    FlatList,
    RefreshControl,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import {
    ReportCard,
    SearchFilter
} from '@/components/business';
import { Loading } from '@/components/ui';
import { theme } from '@/config/theme';
import { mockReports, mockReportTypes } from '@/data/mockData';
import {
    ReportSearchParams
} from '@/types/user';
import { usePermissions } from '@/utils/permissions';

export default function ReportsScreen() {
  const { hasPermission } = usePermissions();
  const [reports, setReports] = useState<any[]>([]);
  const [filteredReports, setFilteredReports] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchParams, setSearchParams] = useState<ReportSearchParams>({
    page: 1,
    pageSize: 20,
  });

  // 模拟加载数据
  const loadReports = useCallback(async (refresh = false) => {
    if (refresh) {
      setRefreshing(true);
    } else {
      setLoading(true);
    }

    try {
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1000));
      setReports(mockReports);
    } catch (error) {
      Alert.alert('错误', '加载举报数据失败');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, []);

  // 初始化加载
  useEffect(() => {
    loadReports();
  }, [loadReports]);

  // 筛选和搜索
  useEffect(() => {
    let filtered = [...reports];

    // 关键词搜索
    if (searchParams.keyword) {
      const keyword = searchParams.keyword.toLowerCase();
      filtered = filtered.filter(report => 
        report.reporter.realName.toLowerCase().includes(keyword) ||
        report.reporter.phone.includes(keyword) ||
        report.reported.realName.toLowerCase().includes(keyword) ||
        report.reported.phone.includes(keyword)
      );
    }

    // 举报类型筛选
    if (searchParams.type) {
      filtered = filtered.filter(report => report.type === searchParams.type);
    }

    // 处理状态筛选
    if (searchParams.status) {
      filtered = filtered.filter(report => report.status === searchParams.status);
    }

    // 时间范围筛选
    if (searchParams.startTime && searchParams.endTime) {
      filtered = filtered.filter(report => {
        const reportTime = new Date(report.reportTime);
        const startTime = new Date(searchParams.startTime!);
        const endTime = new Date(searchParams.endTime!);
        return reportTime >= startTime && reportTime <= endTime;
      });
    }

    setFilteredReports(filtered);
  }, [reports, searchParams]);

  // 处理举报点击
  const handleReportPress = (report: any) => {
    // 跳转到举报详情页面
    router.push(`/community/reports/${report.id}`);
  };

  // 渲染举报卡片
  const renderReportCard = ({ item }: { item: any }) => (
    <ReportCard
      report={item}
      onPress={handleReportPress}
    />
  );

  // 渲染空状态
  const renderEmptyComponent = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="flag-outline" size={64} color={theme.colors.textSecondary} />
      <Text style={styles.emptyText}>暂无举报数据</Text>
      <Text style={styles.emptySubtext}>请检查筛选条件或稍后重试</Text>
    </View>
  );

  // 渲染状态筛选标签
  const renderStatusTags = () => {
    const statusTags = [
      { key: '', label: '全部' },
      { key: 'pending', label: '待处理' },
      { key: 'processing', label: '处理中' },
      { key: 'completed', label: '已完成' },
    ];

    return (
      <View style={styles.filterTags}>
        {statusTags.map(tag => (
          <TouchableOpacity
            key={tag.key}
            style={[
              styles.filterTag,
              searchParams.status === tag.key && styles.filterTagActive
            ]}
            onPress={() => setSearchParams(prev => ({ ...prev, status: tag.key as any }))}
            activeOpacity={0.7}
          >
            <Text style={[
              styles.filterTagText,
              searchParams.status === tag.key && styles.filterTagTextActive
            ]}>
              {tag.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  // 渲染类型筛选标签
  const renderTypeTags = () => {
    const allTypes = [{ id: '', name: '全部类型' }, ...mockReportTypes];

    return (
      <View style={styles.filterTags}>
        {allTypes.map(type => (
          <TouchableOpacity
            key={type.id}
            style={[
              styles.filterTag,
              searchParams.type === type.name && styles.filterTagActive
            ]}
            onPress={() => setSearchParams(prev => ({ 
              ...prev, 
              type: type.id === '' ? undefined : type.name 
            }))}
            activeOpacity={0.7}
          >
            <Text style={[
              styles.filterTagText,
              searchParams.type === type.name && styles.filterTagTextActive
            ]}>
              {type.name}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>举报管理</Text>
        </View>
        <Loading type="fullscreen" text="加载举报数据中..." />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>举报管理</Text>
      </View>

      <View style={styles.content}>
        {/* 搜索筛选 */}
        <View style={styles.searchSection}>
          <SearchFilter
            searchParams={searchParams}
            onSearchParamsChange={setSearchParams}
            placeholder="搜索举报人或被举报人姓名、手机号"
          />
          
          {/* 状态筛选 */}
          <View style={styles.filterSection}>
            <Text style={styles.filterLabel}>处理状态：</Text>
            {renderStatusTags()}
          </View>

          {/* 类型筛选 */}
          <View style={styles.filterSection}>
            <Text style={styles.filterLabel}>举报类型：</Text>
            {renderTypeTags()}
          </View>
        </View>

        {/* 举报列表 */}
        <FlatList
          data={filteredReports}
          renderItem={renderReportCard}
          keyExtractor={item => item.id}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={() => loadReports(true)}
              colors={[theme.colors.primary]}
            />
          }
          ListEmptyComponent={renderEmptyComponent}
          contentContainerStyle={filteredReports.length === 0 ? styles.emptyList : undefined}
        />
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    padding: theme.spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
    backgroundColor: theme.colors.white,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  content: {
    flex: 1,
  },
  searchSection: {
    padding: theme.spacing.lg,
    backgroundColor: theme.colors.white,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.divider,
  },
  filterSection: {
    marginTop: theme.spacing.md,
  },
  filterLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
  },
  filterTags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: theme.spacing.sm,
  },
  filterTag: {
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    borderColor: theme.colors.border,
    backgroundColor: theme.colors.white,
  },
  filterTagActive: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  filterTagText: {
    fontSize: 12,
    color: theme.colors.text,
  },
  filterTagTextActive: {
    color: theme.colors.white,
  },
  emptyList: {
    flexGrow: 1,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: theme.spacing.lg,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    marginTop: theme.spacing.md,
  },
  emptySubtext: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginTop: theme.spacing.sm,
  },
});
