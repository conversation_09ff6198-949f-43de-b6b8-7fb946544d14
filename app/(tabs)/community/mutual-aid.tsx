import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React, { useCallback, useEffect, useState } from 'react';
import {
    Alert,
    FlatList,
    RefreshControl,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import {
    PostCard,
    SearchFilter
} from '@/components/business';
import { Loading } from '@/components/ui';
import { theme } from '@/config/theme';
import { mockMutualAidPosts } from '@/data/mockData';
import {
    MutualAidSearchParams
} from '@/types/user';
import { usePermissions } from '@/utils/permissions';

export default function MutualAidScreen() {
  const { hasPermission } = usePermissions();
  const [posts, setPosts] = useState<any[]>([]);
  const [filteredPosts, setFilteredPosts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchParams, setSearchParams] = useState<MutualAidSearchParams>({
    page: 1,
    pageSize: 20,
  });

  // 模拟加载数据
  const loadPosts = useCallback(async (refresh = false) => {
    if (refresh) {
      setRefreshing(true);
    } else {
      setLoading(true);
    }

    try {
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1000));
      setPosts(mockMutualAidPosts);
    } catch (error) {
      Alert.alert('错误', '加载邻里互助数据失败');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, []);

  // 初始化加载
  useEffect(() => {
    loadPosts();
  }, [loadPosts]);

  // 筛选和搜索
  useEffect(() => {
    let filtered = [...posts];

    // 关键词搜索
    if (searchParams.keyword) {
      const keyword = searchParams.keyword.toLowerCase();
      filtered = filtered.filter(post =>
        post.publisher.realName.toLowerCase().includes(keyword) ||
        post.publisher.phone.includes(keyword) ||
        post.title.toLowerCase().includes(keyword) ||
        post.content.toLowerCase().includes(keyword)
      );
    }

    // 标签筛选
    if (searchParams.tag) {
      filtered = filtered.filter(post => post.tag === searchParams.tag);
    }

    // 状态筛选
    if (searchParams.status) {
      filtered = filtered.filter(post => post.status === searchParams.status);
    }

    // 位置筛选
    if (searchParams.community) {
      filtered = filtered.filter(post =>
        post.location.community === searchParams.community
      );
    }
    if (searchParams.building) {
      filtered = filtered.filter(post =>
        post.location.building === searchParams.building
      );
    }
    if (searchParams.unit) {
      filtered = filtered.filter(post =>
        post.location.unit === searchParams.unit
      );
    }
    if (searchParams.floor) {
      filtered = filtered.filter(post =>
        post.location.floor === searchParams.floor
      );
    }
    if (searchParams.room) {
      filtered = filtered.filter(post =>
        post.location.room?.includes(searchParams.room)
      );
    }

    setFilteredPosts(filtered);
  }, [posts, searchParams]);

  // 处理帖子点击
  const handlePostPress = (post: any) => {
    // 跳转到帖子详情页面
    router.push(`/community/mutual-aid/${post.id}`);
  };

  // 渲染帖子卡片
  const renderPostCard = ({ item }: { item: any }) => (
    <PostCard
      post={item}
      onPress={handlePostPress}
    />
  );

  // 渲染空状态
  const renderEmptyComponent = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="chatbubbles-outline" size={64} color={theme.colors.textSecondary} />
      <Text style={styles.emptyText}>暂无邻里互助内容</Text>
      <Text style={styles.emptySubtext}>请检查筛选条件或稍后重试</Text>
    </View>
  );

  // 渲染筛选标签
  const renderFilterTags = () => {
    const tags = [
      { key: '', label: '全部' },
      { key: 'seeking', label: '寻人寻物' },
      { key: 'lost-found', label: '失物招领' },
      { key: 'idle-items', label: '闲置物品' },
    ];

    return (
      <View style={styles.filterTags}>
        {tags.map(tag => (
          <TouchableOpacity
            key={tag.key}
            style={[
              styles.filterTag,
              searchParams.tag === tag.key && styles.filterTagActive
            ]}
            onPress={() => setSearchParams(prev => ({ ...prev, tag: tag.key as any }))}
            activeOpacity={0.7}
          >
            <Text style={[
              styles.filterTagText,
              searchParams.tag === tag.key && styles.filterTagTextActive
            ]}>
              {tag.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>邻里互助</Text>
        </View>
        <Loading type="fullscreen" text="加载邻里互助数据中..." />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>邻里互助</Text>
      </View>

      <View style={styles.content}>
        {/* 搜索筛选 */}
        <View style={styles.searchSection}>
          <SearchFilter
            searchParams={searchParams}
            onSearchParamsChange={setSearchParams}
            placeholder="搜索发布人姓名、手机号或内容"
          />
          {renderFilterTags()}
        </View>

        {/* 帖子列表 */}
        <FlatList
          data={filteredPosts}
          renderItem={renderPostCard}
          keyExtractor={item => item.id}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={() => loadPosts(true)}
              colors={[theme.colors.primary]}
            />
          }
          ListEmptyComponent={renderEmptyComponent}
          contentContainerStyle={filteredPosts.length === 0 ? styles.emptyList : undefined}
        />
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    padding: theme.spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
    backgroundColor: theme.colors.surface,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  content: {
    flex: 1,
  },
  searchSection: {
    padding: theme.spacing.lg,
    backgroundColor: theme.colors.white,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.divider,
  },
  filterTags: {
    flexDirection: 'row',
    marginTop: theme.spacing.md,
    gap: theme.spacing.sm,
  },
  filterTag: {
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    borderColor: theme.colors.border,
    backgroundColor: theme.colors.white,
  },
  filterTagActive: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  filterTagText: {
    fontSize: 14,
    color: theme.colors.text,
  },
  filterTagTextActive: {
    color: theme.colors.white,
  },
  emptyList: {
    flexGrow: 1,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: theme.spacing.lg,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    marginTop: theme.spacing.md,
  },
  emptySubtext: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginTop: theme.spacing.sm,
  },
});
