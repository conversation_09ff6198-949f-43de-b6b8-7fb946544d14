import { Ionicons } from '@expo/vector-icons';
import { router, useLocalSearchParams } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
    Alert,
    Image,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { MediaPreview } from '@/components/business';
import { Loading } from '@/components/ui';
import { theme } from '@/config/theme';
import { mockComments, mockMutualAidPosts } from '@/data/mockData';
import { PERMISSIONS, usePermissions } from '@/utils/permissions';

export default function MutualAidDetailScreen() {
  const { postId } = useLocalSearchParams<{ postId: string }>();
  const { hasPermission } = usePermissions();
  const [post, setPost] = useState<any>(null);
  const [comments, setComments] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAllComments, setShowAllComments] = useState(false);

  // 加载帖子详情
  useEffect(() => {
    const loadPostDetail = async () => {
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 500));
        
        const foundPost = mockMutualAidPosts.find(p => p.id === postId);
        if (foundPost) {
          setPost(foundPost);
          // 加载评论
          const postComments = mockComments.filter(c => c.postId === postId);
          setComments(postComments);
        } else {
          Alert.alert('错误', '帖子不存在');
          router.back();
        }
      } catch (error) {
        Alert.alert('错误', '加载帖子详情失败');
        router.back();
      } finally {
        setLoading(false);
      }
    };

    if (postId) {
      loadPostDetail();
    }
  }, [postId]);

  // 删除帖子
  const handleDeletePost = () => {
    if (!hasPermission(PERMISSIONS.MUTUAL_AID_MANAGE)) {
      Alert.alert('提示', '您没有删除帖子的权限');
      return;
    }

    Alert.alert(
      '确认删除',
      '确定要删除这个帖子吗？删除后无法恢复。',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '删除',
          style: 'destructive',
          onPress: () => {
            Alert.prompt(
              '删除原因',
              '请输入删除原因（可选）',
              [
                { text: '取消', style: 'cancel' },
                {
                  text: '确定',
                  onPress: async (reason) => {
                    try {
                      // 模拟API调用
                      await new Promise(resolve => setTimeout(resolve, 500));
                      
                      Alert.alert('成功', '帖子已删除');
                      router.back();
                    } catch (error) {
                      Alert.alert('错误', '删除帖子失败');
                    }
                  }
                }
              ],
              'plain-text',
              '',
              '请输入删除原因'
            );
          }
        }
      ]
    );
  };

  // 删除评论
  const handleDeleteComment = (commentId: string) => {
    if (!hasPermission(PERMISSIONS.MUTUAL_AID_MANAGE)) {
      Alert.alert('提示', '您没有删除评论的权限');
      return;
    }

    Alert.alert(
      '确认删除',
      '确定要删除这条评论吗？',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '删除',
          style: 'destructive',
          onPress: async () => {
            try {
              // 模拟API调用
              await new Promise(resolve => setTimeout(resolve, 500));
              
              // 更新本地评论列表
              setComments(prev => prev.map(comment => 
                comment.id === commentId 
                  ? { ...comment, status: 'deleted', deleteReason: '违规内容' }
                  : comment
              ));
              
              Alert.alert('成功', '评论已删除');
            } catch (error) {
              Alert.alert('错误', '删除评论失败');
            }
          }
        }
      ]
    );
  };

  const getTagText = (tag: string) => {
    switch (tag) {
      case 'seeking':
        return '寻人寻物';
      case 'lost-found':
        return '失物招领';
      case 'idle-items':
        return '闲置物品';
      default:
        return tag;
    }
  };

  const getTagColor = (tag: string) => {
    switch (tag) {
      case 'seeking':
        return '#FF9800';
      case 'lost-found':
        return '#4CAF50';
      case 'idle-items':
        return '#2196F3';
      default:
        return theme.colors.textSecondary;
    }
  };

  // 渲染评论项
  const renderComment = (comment: any) => (
    <View key={comment.id} style={styles.commentItem}>
      <View style={styles.commentHeader}>
        <View style={styles.commenterInfo}>
          {comment.commenter.avatar ? (
            <Image source={{ uri: comment.commenter.avatar }} style={styles.commenterAvatar} />
          ) : (
            <View style={styles.commenterAvatarPlaceholder}>
              <Ionicons name="person" size={16} color={theme.colors.textSecondary} />
            </View>
          )}
          <View style={styles.commenterDetails}>
            <Text style={styles.commenterName}>{comment.commenter.realName}</Text>
            {comment.commenter.nickname && (
              <Text style={styles.commenterNickname}>({comment.commenter.nickname})</Text>
            )}
            <Text style={styles.commentTime}>{comment.commentTime}</Text>
          </View>
        </View>
        
        {/* 删除按钮 */}
        {comment.status === 'normal' && hasPermission(PERMISSIONS.MUTUAL_AID_MANAGE) && (
          <TouchableOpacity
            style={styles.deleteCommentButton}
            onPress={() => handleDeleteComment(comment.id)}
            activeOpacity={0.7}
          >
            <Text style={styles.deleteCommentButtonText}>删除</Text>
          </TouchableOpacity>
        )}
      </View>
      
      <Text style={[
        styles.commentContent,
        comment.status === 'deleted' && styles.deletedContent
      ]}>
        {comment.status === 'deleted' 
          ? `评论已删除：${comment.deleteReason || '违规内容'}`
          : comment.content
        }
      </Text>
      
      {comment.images && comment.images.length > 0 && comment.status === 'normal' && (
        <MediaPreview images={comment.images} maxDisplay={3} columns={3} />
      )}
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <Loading type="fullscreen" text="加载帖子详情中..." />
      </SafeAreaView>
    );
  }

  if (!post) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>帖子不存在</Text>
        </View>
      </SafeAreaView>
    );
  }

  const displayComments = showAllComments ? comments : comments.slice(0, 3);

  return (
    <SafeAreaView style={styles.container}>
      {/* 头部导航 */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>帖子详情</Text>
        <View style={styles.headerRight} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* 帖子内容 */}
        <View style={styles.postContainer}>
          {/* 状态和标签 */}
          <View style={styles.postHeader}>
            <View style={[styles.tagBadge, { backgroundColor: getTagColor(post.tag) }]}>
              <Text style={styles.tagText}>{getTagText(post.tag)}</Text>
            </View>
            {post.status === 'deleted' && (
              <View style={styles.deletedBadge}>
                <Text style={styles.deletedText}>已删除</Text>
              </View>
            )}
          </View>

          {/* 发布者信息 */}
          <View style={styles.publisherSection}>
            {post.publisher.avatar ? (
              <Image source={{ uri: post.publisher.avatar }} style={styles.publisherAvatar} />
            ) : (
              <View style={styles.publisherAvatarPlaceholder}>
                <Ionicons name="person" size={24} color={theme.colors.textSecondary} />
              </View>
            )}
            <View style={styles.publisherInfo}>
              <Text style={styles.publisherName}>{post.publisher.realName}</Text>
              <Text style={styles.publisherRoom}>{post.publisher.room}</Text>
              <Text style={styles.publishTime}>{post.publishTime}</Text>
            </View>
          </View>

          {/* 标题和内容 */}
          <Text style={[styles.postTitle, post.status === 'deleted' && styles.deletedContent]}>
            {post.status === 'deleted' ? '内容已删除' : post.title}
          </Text>
          
          <Text style={[styles.postContent, post.status === 'deleted' && styles.deletedContent]}>
            {post.status === 'deleted' 
              ? `删除原因：${post.deleteReason || '违规内容'}`
              : post.content
            }
          </Text>

          {/* 媒体附件 */}
          {post.images.length > 0 && post.status === 'normal' && (
            <MediaPreview images={post.images} videos={post.videos} />
          )}

          {/* 删除按钮 */}
          {post.status === 'normal' && hasPermission(PERMISSIONS.MUTUAL_AID_MANAGE) && (
            <TouchableOpacity
              style={styles.deletePostButton}
              onPress={handleDeletePost}
              activeOpacity={0.7}
            >
              <Ionicons name="trash-outline" size={20} color={theme.colors.white} />
              <Text style={styles.deletePostButtonText}>删帖</Text>
            </TouchableOpacity>
          )}
        </View>

        {/* 评论列表 */}
        <View style={styles.commentsContainer}>
          <View style={styles.commentsHeader}>
            <Text style={styles.commentsTitle}>留言 ({comments.length})</Text>
            {comments.length > 3 && (
              <TouchableOpacity
                onPress={() => setShowAllComments(!showAllComments)}
                activeOpacity={0.7}
              >
                <Text style={styles.expandText}>
                  {showAllComments ? '收起' : '展开更多'}
                </Text>
              </TouchableOpacity>
            )}
          </View>
          
          {displayComments.map(renderComment)}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: theme.spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
    backgroundColor: theme.colors.white,
  },
  backButton: {
    padding: theme.spacing.xs,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  headerRight: {
    width: 32,
  },
  content: {
    flex: 1,
  },
  postContainer: {
    backgroundColor: theme.colors.white,
    padding: theme.spacing.lg,
    marginBottom: theme.spacing.md,
  },
  postHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: theme.spacing.md,
  },
  tagBadge: {
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 4,
    borderRadius: theme.borderRadius.sm,
  },
  tagText: {
    fontSize: 12,
    color: theme.colors.white,
    fontWeight: '500',
  },
  deletedBadge: {
    backgroundColor: theme.colors.error,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 4,
    borderRadius: theme.borderRadius.sm,
  },
  deletedText: {
    fontSize: 12,
    color: theme.colors.white,
    fontWeight: '500',
  },
  publisherSection: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.lg,
  },
  publisherAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    marginRight: theme.spacing.md,
  },
  publisherAvatarPlaceholder: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: theme.colors.surface,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: theme.spacing.md,
  },
  publisherInfo: {
    flex: 1,
  },
  publisherName: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 2,
  },
  publisherRoom: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 2,
  },
  publishTime: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  postTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    lineHeight: 26,
    marginBottom: theme.spacing.md,
  },
  postContent: {
    fontSize: 16,
    color: theme.colors.text,
    lineHeight: 24,
    marginBottom: theme.spacing.lg,
  },
  deletedContent: {
    color: theme.colors.textSecondary,
    fontStyle: 'italic',
  },
  deletePostButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.colors.error,
    paddingVertical: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
    marginTop: theme.spacing.lg,
  },
  deletePostButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.white,
    marginLeft: theme.spacing.sm,
  },
  commentsContainer: {
    backgroundColor: theme.colors.white,
    padding: theme.spacing.lg,
  },
  commentsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.lg,
  },
  commentsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
  },
  expandText: {
    fontSize: 14,
    color: theme.colors.primary,
    fontWeight: '500',
  },
  commentItem: {
    marginBottom: theme.spacing.lg,
    paddingBottom: theme.spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.divider,
  },
  commentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: theme.spacing.sm,
  },
  commenterInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  commenterAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    marginRight: theme.spacing.sm,
  },
  commenterAvatarPlaceholder: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: theme.colors.surface,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: theme.spacing.sm,
  },
  commenterDetails: {
    flex: 1,
  },
  commenterName: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
  },
  commenterNickname: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginTop: 2,
  },
  commentTime: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginTop: 2,
  },
  deleteCommentButton: {
    backgroundColor: theme.colors.error,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 4,
    borderRadius: theme.borderRadius.sm,
  },
  deleteCommentButtonText: {
    fontSize: 12,
    color: theme.colors.white,
    fontWeight: '500',
  },
  commentContent: {
    fontSize: 14,
    color: theme.colors.text,
    lineHeight: 20,
    marginBottom: theme.spacing.sm,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: theme.spacing.lg,
  },
  errorText: {
    fontSize: 18,
    color: theme.colors.textSecondary,
  },
});
