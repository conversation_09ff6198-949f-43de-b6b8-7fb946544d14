import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React from 'react';
import {
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { theme } from '@/config/theme';
import { PERMISSIONS, usePermissions } from '@/utils/permissions';

interface CommunityModule {
  id: string;
  title: string;
  description: string;
  icon: keyof typeof Ionicons.glyphMap;
  route: string;
  permission: string;
  color: string;
  count?: number;
}

export default function CommunityScreen() {
  const { hasPermission } = usePermissions();

  const communityModules: CommunityModule[] = [
    {
      id: 'mutual-aid',
      title: '邻里互助',
      description: '查看和管理邻里互助内容',
      icon: 'chatbubbles',
      route: '/community/mutual-aid',
      permission: PERMISSIONS.MUTUAL_AID_MANAGE,
      color: '#4CAF50',
      count: 12,
    },
    {
      id: 'reports',
      title: '举报管理',
      description: '处理用户举报信息',
      icon: 'flag',
      route: '/community/reports',
      permission: PERMISSIONS.REPORTS_MANAGE,
      color: '#FF5722',
      count: 3,
    },
  ];

  const handleModulePress = (module: CommunityModule) => {
    if (hasPermission(module.permission)) {
      router.push(module.route as any);
    }
  };

  const availableModules = communityModules.filter(module =>
    hasPermission(module.permission)
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* 头部 */}
        <View style={styles.header}>
          <Text style={styles.title}>社区管理</Text>
          <Text style={styles.subtitle}>邻里互助和举报管理</Text>
        </View>

        {/* 功能模块网格 */}
        <View style={styles.modulesContainer}>
          {availableModules.map((module) => (
            <TouchableOpacity
              key={module.id}
              style={styles.moduleCard}
              onPress={() => handleModulePress(module)}
              activeOpacity={0.7}
            >
              <View style={[styles.iconContainer, { backgroundColor: module.color }]}>
                <Ionicons
                  name={module.icon}
                  size={32}
                  color={theme.colors.white}
                />
              </View>
              <View style={styles.moduleContent}>
                <View style={styles.moduleTitleRow}>
                  <Text style={styles.moduleTitle}>{module.title}</Text>
                  {module.count !== undefined && (
                    <View style={styles.countBadge}>
                      <Text style={styles.countText}>{module.count}</Text>
                    </View>
                  )}
                </View>
                <Text style={styles.moduleDescription}>{module.description}</Text>
              </View>
              <Ionicons
                name="chevron-forward"
                size={20}
                color={theme.colors.textSecondary}
              />
            </TouchableOpacity>
          ))}
        </View>

        {/* 快捷操作 */}
        <View style={styles.quickActionsContainer}>
          <Text style={styles.sectionTitle}>快捷操作</Text>
          <View style={styles.quickActionsGrid}>
            {hasPermission(PERMISSIONS.MUTUAL_AID_MANAGE) && (
              <TouchableOpacity
                style={styles.quickActionCard}
                onPress={() => router.push('/community/mutual-aid')}
              >
                <Ionicons name="search" size={24} color={theme.colors.primary} />
                <Text style={styles.quickActionText}>查看内容</Text>
              </TouchableOpacity>
            )}

            {hasPermission(PERMISSIONS.REPORTS_MANAGE) && (
              <TouchableOpacity
                style={styles.quickActionCard}
                onPress={() => router.push('/community/reports')}
              >
                <Ionicons name="shield-checkmark" size={24} color={theme.colors.primary} />
                <Text style={styles.quickActionText}>处理举报</Text>
              </TouchableOpacity>
            )}

            <TouchableOpacity
              style={styles.quickActionCard}
              onPress={() => {
                // 可以添加更多快捷操作
              }}
            >
              <Ionicons name="analytics" size={24} color={theme.colors.primary} />
              <Text style={styles.quickActionText}>数据统计</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* 统计信息 */}
        <View style={styles.statsContainer}>
          <Text style={styles.sectionTitle}>社区统计</Text>
          <View style={styles.statsGrid}>
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>12</Text>
              <Text style={styles.statLabel}>互助内容</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>3</Text>
              <Text style={styles.statLabel}>待处理举报</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>89%</Text>
              <Text style={styles.statLabel}>处理效率</Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: theme.spacing.lg,
    backgroundColor: theme.colors.white,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: theme.spacing.xs,
  },
  subtitle: {
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  modulesContainer: {
    padding: theme.spacing.lg,
    gap: theme.spacing.md,
  },
  moduleCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: theme.spacing.lg,
    backgroundColor: theme.colors.white,
    borderRadius: theme.borderRadius.lg,
    shadowColor: theme.colors.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  iconContainer: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: theme.spacing.md,
  },
  moduleContent: {
    flex: 1,
  },
  moduleTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.xs,
  },
  moduleTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    flex: 1,
  },
  countBadge: {
    backgroundColor: theme.colors.primary,
    borderRadius: 12,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 2,
    marginLeft: theme.spacing.sm,
  },
  countText: {
    fontSize: 12,
    fontWeight: '600',
    color: theme.colors.white,
  },
  moduleDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    lineHeight: 20,
  },
  quickActionsContainer: {
    padding: theme.spacing.lg,
    backgroundColor: theme.colors.white,
    marginTop: theme.spacing.md,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: theme.spacing.lg,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    gap: theme.spacing.md,
  },
  quickActionCard: {
    flex: 1,
    alignItems: 'center',
    padding: theme.spacing.lg,
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  quickActionText: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
    marginTop: theme.spacing.sm,
    textAlign: 'center',
  },
  statsContainer: {
    padding: theme.spacing.lg,
    backgroundColor: theme.colors.white,
    marginTop: theme.spacing.md,
  },
  statsGrid: {
    flexDirection: 'row',
    gap: theme.spacing.md,
  },
  statCard: {
    flex: 1,
    alignItems: 'center',
    padding: theme.spacing.lg,
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.md,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.primary,
    marginBottom: theme.spacing.xs,
  },
  statLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
  },
});
