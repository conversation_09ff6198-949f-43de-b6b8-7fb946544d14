import { Redirect } from 'expo-router';
import { useEffect, useState } from 'react';

import { PERMISSIONS, usePermissions } from '@/utils/permissions';

/**
 * Tab 默认页面 - 重定向到第一个有权限的 tab
 */
export default function TabsIndex() {
  const { hasPermission } = usePermissions();
  const [redirectPath, setRedirectPath] = useState<string | null>(null);

  useEffect(() => {
    // 按优先级检查权限并重定向到第一个可用的 tab
    const tabPriority = [
      { permission: PERMISSIONS.DEVICE_REGISTER, path: '/device-register' },
      { permission: PERMISSIONS.DEVICE_DEBUG, path: '/device-debug' },
      { permission: PERMISSIONS.DEVICE_UNBIND, path: '/device-unbind' },
      { permission: PERMISSIONS.DEVICE_CONTROL, path: '/device-control' },
      { permission: PERMISSIONS.RESIDENTS_VIEW, path: '/residents' },
      { permission: PERMISSIONS.STAFF_VIEW, path: '/staff' },
      { permission: PERMISSIONS.COMMUNITY_MANAGE, path: '/community' },
    ];

    // 找到第一个有权限的 tab
    const firstAvailableTab = tabPriority.find(tab => 
      hasPermission(tab.permission)
    );

    if (firstAvailableTab) {
      setRedirectPath(firstAvailableTab.path);
    } else {
      // 如果没有任何权限，重定向到个人中心
      setRedirectPath('/profile');
    }
  }, [hasPermission]);

  // 等待权限检查完成
  if (!redirectPath) {
    return null;
  }

  return <Redirect href={redirectPath} />;
}
