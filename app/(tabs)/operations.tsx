import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React from 'react';
import {
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { theme } from '@/config/theme';
import { PERMISSIONS, usePermissions } from '@/utils/permissions';

interface OperationModule {
  id: string;
  title: string;
  description: string;
  icon: keyof typeof Ionicons.glyphMap;
  route: string;
  permission: string;
  color: string;
}

export default function OperationsScreen() {
  const { hasPermission } = usePermissions();

  const operationModules: OperationModule[] = [
    {
      id: 'device-register',
      title: '设备注册',
      description: '注册门禁、网关、呼叫器等设备',
      icon: 'add-circle',
      route: '/device-register',
      permission: PERMISSIONS.DEVICE_REGISTER,
      color: '#4CAF50',
    },
    {
      id: 'device-debug',
      title: '设备调试',
      description: '调试门禁设备，管理临时凭证',
      icon: 'build',
      route: '/device-debug',
      permission: PERMISSIONS.DEVICE_DEBUG,
      color: '#2196F3',
    },
    {
      id: 'device-unbind',
      title: '设备解绑',
      description: '解绑设备与基础设施的关联',
      icon: 'unlink',
      route: '/device-unbind',
      permission: PERMISSIONS.DEVICE_UNBIND,
      color: '#FF9800',
    },
    {
      id: 'device-control',
      title: '设备控制',
      description: '批量控制设备，远程操作管理',
      icon: 'settings',
      route: '/device-control',
      permission: PERMISSIONS.DEVICE_CONTROL,
      color: '#9C27B0',
    },
  ];

  const handleModulePress = (module: OperationModule) => {
    if (hasPermission(module.permission)) {
      router.push(module.route as any);
    }
  };

  const availableModules = operationModules.filter(module => 
    hasPermission(module.permission)
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* 头部 */}
        <View style={styles.header}>
          <Text style={styles.title}>运维管理</Text>
          <Text style={styles.subtitle}>设备运维和管理功能</Text>
        </View>

        {/* 功能模块网格 */}
        <View style={styles.modulesContainer}>
          {availableModules.map((module) => (
            <TouchableOpacity
              key={module.id}
              style={styles.moduleCard}
              onPress={() => handleModulePress(module)}
              activeOpacity={0.7}
            >
              <View style={[styles.iconContainer, { backgroundColor: module.color }]}>
                <Ionicons
                  name={module.icon}
                  size={32}
                  color={theme.colors.white}
                />
              </View>
              <View style={styles.moduleContent}>
                <Text style={styles.moduleTitle}>{module.title}</Text>
                <Text style={styles.moduleDescription}>{module.description}</Text>
              </View>
              <Ionicons
                name="chevron-forward"
                size={20}
                color={theme.colors.textSecondary}
              />
            </TouchableOpacity>
          ))}
        </View>

        {/* 快捷操作 */}
        <View style={styles.quickActionsContainer}>
          <Text style={styles.sectionTitle}>快捷操作</Text>
          <View style={styles.quickActionsGrid}>
            {hasPermission(PERMISSIONS.DEVICE_REGISTER) && (
              <TouchableOpacity
                style={styles.quickActionCard}
                onPress={() => router.push('/device-register')}
              >
                <Ionicons name="qr-code" size={24} color={theme.colors.primary} />
                <Text style={styles.quickActionText}>扫码注册</Text>
              </TouchableOpacity>
            )}
            
            {hasPermission(PERMISSIONS.DEVICE_DEBUG) && (
              <TouchableOpacity
                style={styles.quickActionCard}
                onPress={() => router.push('/device-debug')}
              >
                <Ionicons name="flash" size={24} color={theme.colors.primary} />
                <Text style={styles.quickActionText}>快速调试</Text>
              </TouchableOpacity>
            )}
            
            {hasPermission(PERMISSIONS.DEVICE_CONTROL) && (
              <TouchableOpacity
                style={styles.quickActionCard}
                onPress={() => router.push('/device-control')}
              >
                <Ionicons name="sync" size={24} color={theme.colors.primary} />
                <Text style={styles.quickActionText}>批量操作</Text>
              </TouchableOpacity>
            )}
          </View>
        </View>

        {/* 统计信息 */}
        <View style={styles.statsContainer}>
          <Text style={styles.sectionTitle}>设备统计</Text>
          <View style={styles.statsGrid}>
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>24</Text>
              <Text style={styles.statLabel}>在线设备</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>3</Text>
              <Text style={styles.statLabel}>离线设备</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>156</Text>
              <Text style={styles.statLabel}>总用户数</Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: theme.spacing.lg,
    backgroundColor: theme.colors.white,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: theme.spacing.xs,
  },
  subtitle: {
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  modulesContainer: {
    padding: theme.spacing.lg,
    gap: theme.spacing.md,
  },
  moduleCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: theme.spacing.lg,
    backgroundColor: theme.colors.white,
    borderRadius: theme.borderRadius.lg,
    shadowColor: theme.colors.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  iconContainer: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: theme.spacing.md,
  },
  moduleContent: {
    flex: 1,
  },
  moduleTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: theme.spacing.xs,
  },
  moduleDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    lineHeight: 20,
  },
  quickActionsContainer: {
    padding: theme.spacing.lg,
    backgroundColor: theme.colors.white,
    marginTop: theme.spacing.md,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: theme.spacing.lg,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    gap: theme.spacing.md,
  },
  quickActionCard: {
    flex: 1,
    alignItems: 'center',
    padding: theme.spacing.lg,
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  quickActionText: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
    marginTop: theme.spacing.sm,
    textAlign: 'center',
  },
  statsContainer: {
    padding: theme.spacing.lg,
    backgroundColor: theme.colors.white,
    marginTop: theme.spacing.md,
  },
  statsGrid: {
    flexDirection: 'row',
    gap: theme.spacing.md,
  },
  statCard: {
    flex: 1,
    alignItems: 'center',
    padding: theme.spacing.lg,
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.md,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.primary,
    marginBottom: theme.spacing.xs,
  },
  statLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
  },
});
