import { Ionicons } from '@expo/vector-icons';
import React, { useCallback, useEffect, useState } from 'react';
import {
    <PERSON><PERSON>,
    FlatList,
    RefreshControl,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import {
    DataSyncModal,
    ResidentCard,
    SearchFilter
} from '@/components/business';
import { Loading } from '@/components/ui';
import { theme } from '@/config/theme';
import { mockResidents } from '@/data/mockData';
import {
    DataType,
    Resident,
    UserSearchParams
} from '@/types/user';

export default function ResidentsScreen() {
  const [residents, setResidents] = useState<Resident[]>([]);
  const [filteredResidents, setFilteredResidents] = useState<Resident[]>([]);
  const [selectedResidents, setSelectedResidents] = useState<Resident[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [showDataSyncModal, setShowDataSyncModal] = useState(false);
  const [searchParams, setSearchParams] = useState<UserSearchParams>({
    page: 1,
    pageSize: 20,
  });

  // 模拟加载数据
  const loadResidents = useCallback(async (refresh = false) => {
    if (refresh) {
      setRefreshing(true);
    } else {
      setLoading(true);
    }

    try {
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1000));
      setResidents(mockResidents);
    } catch (error) {
      Alert.alert('错误', '加载住户数据失败');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, []);

  // 初始化加载
  useEffect(() => {
    loadResidents();
  }, [loadResidents]);

  // 筛选和搜索
  useEffect(() => {
    let filtered = [...residents];

    // 关键词搜索
    if (searchParams.keyword) {
      const keyword = searchParams.keyword.toLowerCase();
      filtered = filtered.filter(resident =>
        resident.realName.toLowerCase().includes(keyword) ||
        resident.phone.includes(keyword)
      );
    }

    // 位置筛选
    if (searchParams.community) {
      filtered = filtered.filter(resident =>
        resident.community === searchParams.community
      );
    }
    if (searchParams.building) {
      filtered = filtered.filter(resident =>
        resident.building === searchParams.building
      );
    }
    if (searchParams.unit) {
      filtered = filtered.filter(resident =>
        resident.unit === searchParams.unit
      );
    }
    if (searchParams.floor) {
      filtered = filtered.filter(resident =>
        resident.floor === searchParams.floor
      );
    }
    if (searchParams.room) {
      filtered = filtered.filter(resident =>
        resident.room.includes(searchParams.room)
      );
    }

    setFilteredResidents(filtered);
  }, [residents, searchParams]);

  // 处理住户选择
  const handleResidentSelect = (resident: Resident) => {
    setSelectedResidents(prev => {
      const isSelected = prev.some(r => r.id === resident.id);
      if (isSelected) {
        return prev.filter(r => r.id !== resident.id);
      } else {
        return [...prev, resident];
      }
    });
  };

  // 全选/取消全选
  const handleSelectAll = () => {
    if (selectedResidents.length === filteredResidents.length) {
      setSelectedResidents([]);
    } else {
      setSelectedResidents([...filteredResidents]);
    }
  };

  // 删除门卡
  const handleDeleteCard = async (resident: Resident, cardNumber: string) => {
    Alert.alert(
      '确认删除',
      `确定要删除${resident.realName}的门卡 ${cardNumber} 吗？`,
      [
        { text: '取消', style: 'cancel' },
        {
          text: '删除',
          style: 'destructive',
          onPress: async () => {
            try {
              // 模拟API调用
              await new Promise(resolve => setTimeout(resolve, 500));

              // 更新本地数据
              setResidents(prev => prev.map(r => {
                if (r.id === resident.id) {
                  return {
                    ...r,
                    nfcCards: r.nfcCards.filter(card => card.cardNumber !== cardNumber)
                  };
                }
                return r;
              }));

              Alert.alert('成功', '门卡删除成功');
            } catch (error) {
              Alert.alert('错误', '删除门卡失败');
            }
          }
        }
      ]
    );
  };

  // 数据下发
  const handleDataSync = async (userIds: string[], dataTypes: DataType[], deviceIds: string[]) => {
    try {
      setShowDataSyncModal(false);

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1500));

      Alert.alert('成功', `数据下发指令已发送\n用户数量: ${userIds.length}\n设备数量: ${deviceIds.length}`);

      // 清空选择
      setSelectedResidents([]);
    } catch (error) {
      Alert.alert('错误', '数据下发失败');
    }
  };

  // 渲染住户卡片
  const renderResidentCard = ({ item }: { item: Resident }) => (
    <ResidentCard
      resident={item}
      selected={selectedResidents.some(r => r.id === item.id)}
      onSelect={handleResidentSelect}
      onDeleteCard={handleDeleteCard}
    />
  );

  // 渲染空状态
  const renderEmptyComponent = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="people-outline" size={64} color={theme.colors.textSecondary} />
      <Text style={styles.emptyText}>暂无住户数据</Text>
      <Text style={styles.emptySubtext}>请检查筛选条件或稍后重试</Text>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>小区住户</Text>
        </View>
        <Loading type="fullscreen" text="加载住户数据中..." />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>小区住户</Text>
      </View>

      <View style={styles.content}>
        {/* 搜索筛选 */}
        <View style={styles.searchSection}>
          <SearchFilter
            searchParams={searchParams}
            onSearchParamsChange={setSearchParams}
            placeholder="搜索住户姓名或手机号"
          />
        </View>

        {/* 住户列表 */}
        <FlatList
          data={filteredResidents}
          renderItem={renderResidentCard}
          keyExtractor={item => item.id}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={() => loadResidents(true)}
              colors={[theme.colors.primary]}
            />
          }
          ListEmptyComponent={renderEmptyComponent}
          contentContainerStyle={filteredResidents.length === 0 ? styles.emptyList : undefined}
        />

        {/* 底部操作栏 */}
        {selectedResidents.length > 0 && (
          <View style={styles.bottomBar}>
            <View style={styles.selectionInfo}>
              <TouchableOpacity
                style={styles.selectAllButton}
                onPress={handleSelectAll}
                activeOpacity={0.7}
              >
                <Text style={styles.selectAllText}>
                  {selectedResidents.length === filteredResidents.length ? '取消全选' : '全选'}
                </Text>
              </TouchableOpacity>
              <Text style={styles.selectionCount}>
                已选择 {selectedResidents.length} 个住户
              </Text>
            </View>
            <TouchableOpacity
              style={styles.syncButton}
              onPress={() => setShowDataSyncModal(true)}
              activeOpacity={0.7}
            >
              <Text style={styles.syncButtonText}>下发数据</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>

      {/* 数据下发模态框 */}
      <DataSyncModal
        visible={showDataSyncModal}
        selectedUsers={selectedResidents}
        userType="resident"
        onClose={() => setShowDataSyncModal(false)}
        onConfirm={handleDataSync}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    padding: theme.spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
    backgroundColor: theme.colors.surface,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  content: {
    flex: 1,
  },
  searchSection: {
    padding: theme.spacing.lg,
    backgroundColor: theme.colors.white,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.divider,
  },
  emptyList: {
    flexGrow: 1,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: theme.spacing.lg,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    marginTop: theme.spacing.md,
  },
  emptySubtext: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginTop: theme.spacing.sm,
  },
  bottomBar: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: theme.spacing.lg,
    backgroundColor: theme.colors.white,
    borderTopWidth: 1,
    borderTopColor: theme.colors.divider,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  selectionInfo: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  selectAllButton: {
    marginRight: theme.spacing.md,
  },
  selectAllText: {
    fontSize: 14,
    color: theme.colors.primary,
    fontWeight: '500',
  },
  selectionCount: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  syncButton: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
  },
  syncButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.white,
  },
});
