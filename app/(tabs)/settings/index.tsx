import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React, { useState } from 'react';
import {
    Alert,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { Loading } from '@/components/ui';
import { theme } from '@/config/theme';
import { useAuthStore } from '@/stores/auth';

export default function SettingsScreen() {
  const { logout } = useAuthStore();
  const [loading, setLoading] = useState(false);

  // 检查更新
  const handleCheckUpdate = async () => {
    setLoading(true);
    
    try {
      // 模拟检查更新
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 模拟有新版本的情况（30%概率）
      const hasUpdate = Math.random() < 0.3;
      
      if (hasUpdate) {
        Alert.alert(
          '发现新版本',
          '发现新版本 V2.1.0，是否立即更新？\n\n更新内容：\n• 优化用户体验\n• 修复已知问题\n• 新增功能特性',
          [
            { text: '稍后更新', style: 'cancel' },
            {
              text: '立即更新',
              onPress: () => {
                Alert.alert('提示', '正在下载更新包...');
              }
            }
          ]
        );
      } else {
        Alert.alert('提示', '当前已是最新版本');
      }
    } catch (error) {
      Alert.alert('错误', '检查更新失败');
    } finally {
      setLoading(false);
    }
  };

  // 注销账户
  const handleDeleteAccount = () => {
    Alert.alert(
      '注销账户',
      '注销账户将永久删除您的所有数据，此操作不可恢复。\n\n注销后您将无法：\n• 访问个人信息\n• 使用设备管理功能\n• 恢复历史数据\n\n确定要继续吗？',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '确认注销',
          style: 'destructive',
          onPress: () => {
            Alert.alert(
              '最终确认',
              '请再次确认是否要注销账户？此操作不可撤销！',
              [
                { text: '取消', style: 'cancel' },
                {
                  text: '确认注销',
                  style: 'destructive',
                  onPress: async () => {
                    setLoading(true);
                    try {
                      // 模拟注销账户API调用
                      await new Promise(resolve => setTimeout(resolve, 2000));
                      
                      Alert.alert('成功', '账户已注销', [
                        {
                          text: '确定',
                          onPress: () => {
                            logout();
                            router.replace('/login');
                          }
                        }
                      ]);
                    } catch (error) {
                      Alert.alert('错误', '注销账户失败');
                    } finally {
                      setLoading(false);
                    }
                  }
                }
              ]
            );
          }
        }
      ]
    );
  };

  // 退出登录
  const handleLogout = () => {
    Alert.alert(
      '确认退出',
      '确定要退出登录吗？',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '退出',
          style: 'destructive',
          onPress: async () => {
            setLoading(true);
            try {
              // 模拟退出登录API调用
              await new Promise(resolve => setTimeout(resolve, 500));
              logout();
              router.replace('/login');
            } catch (error) {
              Alert.alert('错误', '退出登录失败');
            } finally {
              setLoading(false);
            }
          }
        }
      ]
    );
  };

  // 渲染菜单项
  const renderMenuItem = (
    icon: string,
    title: string,
    subtitle?: string,
    onPress?: () => void,
    showArrow = true,
    danger = false
  ) => (
    <TouchableOpacity
      style={styles.menuItem}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View style={styles.menuItemLeft}>
        <Ionicons 
          name={icon as any} 
          size={20} 
          color={danger ? theme.colors.error : theme.colors.text} 
        />
        <View style={styles.menuItemText}>
          <Text style={[
            styles.menuItemTitle,
            danger && styles.menuItemTitleDanger
          ]}>
            {title}
          </Text>
          {subtitle && <Text style={styles.menuItemSubtitle}>{subtitle}</Text>}
        </View>
      </View>
      {showArrow && (
        <Ionicons name="chevron-forward" size={16} color={theme.colors.textSecondary} />
      )}
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* 头部导航 */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>设置</Text>
        <View style={styles.headerRight} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* 应用设置 */}
        <View style={styles.menuSection}>
          <Text style={styles.sectionTitle}>应用设置</Text>
          {renderMenuItem(
            'information-circle-outline',
            '关于我们',
            '版本信息和帮助',
            () => router.push('/settings/about')
          )}
          {renderMenuItem(
            'refresh-outline',
            '检查更新',
            '手动检查应用更新',
            handleCheckUpdate
          )}
        </View>

        {/* 账户管理 */}
        <View style={styles.menuSection}>
          <Text style={styles.sectionTitle}>账户管理</Text>
          {renderMenuItem(
            'trash-outline',
            '注销账户',
            '永久删除账户和所有数据',
            handleDeleteAccount,
            true,
            true
          )}
          {renderMenuItem(
            'log-out-outline',
            '退出登录',
            undefined,
            handleLogout,
            false,
            true
          )}
        </View>
      </ScrollView>

      {/* 全屏加载 */}
      {loading && <Loading type="fullscreen" text="处理中..." />}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: theme.spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
    backgroundColor: theme.colors.white,
  },
  backButton: {
    padding: theme.spacing.xs,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  headerRight: {
    width: 32,
  },
  content: {
    flex: 1,
  },
  menuSection: {
    backgroundColor: theme.colors.white,
    marginBottom: theme.spacing.md,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.textSecondary,
    paddingHorizontal: theme.spacing.lg,
    paddingTop: theme.spacing.lg,
    paddingBottom: theme.spacing.sm,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.divider,
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  menuItemText: {
    marginLeft: theme.spacing.md,
    flex: 1,
  },
  menuItemTitle: {
    fontSize: 16,
    color: theme.colors.text,
    marginBottom: 2,
  },
  menuItemTitleDanger: {
    color: theme.colors.error,
  },
  menuItemSubtitle: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
});
