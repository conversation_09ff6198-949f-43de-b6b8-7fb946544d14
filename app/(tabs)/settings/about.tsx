import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React from 'react';
import {
    Alert,
    Linking,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { theme } from '@/config/theme';

export default function AboutScreen() {
  // 应用信息
  const appInfo = {
    name: '攸家设备管理系统',
    version: 'V2.0.0',
    buildNumber: '2024121901',
    releaseDate: '2024年12月19日',
    description: '专业的智能设备管理平台，为物业管理人员提供高效便捷的设备运维解决方案。',
  };

  // 公司信息
  const companyInfo = {
    name: '攸家科技有限公司',
    website: 'https://www.youjia.com',
    email: '<EMAIL>',
    phone: '************',
    address: '北京市朝阳区科技园区创新大厦A座',
  };

  // 处理链接点击
  const handleLinkPress = async (url: string, title: string) => {
    try {
      const supported = await Linking.canOpenURL(url);
      if (supported) {
        await Linking.openURL(url);
      } else {
        Alert.alert('提示', `无法打开${title}`);
      }
    } catch (error) {
      Alert.alert('错误', `打开${title}失败`);
    }
  };

  // 处理邮件
  const handleEmailPress = () => {
    handleLinkPress(`mailto:${companyInfo.email}`, '邮箱');
  };

  // 处理电话
  const handlePhonePress = () => {
    Alert.alert(
      '联系客服',
      `客服电话：${companyInfo.phone}`,
      [
        { text: '取消', style: 'cancel' },
        {
          text: '拨打电话',
          onPress: () => handleLinkPress(`tel:${companyInfo.phone}`, '电话')
        }
      ]
    );
  };

  // 处理网站
  const handleWebsitePress = () => {
    handleLinkPress(companyInfo.website, '官网');
  };

  // 渲染信息项
  const renderInfoItem = (label: string, value: string, onPress?: () => void) => (
    <TouchableOpacity
      style={styles.infoItem}
      onPress={onPress}
      activeOpacity={onPress ? 0.7 : 1}
      disabled={!onPress}
    >
      <Text style={styles.infoLabel}>{label}</Text>
      <View style={styles.infoValueContainer}>
        <Text style={[
          styles.infoValue,
          onPress && styles.infoValueLink
        ]}>
          {value}
        </Text>
        {onPress && (
          <Ionicons name="open-outline" size={16} color={theme.colors.primary} />
        )}
      </View>
    </TouchableOpacity>
  );

  // 渲染链接项
  const renderLinkItem = (icon: string, title: string, onPress: () => void) => (
    <TouchableOpacity
      style={styles.linkItem}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <Ionicons name={icon as any} size={20} color={theme.colors.primary} />
      <Text style={styles.linkText}>{title}</Text>
      <Ionicons name="chevron-forward" size={16} color={theme.colors.textSecondary} />
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* 头部导航 */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>关于我们</Text>
        <View style={styles.headerRight} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* 应用Logo和名称 */}
        <View style={styles.logoSection}>
          <View style={styles.logoContainer}>
            <Ionicons name="home" size={48} color={theme.colors.white} />
          </View>
          <Text style={styles.appName}>{appInfo.name}</Text>
          <Text style={styles.appDescription}>{appInfo.description}</Text>
        </View>

        {/* 版本信息 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>版本信息</Text>
          {renderInfoItem('当前版本', appInfo.version)}
          {renderInfoItem('构建版本', appInfo.buildNumber)}
          {renderInfoItem('发布日期', appInfo.releaseDate)}
        </View>

        {/* 公司信息 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>公司信息</Text>
          {renderInfoItem('公司名称', companyInfo.name)}
          {renderInfoItem('公司地址', companyInfo.address)}
        </View>

        {/* 联系方式 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>联系我们</Text>
          {renderLinkItem('call-outline', companyInfo.phone, handlePhonePress)}
          {renderLinkItem('mail-outline', companyInfo.email, handleEmailPress)}
          {renderLinkItem('globe-outline', '官方网站', handleWebsitePress)}
        </View>

        {/* 法律信息 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>法律信息</Text>
          {renderLinkItem(
            'document-text-outline',
            '服务协议',
            () => Alert.alert('提示', '服务协议功能开发中')
          )}
          {renderLinkItem(
            'shield-checkmark-outline',
            '隐私政策',
            () => Alert.alert('提示', '隐私政策功能开发中')
          )}
        </View>

        {/* 版权信息 */}
        <View style={styles.copyrightSection}>
          <Text style={styles.copyrightText}>
            © 2024 {companyInfo.name}
          </Text>
          <Text style={styles.copyrightText}>
            保留所有权利
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: theme.spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
    backgroundColor: theme.colors.white,
  },
  backButton: {
    padding: theme.spacing.xs,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  headerRight: {
    width: 32,
  },
  content: {
    flex: 1,
  },
  logoSection: {
    alignItems: 'center',
    paddingVertical: theme.spacing.xl,
    backgroundColor: theme.colors.white,
    marginBottom: theme.spacing.md,
  },
  logoContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: theme.colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: theme.spacing.lg,
  },
  appName: {
    fontSize: 20,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
  },
  appDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
    paddingHorizontal: theme.spacing.lg,
  },
  section: {
    backgroundColor: theme.colors.white,
    marginBottom: theme.spacing.md,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.textSecondary,
    paddingHorizontal: theme.spacing.lg,
    paddingTop: theme.spacing.lg,
    paddingBottom: theme.spacing.sm,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.divider,
  },
  infoLabel: {
    fontSize: 16,
    color: theme.colors.text,
  },
  infoValueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.sm,
  },
  infoValue: {
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  infoValueLink: {
    color: theme.colors.primary,
  },
  linkItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.divider,
  },
  linkText: {
    fontSize: 16,
    color: theme.colors.text,
    marginLeft: theme.spacing.md,
    flex: 1,
  },
  copyrightSection: {
    alignItems: 'center',
    paddingVertical: theme.spacing.xl,
    backgroundColor: theme.colors.white,
  },
  copyrightText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginBottom: 4,
  },
});
