import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity, 
  Alert 
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';

import { theme } from '@/config/theme';
import { AvatarUpload } from '@/components/business';
import { Input, Button, Loading } from '@/components/ui';
import { useAuthStore } from '@/stores/auth';

export default function EditProfileScreen() {
  const { user, setUser } = useAuthStore();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    avatar: user?.avatar || '',
    realName: user?.realName || '',
    nickname: user?.nickname || '',
  });

  // 表单验证
  const validateForm = () => {
    if (!formData.realName.trim()) {
      Alert.alert('提示', '请输入真实姓名');
      return false;
    }
    
    if (formData.realName.trim().length < 2) {
      Alert.alert('提示', '姓名至少需要2个字符');
      return false;
    }
    
    if (formData.nickname && formData.nickname.trim().length > 20) {
      Alert.alert('提示', '昵称不能超过20个字符');
      return false;
    }
    
    return true;
  };

  // 保存个人信息
  const handleSave = async () => {
    if (!validateForm()) return;

    setLoading(true);
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // 更新用户信息
      const updatedUser = {
        ...user!,
        avatar: formData.avatar,
        realName: formData.realName.trim(),
        nickname: formData.nickname.trim() || undefined,
        updateTime: new Date().toISOString(),
      };
      
      setUser(updatedUser);
      
      Alert.alert('成功', '个人信息更新成功', [
        {
          text: '确定',
          onPress: () => router.back(),
        }
      ]);
    } catch (error) {
      Alert.alert('错误', '更新个人信息失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理头像变化
  const handleAvatarChange = (avatarUrl: string) => {
    setFormData(prev => ({ ...prev, avatar: avatarUrl }));
  };

  // 处理输入变化
  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* 头部导航 */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>编辑个人信息</Text>
        <View style={styles.headerRight} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* 头像上传 */}
        <View style={styles.avatarSection}>
          <AvatarUpload
            avatar={formData.avatar}
            size={100}
            onAvatarChange={handleAvatarChange}
          />
          <Text style={styles.avatarHint}>点击更换头像</Text>
        </View>

        {/* 表单区域 */}
        <View style={styles.formSection}>
          {/* 真实姓名 */}
          <View style={styles.inputGroup}>
            <Input
              label="真实姓名"
              placeholder="请输入真实姓名"
              value={formData.realName}
              onChangeText={(value) => handleInputChange('realName', value)}
              required
              maxLength={20}
              validator={(value) => {
                if (!value.trim()) return '请输入真实姓名';
                if (value.trim().length < 2) return '姓名至少需要2个字符';
                return null;
              }}
            />
          </View>

          {/* 昵称 */}
          <View style={styles.inputGroup}>
            <Input
              label="昵称"
              placeholder="请输入昵称（可选）"
              value={formData.nickname}
              onChangeText={(value) => handleInputChange('nickname', value)}
              maxLength={20}
              helperText="昵称将在部分场景下显示"
              validator={(value) => {
                if (value && value.trim().length > 20) return '昵称不能超过20个字符';
                return null;
              }}
            />
          </View>

          {/* 手机号（只读） */}
          <View style={styles.inputGroup}>
            <Input
              label="手机号"
              value={user?.phone || ''}
              disabled
              helperText="如需修改手机号，请在个人信息页面操作"
            />
          </View>
        </View>

        {/* 保存按钮 */}
        <View style={styles.buttonSection}>
          <Button
            onPress={handleSave}
            loading={loading}
            loadingText="保存中..."
            fullWidth
          >
            保存
          </Button>
        </View>
      </ScrollView>

      {/* 全屏加载 */}
      {loading && <Loading type="fullscreen" text="保存中..." />}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: theme.spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
    backgroundColor: theme.colors.white,
  },
  backButton: {
    padding: theme.spacing.xs,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  headerRight: {
    width: 32,
  },
  content: {
    flex: 1,
  },
  avatarSection: {
    alignItems: 'center',
    paddingVertical: theme.spacing.xl,
    backgroundColor: theme.colors.white,
    marginBottom: theme.spacing.md,
  },
  avatarHint: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginTop: theme.spacing.sm,
  },
  formSection: {
    backgroundColor: theme.colors.white,
    padding: theme.spacing.lg,
    marginBottom: theme.spacing.md,
  },
  inputGroup: {
    marginBottom: theme.spacing.lg,
  },
  buttonSection: {
    padding: theme.spacing.lg,
    backgroundColor: theme.colors.white,
  },
});
