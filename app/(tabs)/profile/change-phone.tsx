import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React, { useState } from 'react';
import {
    Alert,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { Button, Input, Loading } from '@/components/ui';
import { theme } from '@/config/theme';
import { useAuthStore } from '@/stores/auth';

type Step = 'verify-old' | 'input-new' | 'verify-new' | 'complete';

export default function ChangePhoneScreen() {
  const { user, setUser } = useAuthStore();
  const [currentStep, setCurrentStep] = useState<Step>('verify-old');
  const [loading, setLoading] = useState(false);
  const [countdown, setCountdown] = useState(0);
  
  const [formData, setFormData] = useState({
    oldPhoneCode: '',
    newPhone: '',
    newPhoneCode: '',
  });

  // 验证手机号格式
  const validatePhone = (phone: string) => {
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone);
  };

  // 发送验证码
  const sendVerificationCode = async (phone: string, isOldPhone = false) => {
    if (!isOldPhone && !validatePhone(phone)) {
      Alert.alert('提示', '请输入正确的手机号');
      return false;
    }

    setLoading(true);
    
    try {
      // 模拟发送验证码
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 开始倒计时
      setCountdown(60);
      const timer = setInterval(() => {
        setCountdown(prev => {
          if (prev <= 1) {
            clearInterval(timer);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
      
      Alert.alert('成功', `验证码已发送至 ${phone}`);
      return true;
    } catch (error) {
      Alert.alert('错误', '发送验证码失败');
      return false;
    } finally {
      setLoading(false);
    }
  };

  // 验证旧手机号
  const handleVerifyOldPhone = async () => {
    if (!formData.oldPhoneCode.trim()) {
      Alert.alert('提示', '请输入验证码');
      return;
    }

    if (formData.oldPhoneCode.length !== 4) {
      Alert.alert('提示', '请输入4位验证码');
      return;
    }

    setLoading(true);
    
    try {
      // 模拟验证码验证
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 模拟验证失败的情况（20%概率）
      if (Math.random() < 0.2) {
        Alert.alert('错误', '验证码错误');
        return;
      }
      
      setCurrentStep('input-new');
    } catch (error) {
      Alert.alert('错误', '验证失败');
    } finally {
      setLoading(false);
    }
  };

  // 输入新手机号并发送验证码
  const handleInputNewPhone = async () => {
    if (!validatePhone(formData.newPhone)) {
      Alert.alert('提示', '请输入正确的手机号');
      return;
    }

    if (formData.newPhone === user?.phone) {
      Alert.alert('提示', '新手机号不能与当前手机号相同');
      return;
    }

    const success = await sendVerificationCode(formData.newPhone);
    if (success) {
      setCurrentStep('verify-new');
    }
  };

  // 验证新手机号
  const handleVerifyNewPhone = async () => {
    if (!formData.newPhoneCode.trim()) {
      Alert.alert('提示', '请输入验证码');
      return;
    }

    if (formData.newPhoneCode.length !== 4) {
      Alert.alert('提示', '请输入4位验证码');
      return;
    }

    setLoading(true);
    
    try {
      // 模拟验证码验证
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // 模拟验证失败的情况（20%概率）
      if (Math.random() < 0.2) {
        Alert.alert('错误', '验证码错误');
        return;
      }
      
      // 更新用户手机号
      const updatedUser = {
        ...user!,
        phone: formData.newPhone,
        updateTime: new Date().toISOString(),
      };
      
      setUser(updatedUser);
      setCurrentStep('complete');
      
      Alert.alert('成功', '手机号修改成功', [
        {
          text: '确定',
          onPress: () => router.back(),
        }
      ]);
    } catch (error) {
      Alert.alert('错误', '修改手机号失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理输入变化
  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  // 渲染步骤内容
  const renderStepContent = () => {
    switch (currentStep) {
      case 'verify-old':
        return (
          <View style={styles.stepContent}>
            <Text style={styles.stepTitle}>验证当前手机号</Text>
            <Text style={styles.stepDescription}>
              为了保护您的账户安全，请先验证当前手机号 {user?.phone}
            </Text>
            
            <View style={styles.inputGroup}>
              <Input
                label="验证码"
                placeholder="请输入4位验证码"
                value={formData.oldPhoneCode}
                onChangeText={(value) => handleInputChange('oldPhoneCode', value)}
                keyboardType="numeric"
                maxLength={4}
                rightIcon={
                  <TouchableOpacity
                    style={styles.sendCodeButton}
                    onPress={() => sendVerificationCode(user?.phone || '')}
                    disabled={countdown > 0 || loading}
                    activeOpacity={0.7}
                  >
                    <Text style={[
                      styles.sendCodeText,
                      (countdown > 0 || loading) && styles.sendCodeTextDisabled
                    ]}>
                      {countdown > 0 ? `${countdown}s` : '获取验证码'}
                    </Text>
                  </TouchableOpacity>
                }
              />
            </View>
            
            <Button
              onPress={handleVerifyOldPhone}
              loading={loading}
              fullWidth
            >
              下一步
            </Button>
          </View>
        );

      case 'input-new':
        return (
          <View style={styles.stepContent}>
            <Text style={styles.stepTitle}>输入新手机号</Text>
            <Text style={styles.stepDescription}>
              请输入您要绑定的新手机号
            </Text>
            
            <View style={styles.inputGroup}>
              <Input
                label="新手机号"
                placeholder="请输入新手机号"
                value={formData.newPhone}
                onChangeText={(value) => handleInputChange('newPhone', value)}
                keyboardType="phone-pad"
                maxLength={11}
                validator={(value) => {
                  if (!value) return '请输入手机号';
                  if (!validatePhone(value)) return '请输入正确的手机号';
                  if (value === user?.phone) return '新手机号不能与当前手机号相同';
                  return null;
                }}
              />
            </View>
            
            <Button
              onPress={handleInputNewPhone}
              loading={loading}
              fullWidth
            >
              发送验证码
            </Button>
          </View>
        );

      case 'verify-new':
        return (
          <View style={styles.stepContent}>
            <Text style={styles.stepTitle}>验证新手机号</Text>
            <Text style={styles.stepDescription}>
              验证码已发送至 {formData.newPhone}，请查收
            </Text>
            
            <View style={styles.inputGroup}>
              <Input
                label="验证码"
                placeholder="请输入4位验证码"
                value={formData.newPhoneCode}
                onChangeText={(value) => handleInputChange('newPhoneCode', value)}
                keyboardType="numeric"
                maxLength={4}
                rightIcon={
                  <TouchableOpacity
                    style={styles.sendCodeButton}
                    onPress={() => sendVerificationCode(formData.newPhone)}
                    disabled={countdown > 0 || loading}
                    activeOpacity={0.7}
                  >
                    <Text style={[
                      styles.sendCodeText,
                      (countdown > 0 || loading) && styles.sendCodeTextDisabled
                    ]}>
                      {countdown > 0 ? `${countdown}s` : '重新发送'}
                    </Text>
                  </TouchableOpacity>
                }
              />
            </View>
            
            <Button
              onPress={handleVerifyNewPhone}
              loading={loading}
              fullWidth
            >
              完成修改
            </Button>
          </View>
        );

      default:
        return null;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* 头部导航 */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>修改手机号</Text>
        <View style={styles.headerRight} />
      </View>

      {/* 步骤指示器 */}
      <View style={styles.stepIndicator}>
        <View style={styles.stepDots}>
          {['verify-old', 'input-new', 'verify-new'].map((step, index) => (
            <View
              key={step}
              style={[
                styles.stepDot,
                ['verify-old', 'input-new', 'verify-new'].indexOf(currentStep) >= index && styles.stepDotActive
              ]}
            />
          ))}
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderStepContent()}
      </ScrollView>

      {/* 全屏加载 */}
      {loading && <Loading type="fullscreen" text="处理中..." />}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: theme.spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
    backgroundColor: theme.colors.white,
  },
  backButton: {
    padding: theme.spacing.xs,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  headerRight: {
    width: 32,
  },
  stepIndicator: {
    backgroundColor: theme.colors.white,
    paddingVertical: theme.spacing.lg,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.divider,
  },
  stepDots: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.sm,
  },
  stepDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: theme.colors.border,
  },
  stepDotActive: {
    backgroundColor: theme.colors.primary,
  },
  content: {
    flex: 1,
  },
  stepContent: {
    backgroundColor: theme.colors.white,
    padding: theme.spacing.lg,
    margin: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
  },
  stepTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
  },
  stepDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    lineHeight: 20,
    marginBottom: theme.spacing.xl,
  },
  inputGroup: {
    marginBottom: theme.spacing.xl,
  },
  sendCodeButton: {
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
  },
  sendCodeText: {
    fontSize: 14,
    color: theme.colors.primary,
    fontWeight: '500',
  },
  sendCodeTextDisabled: {
    color: theme.colors.textSecondary,
  },
});
