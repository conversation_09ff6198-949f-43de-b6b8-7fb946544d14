import { Stack } from 'expo-router';

import { theme } from '@/config/theme';

export default function ProfileLayout() {
  return (
    <Stack
      screenOptions={{
        headerShown: false,
        contentStyle: { backgroundColor: theme.colors.background },
      }}
    >
      <Stack.Screen name='index' />
      <Stack.Screen name='edit-profile' />
      <Stack.Screen name='change-phone' />
      <Stack.Screen name='settings' />
    </Stack>
  );
}
