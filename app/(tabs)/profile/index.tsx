import React from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity, 
  Alert 
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';

import { theme } from '@/config/theme';
import { AvatarUpload } from '@/components/business';
import { useAuthStore } from '@/stores/auth';

export default function ProfileScreen() {
  const { user, logout } = useAuthStore();

  // 处理退出登录
  const handleLogout = () => {
    Alert.alert(
      '确认退出',
      '确定要退出登录吗？',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '退出',
          style: 'destructive',
          onPress: async () => {
            try {
              // 模拟退出登录API调用
              await new Promise(resolve => setTimeout(resolve, 500));
              logout();
              router.replace('/login');
            } catch (error) {
              Alert.alert('错误', '退出登录失败');
            }
          }
        }
      ]
    );
  };

  // 渲染菜单项
  const renderMenuItem = (
    icon: string,
    title: string,
    subtitle?: string,
    onPress?: () => void,
    showArrow = true
  ) => (
    <TouchableOpacity
      style={styles.menuItem}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View style={styles.menuItemLeft}>
        <Ionicons name={icon as any} size={20} color={theme.colors.text} />
        <View style={styles.menuItemText}>
          <Text style={styles.menuItemTitle}>{title}</Text>
          {subtitle && <Text style={styles.menuItemSubtitle}>{subtitle}</Text>}
        </View>
      </View>
      {showArrow && (
        <Ionicons name="chevron-forward" size={16} color={theme.colors.textSecondary} />
      )}
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>我的</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* 用户信息卡片 */}
        <View style={styles.userCard}>
          <View style={styles.userInfo}>
            <AvatarUpload
              avatar={user?.avatar}
              size={60}
              editable={false}
            />
            <View style={styles.userDetails}>
              <Text style={styles.userName}>{user?.realName || '未设置姓名'}</Text>
              <Text style={styles.userPhone}>{user?.phone || '未绑定手机号'}</Text>
              {user?.nickname && (
                <Text style={styles.userNickname}>昵称：{user.nickname}</Text>
              )}
            </View>
          </View>
          <TouchableOpacity
            style={styles.editButton}
            onPress={() => router.push('/profile/edit-profile')}
            activeOpacity={0.7}
          >
            <Ionicons name="create-outline" size={20} color={theme.colors.primary} />
          </TouchableOpacity>
        </View>

        {/* 个人信息管理 */}
        <View style={styles.menuSection}>
          <Text style={styles.sectionTitle}>个人信息</Text>
          {renderMenuItem(
            'person-outline',
            '编辑个人信息',
            '修改头像、姓名、昵称',
            () => router.push('/profile/edit-profile')
          )}
          {renderMenuItem(
            'call-outline',
            '修改手机号',
            user?.phone || '未绑定',
            () => router.push('/profile/change-phone')
          )}
        </View>

        {/* 应用设置 */}
        <View style={styles.menuSection}>
          <Text style={styles.sectionTitle}>应用设置</Text>
          {renderMenuItem(
            'settings-outline',
            '设置',
            '应用设置和偏好',
            () => router.push('/settings')
          )}
          {renderMenuItem(
            'information-circle-outline',
            '关于我们',
            '版本信息和帮助',
            () => router.push('/settings/about')
          )}
        </View>

        {/* 账户管理 */}
        <View style={styles.menuSection}>
          <Text style={styles.sectionTitle}>账户管理</Text>
          {renderMenuItem(
            'log-out-outline',
            '退出登录',
            undefined,
            handleLogout,
            false
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    padding: theme.spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
    backgroundColor: theme.colors.white,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  content: {
    flex: 1,
  },
  userCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: theme.spacing.lg,
    backgroundColor: theme.colors.white,
    marginBottom: theme.spacing.md,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  userDetails: {
    marginLeft: theme.spacing.md,
    flex: 1,
  },
  userName: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 4,
  },
  userPhone: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 2,
  },
  userNickname: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  editButton: {
    padding: theme.spacing.sm,
  },
  menuSection: {
    backgroundColor: theme.colors.white,
    marginBottom: theme.spacing.md,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.textSecondary,
    paddingHorizontal: theme.spacing.lg,
    paddingTop: theme.spacing.lg,
    paddingBottom: theme.spacing.sm,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.divider,
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  menuItemText: {
    marginLeft: theme.spacing.md,
    flex: 1,
  },
  menuItemTitle: {
    fontSize: 16,
    color: theme.colors.text,
    marginBottom: 2,
  },
  menuItemSubtitle: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
});
