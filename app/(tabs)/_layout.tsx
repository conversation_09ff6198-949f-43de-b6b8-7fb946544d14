import { Ionicons } from '@expo/vector-icons';
import { Tabs } from 'expo-router';
import { Platform } from 'react-native';

import { theme } from '@/config/theme';
import { PERMISSIONS, usePermissions } from '@/utils/permissions';

export default function TabLayout() {
  const { hasPermission } = usePermissions();

  return (
    <Tabs
      screenOptions={{
        headerShown: false,
        tabBarActiveTintColor: theme.colors.primary,
        tabBarInactiveTintColor: theme.colors.textSecondary,
        tabBarStyle: {
          backgroundColor: theme.colors.white,
          borderTopColor: theme.colors.border,
          borderTopWidth: 1,
          height: Platform.OS === 'ios' ? 88 : 68,
          paddingBottom: Platform.OS === 'ios' ? 28 : 8,
          paddingTop: 8,
          shadowColor: theme.colors.black,
          shadowOffset: {
            width: 0,
            height: -2,
          },
          shadowOpacity: 0.1,
          shadowRadius: 4,
          elevation: 8,
        },
        tabBarLabelStyle: {
          fontSize: 11,
          fontWeight: '600',
        },
        tabBarIconStyle: {
          marginBottom: 2,
        },
      }}
    >
      {/* 运维管理 */}
      <Tabs.Screen
        name='operations'
        options={{
          title: '运维管理',
          tabBarIcon: ({ color, size, focused }) => (
            <Ionicons
              name={focused ? 'construct' : 'construct-outline'}
              size={size}
              color={color}
            />
          ),
          href: hasPermission(PERMISSIONS.DEVICE_REGISTER) ||
                hasPermission(PERMISSIONS.DEVICE_DEBUG) ||
                hasPermission(PERMISSIONS.DEVICE_UNBIND) ||
                hasPermission(PERMISSIONS.DEVICE_CONTROL)
            ? '/operations'
            : null,
        }}
      />

      {/* 用户管理 */}
      <Tabs.Screen
        name='users'
        options={{
          title: '用户管理',
          tabBarIcon: ({ color, size, focused }) => (
            <Ionicons
              name={focused ? 'people' : 'people-outline'}
              size={size}
              color={color}
            />
          ),
          href: hasPermission(PERMISSIONS.RESIDENTS_VIEW) ||
                hasPermission(PERMISSIONS.STAFF_VIEW)
            ? '/users'
            : null,
        }}
      />

      {/* 社区管理 */}
      <Tabs.Screen
        name='community'
        options={{
          title: '社区管理',
          tabBarIcon: ({ color, size, focused }) => (
            <Ionicons
              name={focused ? 'chatbubbles' : 'chatbubbles-outline'}
              size={size}
              color={color}
            />
          ),
          href: hasPermission(PERMISSIONS.COMMUNITY_MANAGE)
            ? '/community'
            : null,
        }}
      />

      {/* 个人中心 */}
      <Tabs.Screen
        name='profile'
        options={{
          title: '我的',
          tabBarIcon: ({ color, size, focused }) => (
            <Ionicons
              name={focused ? 'person-circle' : 'person-circle-outline'}
              size={size}
              color={color}
            />
          ),
        }}
      />

      {/* 隐藏的路由 - 这些页面不应该在Tab中显示 */}
      <Tabs.Screen name="device-register" options={{ href: null }} />
      <Tabs.Screen name="device-debug" options={{ href: null }} />
      <Tabs.Screen name="device-unbind" options={{ href: null }} />
      <Tabs.Screen name="device-control" options={{ href: null }} />
      <Tabs.Screen name="residents" options={{ href: null }} />
      <Tabs.Screen name="staff" options={{ href: null }} />
      <Tabs.Screen name="settings" options={{ href: null }} />
      <Tabs.Screen name="index" options={{ href: null }} />
    </Tabs>
  );
}
