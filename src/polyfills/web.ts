/**
 * Web端polyfills - 修复import.meta和其他Web端兼容性问题
 */

// 修复import.meta在Web端的兼容性问题
if (typeof globalThis !== 'undefined' && typeof globalThis.import === 'undefined') {
  // 为Web环境提供import.meta的polyfill
  Object.defineProperty(globalThis, 'import', {
    value: {
      meta: {
        url: typeof window !== 'undefined' ? window.location.href : '',
        env: process.env,
      },
    },
    writable: false,
    configurable: false,
  });
}

// 确保全局对象存在
if (typeof global === 'undefined') {
  (globalThis as any).global = globalThis;
}

// 修复Web端的模块系统问题
if (typeof window !== 'undefined') {
  // 确保__DEV__变量存在
  if (typeof (window as any).__DEV__ === 'undefined') {
    (window as any).__DEV__ = process.env.NODE_ENV === 'development';
  }

  // 修复可能的模块加载问题
  if (typeof (window as any).require === 'undefined') {
    (window as any).require = (id: string) => {
      throw new Error(`Cannot require module "${id}" in web environment`);
    };
  }

  // 修复React Native动画模块在Web端的兼容性问题
  // 禁用原生动画驱动，避免useNativeDriver警告
  if (typeof (window as any).requestAnimationFrame !== 'undefined') {
    // 确保动画相关的全局变量存在
    (window as any).cancelAnimationFrame =
      (window as any).cancelAnimationFrame ||
      ((id: number) => clearTimeout(id));
  }

  // 模拟原生模块，避免动画警告
  if (typeof (window as any).nativeAnimatedModule === 'undefined') {
    (window as any).nativeAnimatedModule = null;
  }

  // 设置React Native Web的动画配置
  if (typeof (window as any).__REACT_NATIVE_WEB__ === 'undefined') {
    (window as any).__REACT_NATIVE_WEB__ = {
      animation: {
        useNativeDriver: false, // 强制禁用原生动画驱动
      },
    };
  }
}

export { };

