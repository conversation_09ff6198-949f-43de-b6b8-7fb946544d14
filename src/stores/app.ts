import AsyncStorage from '@react-native-async-storage/async-storage';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

export type ThemeMode = 'light' | 'dark' | 'system';

export interface ToastMessage {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number;
  action?: {
    label: string;
    onPress: () => void;
  };
}

interface AppState {
  // 主题相关
  themeMode: ThemeMode;
  isDarkMode: boolean;

  // 加载状态
  isGlobalLoading: boolean;
  loadingText: string;

  // 错误状态
  globalError: string | null;

  // Toast 消息
  toasts: ToastMessage[];

  // 网络状态
  isOnline: boolean;

  // 应用状态
  isAppActive: boolean;
  isFirstLaunch: boolean;
  appVersion: string;

  // 操作方法
  setThemeMode: (mode: ThemeMode) => void;
  setIsDarkMode: (isDark: boolean) => void;

  setGlobalLoading: (loading: boolean, text?: string) => void;

  setGlobalError: (error: string | null) => void;
  clearGlobalError: () => void;

  addToast: (toast: Omit<ToastMessage, 'id'>) => void;
  removeToast: (id: string) => void;
  clearToasts: () => void;

  setIsOnline: (online: boolean) => void;
  setIsAppActive: (active: boolean) => void;
  setIsFirstLaunch: (isFirst: boolean) => void;
  setAppVersion: (version: string) => void;
}

export const useAppStore = create<AppState>()(
  persist(
    (set, get) => ({
      // 初始状态
      themeMode: 'system',
      isDarkMode: false,
      isGlobalLoading: false,
      loadingText: '',
      globalError: null,
      toasts: [],
      isOnline: true,
      isAppActive: true,
      isFirstLaunch: true,
      appVersion: '1.0.0',

      // 主题相关方法
      setThemeMode: (mode: ThemeMode) => {
        set({ themeMode: mode });
      },

      setIsDarkMode: (isDark: boolean) => {
        set({ isDarkMode: isDark });
      },

      // 加载状态方法
      setGlobalLoading: (loading: boolean, text = '') => {
        set({
          isGlobalLoading: loading,
          loadingText: loading ? text : '',
        });
      },

      // 错误状态方法
      setGlobalError: (error: string | null) => {
        set({ globalError: error });
      },

      clearGlobalError: () => {
        set({ globalError: null });
      },

      // Toast 方法
      addToast: (toast: Omit<ToastMessage, 'id'>) => {
        const id = Date.now().toString();
        const newToast: ToastMessage = {
          ...toast,
          id,
          duration: toast.duration ?? 3000,
        };

        set(state => ({
          toasts: [...state.toasts, newToast],
        }));

        // 自动移除 Toast
        if (newToast.duration && newToast.duration > 0) {
          setTimeout(() => {
            get().removeToast(id);
          }, newToast.duration);
        }
      },

      removeToast: (id: string) => {
        set(state => ({
          toasts: state.toasts.filter(toast => toast.id !== id),
        }));
      },

      clearToasts: () => {
        set({ toasts: [] });
      },

      // 网络和应用状态方法
      setIsOnline: (online: boolean) => {
        set({ isOnline: online });
      },

      setIsAppActive: (active: boolean) => {
        set({ isAppActive: active });
      },

      setIsFirstLaunch: (isFirst: boolean) => {
        set({ isFirstLaunch: isFirst });
      },

      setAppVersion: (version: string) => {
        set({ appVersion: version });
      },
    }),
    {
      name: 'app-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: state => ({
        themeMode: state.themeMode,
        isFirstLaunch: state.isFirstLaunch,
        appVersion: state.appVersion,
      }),
    }
  )
);
