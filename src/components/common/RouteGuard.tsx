import { Ionicons } from '@expo/vector-icons';
import { Redirect } from 'expo-router';
import React from 'react';
import { StyleSheet, Text, View } from 'react-native';

import { Button } from '@/components/ui';
import { theme } from '@/config/theme';
import { useAuthStore } from '@/stores/auth';

interface RouteGuardProps {
  children: React.ReactNode;
  requiredPermissions?: string[];
  requiredRoles?: string[];
  requireAuth?: boolean;
  fallbackRoute?: string;
  showNoPermissionUI?: boolean;
}

/**
 * 路由守卫组件
 * 用于保护需要特定权限或角色的路由
 */
export function RouteGuard({
  children,
  requiredPermissions = [],
  requiredRoles = [],
  requireAuth = true,
  fallbackRoute = '/(auth)/login',
  showNoPermissionUI = true,
}: RouteGuardProps) {
  const { isAuthenticated, isLoading, hasAnyPermission, hasRole } =
    useAuthStore();

  // 创建hasAnyRole函数
  const hasAnyRole = (roles: string[]): boolean => {
    return roles.some(role => hasRole(role));
  };

  // 如果正在加载认证状态，显示加载界面
  if (isLoading) {
    return (
      <View style={styles.container}>
        <Text style={styles.loadingText}>正在验证权限...</Text>
      </View>
    );
  }

  // 如果需要认证但用户未登录，重定向到登录页
  if (requireAuth && !isAuthenticated) {
    return <Redirect href={fallbackRoute as any} />;
  }

  // 检查权限
  const hasRequiredPermissions =
    requiredPermissions.length === 0 || hasAnyPermission(requiredPermissions);

  // 检查角色
  const hasRequiredRoles =
    requiredRoles.length === 0 || hasAnyRole(requiredRoles);

  // 如果没有所需权限或角色
  if (!hasRequiredPermissions || !hasRequiredRoles) {
    if (showNoPermissionUI) {
      return <NoPermissionScreen />;
    } else {
      return <Redirect href={fallbackRoute as any} />;
    }
  }

  // 权限检查通过，渲染子组件
  return <>{children}</>;
}

/**
 * 无权限访问页面
 */
function NoPermissionScreen() {
  return (
    <View style={styles.noPermissionContainer}>
      <View style={styles.noPermissionContent}>
        <Ionicons
          name='lock-closed-outline'
          size={64}
          color={theme.colors.textSecondary}
        />
        <Text style={styles.noPermissionTitle}>访问受限</Text>
        <Text style={styles.noPermissionText}>
          您没有访问此页面的权限，请联系管理员获取相应权限。
        </Text>
        <Button
          variant='outline'
          onPress={() => {
            // TODO: 可以添加联系管理员的功能
          }}
          containerStyle={styles.contactButton}
        >
          联系管理员
        </Button>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  noPermissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
    padding: theme.spacing.lg,
  },
  noPermissionContent: {
    alignItems: 'center',
    maxWidth: 300,
  },
  noPermissionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginTop: theme.spacing.lg,
    marginBottom: theme.spacing.md,
  },
  noPermissionText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: theme.spacing.xl,
  },
  contactButton: {
    minWidth: 120,
  },
});
