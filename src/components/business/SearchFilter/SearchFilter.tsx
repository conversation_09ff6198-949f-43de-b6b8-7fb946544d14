import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { Input, Modal } from '@/components/ui';
import { LocationPicker, LocationPath } from '@/components/business';
import { theme } from '@/config/theme';
import { UserSearchParams } from '@/types/user';
import { mockLocationData } from '@/data/mockData';

interface SearchFilterProps {
  /** 搜索参数 */
  searchParams: UserSearchParams;
  /** 搜索参数变化回调 */
  onSearchParamsChange: (params: UserSearchParams) => void;
  /** 占位符文本 */
  placeholder?: string;
}

export function SearchFilter({
  searchParams,
  onSearchParamsChange,
  placeholder = '搜索姓名或手机号',
}: SearchFilterProps) {
  const [showLocationPicker, setShowLocationPicker] = useState(false);

  // 处理搜索关键词变化
  const handleKeywordChange = (keyword: string) => {
    onSearchParamsChange({
      ...searchParams,
      keyword,
    });
  };

  // 处理位置选择
  const handleLocationSelect = (path: LocationPath[]) => {
    const newParams: UserSearchParams = {
      ...searchParams,
      community: undefined,
      building: undefined,
      unit: undefined,
      floor: undefined,
      room: undefined,
    };

    // 根据选择的路径设置对应的位置信息
    if (path.length > 0) newParams.community = path[0].name;
    if (path.length > 1) newParams.building = path[1].name;
    if (path.length > 2) newParams.unit = path[2].name;
    if (path.length > 3) newParams.floor = path[3].name;
    if (path.length > 4) newParams.room = path[4].name;

    onSearchParamsChange(newParams);
    setShowLocationPicker(false);
  };

  // 清除筛选条件
  const handleClearFilter = () => {
    onSearchParamsChange({
      keyword: searchParams.keyword,
      page: 1,
      pageSize: searchParams.pageSize,
    });
  };

  // 获取当前位置路径
  const getCurrentLocationPath = (): LocationPath[] => {
    const path: LocationPath[] = [];
    if (searchParams.community) {
      path.push({ id: 'community', name: searchParams.community });
    }
    if (searchParams.building) {
      path.push({ id: 'building', name: searchParams.building });
    }
    if (searchParams.unit) {
      path.push({ id: 'unit', name: searchParams.unit });
    }
    if (searchParams.floor) {
      path.push({ id: 'floor', name: searchParams.floor });
    }
    if (searchParams.room) {
      path.push({ id: 'room', name: searchParams.room });
    }
    return path;
  };

  // 获取位置显示文本
  const getLocationText = () => {
    const parts = [];
    if (searchParams.community) parts.push(searchParams.community);
    if (searchParams.building) parts.push(searchParams.building);
    if (searchParams.unit) parts.push(searchParams.unit);
    if (searchParams.floor) parts.push(searchParams.floor);
    if (searchParams.room) parts.push(searchParams.room);
    
    return parts.length > 0 ? parts.join(' ') : '选择位置';
  };

  // 检查是否有筛选条件
  const hasFilter = () => {
    return !!(
      searchParams.community ||
      searchParams.building ||
      searchParams.unit ||
      searchParams.floor ||
      searchParams.room
    );
  };

  return (
    <View style={styles.container}>
      {/* 搜索框 */}
      <View style={styles.searchContainer}>
        <Input
          placeholder={placeholder}
          value={searchParams.keyword || ''}
          onChangeText={handleKeywordChange}
          leftIcon={
            <Ionicons name="search" size={20} color={theme.colors.textSecondary} />
          }
          clearable
          containerStyle={styles.searchInput}
        />
        
        {/* 筛选按钮 */}
        <TouchableOpacity
          style={[styles.filterButton, hasFilter() && styles.filterButtonActive]}
          onPress={() => setShowLocationPicker(true)}
          activeOpacity={0.7}
        >
          <Ionicons 
            name="filter" 
            size={20} 
            color={hasFilter() ? theme.colors.white : theme.colors.textSecondary} 
          />
        </TouchableOpacity>
      </View>

      {/* 筛选条件显示 */}
      {hasFilter() && (
        <View style={styles.filterDisplay}>
          <View style={styles.filterTag}>
            <Text style={styles.filterTagText}>{getLocationText()}</Text>
            <TouchableOpacity
              style={styles.clearFilterButton}
              onPress={handleClearFilter}
              activeOpacity={0.7}
            >
              <Ionicons name="close" size={16} color={theme.colors.textSecondary} />
            </TouchableOpacity>
          </View>
        </View>
      )}

      {/* 位置选择器 */}
      <LocationPicker
        visible={showLocationPicker}
        title="选择位置"
        data={mockLocationData}
        defaultPath={getCurrentLocationPath()}
        onSelect={handleLocationSelect}
        onCancel={() => setShowLocationPicker(false)}
        maxLevel={5}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: theme.spacing.md,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.sm,
  },
  searchInput: {
    flex: 1,
  },
  filterButton: {
    width: 44,
    height: 44,
    borderRadius: theme.borderRadius.md,
    backgroundColor: theme.colors.surface,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  filterButtonActive: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  filterDisplay: {
    marginTop: theme.spacing.sm,
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: theme.spacing.xs,
  },
  filterTag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.primaryLight,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 4,
    borderRadius: theme.borderRadius.sm,
    gap: theme.spacing.xs,
  },
  filterTagText: {
    fontSize: 12,
    color: theme.colors.primary,
  },
  clearFilterButton: {
    padding: 2,
  },
});
