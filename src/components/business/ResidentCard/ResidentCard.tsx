import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { theme } from '@/config/theme';
import { Resident } from '@/types/user';

interface ResidentCardProps {
  /** 住户信息 */
  resident: Resident;
  /** 是否选中 */
  selected?: boolean;
  /** 选择回调 */
  onSelect?: (resident: Resident) => void;
  /** 删除门卡回调 */
  onDeleteCard?: (resident: Resident, cardNumber: string) => void;
}

export function ResidentCard({
  resident,
  selected = false,
  onSelect,
  onDeleteCard,
}: ResidentCardProps) {
  const handleSelect = () => {
    onSelect?.(resident);
  };

  const handleDeleteCard = (cardNumber: string) => {
    onDeleteCard?.(resident, cardNumber);
  };

  const getIdentityText = (identity: string) => {
    switch (identity) {
      case 'owner':
        return '业主';
      case 'tenant':
        return '租户';
      case 'family':
        return '家属';
      default:
        return identity;
    }
  };

  const getRegistrationMethodText = (method: string) => {
    switch (method) {
      case 'self':
        return '自主登记';
      case 'property':
        return '物业登记';
      case 'admin':
        return '管理员添加';
      default:
        return method;
    }
  };

  const getStatusColor = (status: string) => {
    return status === 'set' || status === 'recorded' 
      ? theme.colors.success 
      : theme.colors.textSecondary;
  };

  const getStatusText = (status: string, type: 'password' | 'face') => {
    if (type === 'password') {
      return status === 'set' ? '已设置' : '未设置';
    }
    return status === 'recorded' ? '已录入' : '未录入';
  };

  return (
    <View style={[styles.container, selected && styles.selectedContainer]}>
      {/* 选择框 */}
      <TouchableOpacity 
        style={styles.checkbox} 
        onPress={handleSelect}
        activeOpacity={0.7}
      >
        <View style={[styles.checkboxInner, selected && styles.checkboxSelected]}>
          {selected && (
            <Ionicons name="checkmark" size={16} color={theme.colors.white} />
          )}
        </View>
      </TouchableOpacity>

      {/* 主要内容 */}
      <View style={styles.content}>
        {/* 头部信息 */}
        <View style={styles.header}>
          <View style={styles.userInfo}>
            {resident.avatar ? (
              <Image source={{ uri: resident.avatar }} style={styles.avatar} />
            ) : (
              <View style={styles.avatarPlaceholder}>
                <Ionicons name="person" size={20} color={theme.colors.textSecondary} />
              </View>
            )}
            <View style={styles.nameSection}>
              <Text style={styles.name}>{resident.realName}</Text>
              <Text style={styles.time}>{resident.registrationTime}</Text>
            </View>
          </View>
          <View style={styles.identityBadge}>
            <Text style={styles.identityText}>{getIdentityText(resident.identity)}</Text>
          </View>
        </View>

        {/* 基本信息 */}
        <View style={styles.infoSection}>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>户室：</Text>
            <Text style={styles.infoValue}>{resident.room}</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>手机：</Text>
            <Text style={styles.infoValue}>{resident.phone}</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>登记方式：</Text>
            <Text style={styles.infoValue}>{getRegistrationMethodText(resident.registrationMethod)}</Text>
          </View>
        </View>

        {/* 凭证状态 */}
        <View style={styles.credentialSection}>
          <View style={styles.credentialRow}>
            <Text style={styles.credentialLabel}>门禁密码：</Text>
            <Text style={[styles.credentialStatus, { color: getStatusColor(resident.passwordStatus) }]}>
              {getStatusText(resident.passwordStatus, 'password')}
            </Text>
          </View>
          <View style={styles.credentialRow}>
            <Text style={styles.credentialLabel}>人像：</Text>
            <Text style={[styles.credentialStatus, { color: getStatusColor(resident.faceStatus) }]}>
              {getStatusText(resident.faceStatus, 'face')}
            </Text>
          </View>
          <View style={styles.credentialRow}>
            <Text style={styles.credentialLabel}>门卡：</Text>
            {resident.nfcCards.length > 0 ? (
              <View style={styles.cardContainer}>
                {resident.nfcCards.map((card, index) => (
                  <View key={index} style={styles.cardItem}>
                    <Text style={styles.cardText}>
                      {card.cardType} ({card.cardNumber})
                    </Text>
                    <TouchableOpacity
                      style={styles.deleteButton}
                      onPress={() => handleDeleteCard(card.cardNumber)}
                      activeOpacity={0.7}
                    >
                      <Text style={styles.deleteButtonText}>删除</Text>
                    </TouchableOpacity>
                  </View>
                ))}
              </View>
            ) : (
              <Text style={[styles.credentialStatus, { color: theme.colors.textSecondary }]}>
                未录入
              </Text>
            )}
          </View>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: theme.colors.white,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.md,
    flexDirection: 'row',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  selectedContainer: {
    borderWidth: 2,
    borderColor: theme.colors.primary,
  },
  checkbox: {
    marginRight: theme.spacing.md,
    paddingTop: theme.spacing.xs,
  },
  checkboxInner: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: theme.colors.border,
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkboxSelected: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  content: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: theme.spacing.md,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: theme.spacing.sm,
  },
  avatarPlaceholder: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.surface,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: theme.spacing.sm,
  },
  nameSection: {
    flex: 1,
  },
  name: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 2,
  },
  time: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  identityBadge: {
    backgroundColor: theme.colors.primaryLight,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 2,
    borderRadius: theme.borderRadius.sm,
  },
  identityText: {
    fontSize: 12,
    color: theme.colors.primary,
    fontWeight: '500',
  },
  infoSection: {
    marginBottom: theme.spacing.md,
  },
  infoRow: {
    flexDirection: 'row',
    marginBottom: 4,
  },
  infoLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    width: 80,
  },
  infoValue: {
    fontSize: 14,
    color: theme.colors.text,
    flex: 1,
  },
  credentialSection: {
    borderTopWidth: 1,
    borderTopColor: theme.colors.divider,
    paddingTop: theme.spacing.sm,
  },
  credentialRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  credentialLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    width: 80,
  },
  credentialStatus: {
    fontSize: 14,
    fontWeight: '500',
  },
  cardContainer: {
    flex: 1,
  },
  cardItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  cardText: {
    fontSize: 14,
    color: theme.colors.success,
    fontWeight: '500',
    flex: 1,
  },
  deleteButton: {
    backgroundColor: theme.colors.error,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 2,
    borderRadius: theme.borderRadius.sm,
    marginLeft: theme.spacing.sm,
  },
  deleteButtonText: {
    fontSize: 12,
    color: theme.colors.white,
    fontWeight: '500',
  },
});
