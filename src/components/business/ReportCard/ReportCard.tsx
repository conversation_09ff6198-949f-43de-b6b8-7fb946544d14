import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { theme } from '@/config/theme';

interface ReportCardProps {
  /** 举报信息 */
  report: {
    id: string;
    type: string;
    reporter: {
      id: string;
      realName: string;
      phone: string;
      avatar?: string;
    };
    reported: {
      id: string;
      realName: string;
      phone: string;
      avatar?: string;
    };
    description: string;
    contentSummary: string;
    reportTime: string;
    status: 'pending' | 'processing' | 'completed';
    processHistory: any[];
  };
  /** 点击回调 */
  onPress?: (report: any) => void;
}

export function ReportCard({ report, onPress }: ReportCardProps) {
  const handlePress = () => {
    onPress?.(report);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return theme.colors.warning;
      case 'processing':
        return theme.colors.info;
      case 'completed':
        return theme.colors.success;
      default:
        return theme.colors.textSecondary;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return '待处理';
      case 'processing':
        return '处理中';
      case 'completed':
        return '已完成';
      default:
        return status;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case '垃圾信息':
        return '#FF9800';
      case '不当言论':
        return '#F44336';
      case '骚扰他人':
        return '#E91E63';
      case '虚假信息':
        return '#9C27B0';
      case '暴力威胁':
        return '#D32F2F';
      default:
        return theme.colors.textSecondary;
    }
  };

  // 截取描述摘要
  const getDescriptionSummary = (description: string, maxLength = 80) => {
    if (description.length <= maxLength) return description;
    return description.substring(0, maxLength) + '...';
  };

  return (
    <TouchableOpacity 
      style={styles.container} 
      onPress={handlePress}
      activeOpacity={0.7}
    >
      {/* 头部信息 */}
      <View style={styles.header}>
        <View style={styles.typeAndStatus}>
          <View style={[styles.typeBadge, { backgroundColor: getTypeColor(report.type) }]}>
            <Text style={styles.typeText}>{report.type}</Text>
          </View>
          <View style={[styles.statusBadge, { backgroundColor: getStatusColor(report.status) }]}>
            <Text style={styles.statusText}>{getStatusText(report.status)}</Text>
          </View>
        </View>
        <Text style={styles.reportTime}>{report.reportTime}</Text>
      </View>

      {/* 举报人和被举报人信息 */}
      <View style={styles.userInfo}>
        {/* 举报人 */}
        <View style={styles.userSection}>
          <Text style={styles.userLabel}>举报人：</Text>
          <View style={styles.userDetails}>
            {report.reporter.avatar ? (
              <Image source={{ uri: report.reporter.avatar }} style={styles.avatar} />
            ) : (
              <View style={styles.avatarPlaceholder}>
                <Ionicons name="person" size={16} color={theme.colors.textSecondary} />
              </View>
            )}
            <View style={styles.userTextInfo}>
              <Text style={styles.userName}>{report.reporter.realName}</Text>
              <Text style={styles.userPhone}>{report.reporter.phone}</Text>
            </View>
          </View>
        </View>

        {/* 被举报人 */}
        <View style={styles.userSection}>
          <Text style={styles.userLabel}>被举报人：</Text>
          <View style={styles.userDetails}>
            {report.reported.avatar ? (
              <Image source={{ uri: report.reported.avatar }} style={styles.avatar} />
            ) : (
              <View style={styles.avatarPlaceholder}>
                <Ionicons name="person" size={16} color={theme.colors.textSecondary} />
              </View>
            )}
            <View style={styles.userTextInfo}>
              <Text style={styles.userName}>{report.reported.realName}</Text>
              <Text style={styles.userPhone}>{report.reported.phone}</Text>
            </View>
          </View>
        </View>
      </View>

      {/* 举报内容 */}
      <View style={styles.contentSection}>
        <Text style={styles.contentLabel}>举报内容：</Text>
        <Text style={styles.contentSummary}>{report.contentSummary}</Text>
        <Text style={styles.description}>{getDescriptionSummary(report.description)}</Text>
      </View>

      {/* 处理进度 */}
      {report.processHistory.length > 0 && (
        <View style={styles.progressSection}>
          <Text style={styles.progressLabel}>处理进度：</Text>
          <Text style={styles.progressText}>
            已处理 {report.processHistory.length} 项操作
          </Text>
        </View>
      )}

      {/* 底部箭头 */}
      <View style={styles.footer}>
        <Ionicons name="chevron-forward" size={20} color={theme.colors.textSecondary} />
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: theme.colors.white,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.md,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  typeAndStatus: {
    flexDirection: 'row',
    gap: theme.spacing.sm,
  },
  typeBadge: {
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 4,
    borderRadius: theme.borderRadius.sm,
  },
  typeText: {
    fontSize: 12,
    color: theme.colors.white,
    fontWeight: '500',
  },
  statusBadge: {
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 4,
    borderRadius: theme.borderRadius.sm,
  },
  statusText: {
    fontSize: 12,
    color: theme.colors.white,
    fontWeight: '500',
  },
  reportTime: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  userInfo: {
    marginBottom: theme.spacing.md,
  },
  userSection: {
    marginBottom: theme.spacing.sm,
  },
  userLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 4,
  },
  userDetails: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    marginRight: theme.spacing.sm,
  },
  avatarPlaceholder: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: theme.colors.surface,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: theme.spacing.sm,
  },
  userTextInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
  },
  userPhone: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginTop: 2,
  },
  contentSection: {
    marginBottom: theme.spacing.md,
  },
  contentLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 4,
  },
  contentSummary: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
    marginBottom: 4,
  },
  description: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    lineHeight: 20,
  },
  progressSection: {
    marginBottom: theme.spacing.sm,
  },
  progressLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 4,
  },
  progressText: {
    fontSize: 14,
    color: theme.colors.info,
    fontWeight: '500',
  },
  footer: {
    alignItems: 'flex-end',
  },
});
