import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { Modal } from '@/components/ui';
import { theme } from '@/config/theme';
import { DataType, Resident, Staff } from '@/types/user';
import { mockDevices } from '@/data/mockData';

interface DataSyncModalProps {
  /** 是否显示 */
  visible: boolean;
  /** 选中的用户列表 */
  selectedUsers: (Resident | Staff)[];
  /** 用户类型 */
  userType: 'resident' | 'staff';
  /** 关闭回调 */
  onClose: () => void;
  /** 确认下发回调 */
  onConfirm: (userIds: string[], dataTypes: DataType[], deviceIds: string[]) => void;
}

export function DataSyncModal({
  visible,
  selectedUsers,
  userType,
  onClose,
  onConfirm,
}: DataSyncModalProps) {
  const [selectedDataTypes, setSelectedDataTypes] = useState<DataType[]>(['all']);
  const [selectedDevices, setSelectedDevices] = useState<Record<string, string[]>>({});

  // 重置状态
  useEffect(() => {
    if (visible) {
      setSelectedDataTypes(['all']);
      // 初始化每个用户的设备选择状态
      const initialDevices: Record<string, string[]> = {};
      selectedUsers.forEach(user => {
        initialDevices[user.id] = [];
      });
      setSelectedDevices(initialDevices);
    }
  }, [visible, selectedUsers]);

  // 数据类型选项
  const dataTypeOptions = [
    { value: 'password' as DataType, label: '密码' },
    { value: 'nfc' as DataType, label: 'NFC' },
    { value: 'face' as DataType, label: '人像' },
    { value: 'all' as DataType, label: '全部' },
  ];

  // 处理数据类型选择
  const handleDataTypeToggle = (dataType: DataType) => {
    if (dataType === 'all') {
      setSelectedDataTypes(['all']);
    } else {
      const newTypes = selectedDataTypes.filter(type => type !== 'all');
      if (newTypes.includes(dataType)) {
        const filtered = newTypes.filter(type => type !== dataType);
        setSelectedDataTypes(filtered.length === 0 ? ['all'] : filtered);
      } else {
        setSelectedDataTypes([...newTypes, dataType]);
      }
    }
  };

  // 处理设备选择
  const handleDeviceToggle = (userId: string, deviceId: string) => {
    setSelectedDevices(prev => {
      const userDevices = prev[userId] || [];
      const newDevices = userDevices.includes(deviceId)
        ? userDevices.filter(id => id !== deviceId)
        : [...userDevices, deviceId];
      
      return {
        ...prev,
        [userId]: newDevices,
      };
    });
  };

  // 全选用户的所有设备
  const handleSelectAllDevices = (userId: string) => {
    const user = selectedUsers.find(u => u.id === userId);
    if (user) {
      setSelectedDevices(prev => ({
        ...prev,
        [userId]: [...user.accessibleDevices],
      }));
    }
  };

  // 获取用户可访问的设备
  const getUserDevices = (user: Resident | Staff) => {
    return mockDevices.filter(device => 
      user.accessibleDevices.includes(device.id)
    );
  };

  // 精确下发
  const handleConfirm = () => {
    // 验证选择
    if (selectedDataTypes.length === 0) {
      Alert.alert('提示', '请选择要下发的数据类型');
      return;
    }

    const hasSelectedDevices = Object.values(selectedDevices).some(devices => devices.length > 0);
    if (!hasSelectedDevices) {
      Alert.alert('提示', '请为至少一个用户选择要下发的设备');
      return;
    }

    // 收集所有选中的设备ID
    const allDeviceIds = new Set<string>();
    Object.values(selectedDevices).forEach(devices => {
      devices.forEach(deviceId => allDeviceIds.add(deviceId));
    });

    onConfirm(
      selectedUsers.map(user => user.id),
      selectedDataTypes,
      Array.from(allDeviceIds)
    );
  };

  // 一键下发
  const handleOneClickSync = () => {
    // 自动选择所有数据类型和所有设备
    const allDeviceIds = new Set<string>();
    selectedUsers.forEach(user => {
      user.accessibleDevices.forEach(deviceId => allDeviceIds.add(deviceId));
    });

    onConfirm(
      selectedUsers.map(user => user.id),
      ['all'],
      Array.from(allDeviceIds)
    );
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      size="large"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        {/* 头部 */}
        <View style={styles.header}>
          <Text style={styles.title}>下发数据</Text>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color={theme.colors.textSecondary} />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* 数据类型选择 */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>数据类型:</Text>
            <View style={styles.dataTypeContainer}>
              {dataTypeOptions.map(option => (
                <TouchableOpacity
                  key={option.value}
                  style={[
                    styles.dataTypeItem,
                    selectedDataTypes.includes(option.value) && styles.dataTypeItemSelected
                  ]}
                  onPress={() => handleDataTypeToggle(option.value)}
                  activeOpacity={0.7}
                >
                  <Text style={[
                    styles.dataTypeText,
                    selectedDataTypes.includes(option.value) && styles.dataTypeTextSelected
                  ]}>
                    {option.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* 用户和设备选择 */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>选择设备:</Text>
            {selectedUsers.map(user => (
              <View key={user.id} style={styles.userSection}>
                <View style={styles.userHeader}>
                  <Text style={styles.userName}>{user.realName}</Text>
                  <TouchableOpacity
                    style={styles.selectAllButton}
                    onPress={() => handleSelectAllDevices(user.id)}
                    activeOpacity={0.7}
                  >
                    <Text style={styles.selectAllText}>全选</Text>
                  </TouchableOpacity>
                </View>
                <View style={styles.deviceGrid}>
                  {getUserDevices(user).map(device => (
                    <TouchableOpacity
                      key={device.id}
                      style={[
                        styles.deviceItem,
                        selectedDevices[user.id]?.includes(device.id) && styles.deviceItemSelected
                      ]}
                      onPress={() => handleDeviceToggle(user.id, device.id)}
                      activeOpacity={0.7}
                    >
                      <Text style={[
                        styles.deviceText,
                        selectedDevices[user.id]?.includes(device.id) && styles.deviceTextSelected
                      ]}>
                        {device.location}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
            ))}
          </View>
        </ScrollView>

        {/* 底部按钮 */}
        <View style={styles.footer}>
          <TouchableOpacity
            style={styles.oneClickButton}
            onPress={handleOneClickSync}
            activeOpacity={0.7}
          >
            <Text style={styles.oneClickButtonText}>一键下发</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.confirmButton}
            onPress={handleConfirm}
            activeOpacity={0.7}
          >
            <Text style={styles.confirmButtonText}>确定</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.white,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: theme.spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.divider,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  closeButton: {
    padding: theme.spacing.xs,
  },
  content: {
    flex: 1,
    padding: theme.spacing.lg,
  },
  section: {
    marginBottom: theme.spacing.lg,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: theme.spacing.md,
  },
  dataTypeContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: theme.spacing.sm,
  },
  dataTypeItem: {
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    borderColor: theme.colors.border,
    backgroundColor: theme.colors.white,
  },
  dataTypeItemSelected: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  dataTypeText: {
    fontSize: 14,
    color: theme.colors.text,
  },
  dataTypeTextSelected: {
    color: theme.colors.white,
  },
  userSection: {
    marginBottom: theme.spacing.lg,
  },
  userHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.sm,
  },
  userName: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.text,
  },
  selectAllButton: {
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 4,
    borderRadius: theme.borderRadius.sm,
    backgroundColor: theme.colors.surface,
  },
  selectAllText: {
    fontSize: 12,
    color: theme.colors.primary,
  },
  deviceGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: theme.spacing.sm,
  },
  deviceItem: {
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.borderRadius.sm,
    borderWidth: 1,
    borderColor: theme.colors.border,
    backgroundColor: theme.colors.white,
    minWidth: 120,
  },
  deviceItemSelected: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  deviceText: {
    fontSize: 12,
    color: theme.colors.text,
    textAlign: 'center',
  },
  deviceTextSelected: {
    color: theme.colors.white,
  },
  footer: {
    flexDirection: 'row',
    padding: theme.spacing.lg,
    borderTopWidth: 1,
    borderTopColor: theme.colors.divider,
    gap: theme.spacing.md,
  },
  oneClickButton: {
    flex: 1,
    backgroundColor: theme.colors.secondary,
    paddingVertical: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
    alignItems: 'center',
  },
  oneClickButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.white,
  },
  confirmButton: {
    flex: 1,
    backgroundColor: theme.colors.primary,
    paddingVertical: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
    alignItems: 'center',
  },
  confirmButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.white,
  },
});
