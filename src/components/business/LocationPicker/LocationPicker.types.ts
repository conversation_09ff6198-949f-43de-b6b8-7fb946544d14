export interface LocationItem {
  id: string;
  name: string;
  children?: LocationItem[];
}

export interface LocationPath {
  id: string;
  name: string;
}

export interface LocationPickerProps {
  /**
   * 是否显示选择器
   */
  visible: boolean;
  
  /**
   * 标题
   */
  title?: string;
  
  /**
   * 位置数据
   */
  data: LocationItem[];
  
  /**
   * 选择完成回调
   */
  onSelect: (path: LocationPath[]) => void;
  
  /**
   * 取消回调
   */
  onCancel: () => void;
  
  /**
   * 最大选择层级
   */
  maxLevel?: number;
  
  /**
   * 默认选中路径
   */
  defaultPath?: LocationPath[];
  
  /**
   * 是否显示确认按钮（多级选择时）
   */
  showConfirmButton?: boolean;
}

export interface LocationPickerState {
  /**
   * 当前选择路径
   */
  selectedPath: LocationPath[];
  
  /**
   * 当前显示的数据
   */
  currentData: LocationItem[];
  
  /**
   * 当前层级
   */
  currentLevel: number;
  
  /**
   * 是否正在加载
   */
  loading: boolean;
}
