import { Ionicons } from '@expo/vector-icons';
import { useCallback, useEffect, useState } from 'react';
import {
    FlatList,
    Modal,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';

import { Button } from '@/components/ui';
import { theme } from '@/config/theme';
import { LocationItem, LocationPath, LocationPickerProps, LocationPickerState } from './LocationPicker.types';

const EMPTY_PATH: LocationPath[] = [];

export function LocationPicker({
  visible,
  title = '选择位置',
  data,
  onSelect,
  onCancel,
  maxLevel = 5,
  defaultPath = EMPTY_PATH,
  showConfirmButton = true,
}: LocationPickerProps) {
  const [state, setState] = useState<LocationPickerState>({
    selectedPath: [],
    currentData: [],
    currentLevel: 0,
    loading: false,
  });

  // 初始化数据
  useEffect(() => {
    if (visible) {
      setState({
        selectedPath: [...defaultPath],
        currentData: data,
        currentLevel: 0,
        loading: false,
      });
    }
  }, [visible, data, defaultPath]);

  // 选择项目
  const handleSelectItem = useCallback((item: LocationItem) => {
    setState(prev => {
      const newPath = [...prev.selectedPath];

      // 如果当前层级已有选择，替换当前层级及之后的选择
      if (newPath.length > prev.currentLevel) {
        newPath.splice(prev.currentLevel);
      }

      // 添加当前选择
      newPath.push({ id: item.id, name: item.name });

      // 如果有子级且未达到最大层级，进入下一级
      if (item.children && item.children.length > 0 && prev.currentLevel < maxLevel - 1) {
        return {
          ...prev,
          selectedPath: newPath,
          currentData: item.children!,
          currentLevel: prev.currentLevel + 1,
        };
      } else {
        // 如果没有子级或达到最大层级，直接选择
        if (!showConfirmButton) {
          // 延迟调用onSelect，避免在setState中调用
          setTimeout(() => onSelect(newPath), 0);
        }
        return {
          ...prev,
          selectedPath: newPath,
        };
      }
    });
  }, [maxLevel, showConfirmButton, onSelect]);

  // 返回上一级
  const handleGoBack = useCallback(() => {
    setState(prev => {
      if (prev.currentLevel === 0) return prev;

      const newLevel = prev.currentLevel - 1;
      let newData = data;

      // 根据路径找到上一级数据
      for (let i = 0; i < newLevel; i++) {
        const pathItem = prev.selectedPath[i];
        const found = newData.find(item => item.id === pathItem.id);
        if (found && found.children) {
          newData = found.children;
        }
      }

      return {
        ...prev,
        currentData: newData,
        currentLevel: newLevel,
      };
    });
  }, [data]);

  // 确认选择
  const handleConfirm = useCallback(() => {
    if (state.selectedPath.length > 0) {
      onSelect(state.selectedPath);
    }
  }, [state.selectedPath, onSelect]);

  // 渲染面包屑导航
  const renderBreadcrumb = () => {
    if (state.selectedPath.length === 0) return null;
    
    return (
      <View style={styles.breadcrumb}>
        <Text style={styles.breadcrumbText}>
          {state.selectedPath.map(item => item.name).join(' > ')}
        </Text>
      </View>
    );
  };

  // 渲染列表项
  const renderItem = ({ item }: { item: LocationItem }) => {
    const isSelected = state.selectedPath[state.currentLevel]?.id === item.id;
    const hasChildren = item.children && item.children.length > 0;
    
    return (
      <TouchableOpacity
        style={[styles.listItem, isSelected && styles.listItemSelected]}
        onPress={() => handleSelectItem(item)}
      >
        <Text style={[styles.listItemText, isSelected && styles.listItemTextSelected]}>
          {item.name}
        </Text>
        {hasChildren && (
          <Ionicons 
            name="chevron-forward" 
            size={20} 
            color={isSelected ? theme.colors.white : theme.colors.textSecondary} 
          />
        )}
      </TouchableOpacity>
    );
  };

  if (!visible) return null;

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onCancel}
    >
      <View style={styles.container}>
        {/* 头部 */}
        <View style={styles.header}>
          <TouchableOpacity onPress={onCancel} style={styles.headerButton}>
            <Text style={styles.cancelText}>取消</Text>
          </TouchableOpacity>
          
          <Text style={styles.title}>{title}</Text>
          
          {state.currentLevel > 0 ? (
            <TouchableOpacity onPress={handleGoBack} style={styles.headerButton}>
              <Text style={styles.backText}>返回</Text>
            </TouchableOpacity>
          ) : (
            <View style={styles.headerButton} />
          )}
        </View>

        {/* 面包屑 */}
        {renderBreadcrumb()}

        {/* 列表 */}
        <FlatList
          data={state.currentData}
          keyExtractor={(item) => item.id}
          renderItem={renderItem}
          style={styles.list}
          showsVerticalScrollIndicator={false}
        />

        {/* 底部按钮 */}
        {showConfirmButton && state.selectedPath.length > 0 && (
          <View style={styles.footer}>
            <Button
              variant="primary"
              size="large"
              onPress={handleConfirm}
              containerStyle={styles.confirmButton}
            >
              确定选择
            </Button>
          </View>
        )}
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
    backgroundColor: theme.colors.white,
  },
  headerButton: {
    minWidth: 60,
  },
  cancelText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  backText: {
    fontSize: 16,
    color: theme.colors.primary,
    textAlign: 'right',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  breadcrumb: {
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    backgroundColor: theme.colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  breadcrumbText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  list: {
    flex: 1,
  },
  listItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    backgroundColor: theme.colors.white,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  listItemSelected: {
    backgroundColor: theme.colors.primary,
  },
  listItemText: {
    fontSize: 16,
    color: theme.colors.text,
    flex: 1,
  },
  listItemTextSelected: {
    color: theme.colors.white,
  },
  footer: {
    padding: theme.spacing.lg,
    backgroundColor: theme.colors.white,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
  confirmButton: {
    width: '100%',
  },
});
