import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { Modal } from '@/components/ui';
import { theme } from '@/config/theme';

interface AvatarUploadProps {
  /** 当前头像URL */
  avatar?: string;
  /** 头像尺寸 */
  size?: number;
  /** 是否可编辑 */
  editable?: boolean;
  /** 头像变化回调 */
  onAvatarChange?: (avatarUrl: string) => void;
}

export function AvatarUpload({
  avatar,
  size = 80,
  editable = true,
  onAvatarChange,
}: AvatarUploadProps) {
  const [showActionSheet, setShowActionSheet] = useState(false);
  const [uploading, setUploading] = useState(false);

  // 处理拍照
  const handleTakePhoto = async () => {
    setShowActionSheet(false);
    
    try {
      // 模拟拍照功能
      Alert.alert('提示', '拍照功能需要在真实设备上测试');
      
      // TODO: 实现真实的拍照功能
      // const result = await ImagePicker.launchCameraAsync({
      //   mediaTypes: ImagePicker.MediaTypeOptions.Images,
      //   allowsEditing: true,
      //   aspect: [1, 1],
      //   quality: 0.8,
      // });
      
      // if (!result.canceled) {
      //   await handleImageUpload(result.assets[0].uri);
      // }
    } catch (error) {
      Alert.alert('错误', '拍照失败');
    }
  };

  // 处理从相册选择
  const handlePickFromGallery = async () => {
    setShowActionSheet(false);
    
    try {
      // 模拟从相册选择功能
      Alert.alert('提示', '相册选择功能需要在真实设备上测试');
      
      // TODO: 实现真实的相册选择功能
      // const result = await ImagePicker.launchImageLibraryAsync({
      //   mediaTypes: ImagePicker.MediaTypeOptions.Images,
      //   allowsEditing: true,
      //   aspect: [1, 1],
      //   quality: 0.8,
      // });
      
      // if (!result.canceled) {
      //   await handleImageUpload(result.assets[0].uri);
      // }
    } catch (error) {
      Alert.alert('错误', '选择图片失败');
    }
  };

  // 处理图片上传
  const handleImageUpload = async (imageUri: string) => {
    setUploading(true);
    
    try {
      // 模拟图片上传
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 模拟返回的图片URL
      const uploadedUrl = `https://via.placeholder.com/${size}x${size}?text=Avatar&t=${Date.now()}`;
      
      onAvatarChange?.(uploadedUrl);
      Alert.alert('成功', '头像更新成功');
    } catch (error) {
      Alert.alert('错误', '头像上传失败');
    } finally {
      setUploading(false);
    }
  };

  // 处理头像点击
  const handleAvatarPress = () => {
    if (!editable) return;
    setShowActionSheet(true);
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={[
          styles.avatarContainer,
          { width: size, height: size, borderRadius: size / 2 }
        ]}
        onPress={handleAvatarPress}
        disabled={!editable || uploading}
        activeOpacity={0.7}
      >
        {avatar ? (
          <Image
            source={{ uri: avatar }}
            style={[
              styles.avatar,
              { width: size, height: size, borderRadius: size / 2 }
            ]}
          />
        ) : (
          <View
            style={[
              styles.avatarPlaceholder,
              { width: size, height: size, borderRadius: size / 2 }
            ]}
          >
            <Ionicons name="person" size={size * 0.4} color={theme.colors.white} />
          </View>
        )}
        
        {/* 编辑图标 */}
        {editable && (
          <View style={styles.editIcon}>
            {uploading ? (
              <View style={styles.uploadingIcon}>
                <Ionicons name="hourglass" size={12} color={theme.colors.white} />
              </View>
            ) : (
              <Ionicons name="camera" size={12} color={theme.colors.white} />
            )}
          </View>
        )}
      </TouchableOpacity>

      {/* 操作选择模态框 */}
      <Modal
        visible={showActionSheet}
        onClose={() => setShowActionSheet(false)}
        title="更换头像"
        size="small"
      >
        <View style={styles.actionSheet}>
          <TouchableOpacity
            style={styles.actionItem}
            onPress={handleTakePhoto}
            activeOpacity={0.7}
          >
            <Ionicons name="camera-outline" size={24} color={theme.colors.text} />
            <Text style={styles.actionText}>拍照</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.actionItem}
            onPress={handlePickFromGallery}
            activeOpacity={0.7}
          >
            <Ionicons name="images-outline" size={24} color={theme.colors.text} />
            <Text style={styles.actionText}>从相册选择</Text>
          </TouchableOpacity>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
  },
  avatarContainer: {
    position: 'relative',
  },
  avatar: {
    backgroundColor: theme.colors.surface,
  },
  avatarPlaceholder: {
    backgroundColor: theme.colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  editIcon: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: theme.colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: theme.colors.white,
  },
  uploadingIcon: {
    backgroundColor: theme.colors.warning,
    width: 20,
    height: 20,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  actionSheet: {
    padding: theme.spacing.lg,
  },
  actionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: theme.spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.divider,
  },
  actionText: {
    fontSize: 16,
    color: theme.colors.text,
    marginLeft: theme.spacing.md,
  },
});
