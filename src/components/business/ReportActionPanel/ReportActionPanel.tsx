import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert, TextInput } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { Modal } from '@/components/ui';
import { theme } from '@/config/theme';

interface ReportActionPanelProps {
  /** 举报信息 */
  report: {
    id: string;
    status: 'pending' | 'processing' | 'completed';
    reported: {
      id: string;
      realName: string;
    };
  };
  /** 处理操作回调 */
  onProcess?: (type: 'reply' | 'warning' | 'ban' | 'delete-content', content: string) => void;
  /** 查看内容回调 */
  onViewContent?: () => void;
}

export function ReportActionPanel({ report, onProcess, onViewContent }: ReportActionPanelProps) {
  const [showActionModal, setShowActionModal] = useState(false);
  const [actionType, setActionType] = useState<'reply' | 'warning' | 'ban' | 'delete-content' | null>(null);
  const [actionContent, setActionContent] = useState('');

  const handleActionPress = (type: 'reply' | 'warning' | 'ban' | 'delete-content') => {
    setActionType(type);
    setActionContent('');
    setShowActionModal(true);
  };

  const handleConfirmAction = () => {
    if (!actionType) return;

    if (!actionContent.trim()) {
      Alert.alert('提示', '请输入处理内容');
      return;
    }

    Alert.alert(
      '确认操作',
      `确定要${getActionText(actionType)}吗？`,
      [
        { text: '取消', style: 'cancel' },
        {
          text: '确定',
          onPress: () => {
            onProcess?.(actionType, actionContent.trim());
            setShowActionModal(false);
            setActionType(null);
            setActionContent('');
          }
        }
      ]
    );
  };

  const getActionText = (type: string) => {
    switch (type) {
      case 'reply':
        return '回复举报人';
      case 'warning':
        return '警告被举报人';
      case 'ban':
        return '禁封被举报人';
      case 'delete-content':
        return '删除相关内容';
      default:
        return type;
    }
  };

  const getActionIcon = (type: string) => {
    switch (type) {
      case 'reply':
        return 'mail-outline';
      case 'warning':
        return 'warning-outline';
      case 'ban':
        return 'ban-outline';
      case 'delete-content':
        return 'trash-outline';
      default:
        return 'help-outline';
    }
  };

  const getActionColor = (type: string) => {
    switch (type) {
      case 'reply':
        return theme.colors.info;
      case 'warning':
        return theme.colors.warning;
      case 'ban':
        return theme.colors.error;
      case 'delete-content':
        return theme.colors.error;
      default:
        return theme.colors.textSecondary;
    }
  };

  const getPlaceholderText = (type: string) => {
    switch (type) {
      case 'reply':
        return '请输入回复内容...';
      case 'warning':
        return '请输入警告内容...';
      case 'ban':
        return '请输入禁封原因...';
      case 'delete-content':
        return '请输入删除原因...';
      default:
        return '请输入内容...';
    }
  };

  const isCompleted = report.status === 'completed';

  return (
    <View style={styles.container}>
      <Text style={styles.title}>处理操作</Text>
      
      {/* 查看举报内容 */}
      <TouchableOpacity
        style={[styles.actionButton, styles.viewButton]}
        onPress={onViewContent}
        activeOpacity={0.7}
      >
        <Ionicons name="eye-outline" size={20} color={theme.colors.info} />
        <Text style={[styles.actionButtonText, { color: theme.colors.info }]}>
          查看举报内容
        </Text>
      </TouchableOpacity>

      {/* 处理操作按钮 */}
      <View style={styles.actionGrid}>
        <TouchableOpacity
          style={[
            styles.actionButton,
            isCompleted && styles.disabledButton
          ]}
          onPress={() => handleActionPress('reply')}
          disabled={isCompleted}
          activeOpacity={0.7}
        >
          <Ionicons 
            name={getActionIcon('reply')} 
            size={20} 
            color={isCompleted ? theme.colors.textDisabled : getActionColor('reply')} 
          />
          <Text style={[
            styles.actionButtonText, 
            { color: isCompleted ? theme.colors.textDisabled : getActionColor('reply') }
          ]}>
            回复举报人
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.actionButton,
            isCompleted && styles.disabledButton
          ]}
          onPress={() => handleActionPress('warning')}
          disabled={isCompleted}
          activeOpacity={0.7}
        >
          <Ionicons 
            name={getActionIcon('warning')} 
            size={20} 
            color={isCompleted ? theme.colors.textDisabled : getActionColor('warning')} 
          />
          <Text style={[
            styles.actionButtonText, 
            { color: isCompleted ? theme.colors.textDisabled : getActionColor('warning') }
          ]}>
            警告被举报人
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.actionButton,
            isCompleted && styles.disabledButton
          ]}
          onPress={() => handleActionPress('ban')}
          disabled={isCompleted}
          activeOpacity={0.7}
        >
          <Ionicons 
            name={getActionIcon('ban')} 
            size={20} 
            color={isCompleted ? theme.colors.textDisabled : getActionColor('ban')} 
          />
          <Text style={[
            styles.actionButtonText, 
            { color: isCompleted ? theme.colors.textDisabled : getActionColor('ban') }
          ]}>
            禁封被举报人
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.actionButton,
            isCompleted && styles.disabledButton
          ]}
          onPress={() => handleActionPress('delete-content')}
          disabled={isCompleted}
          activeOpacity={0.7}
        >
          <Ionicons 
            name={getActionIcon('delete-content')} 
            size={20} 
            color={isCompleted ? theme.colors.textDisabled : getActionColor('delete-content')} 
          />
          <Text style={[
            styles.actionButtonText, 
            { color: isCompleted ? theme.colors.textDisabled : getActionColor('delete-content') }
          ]}>
            删除相关内容
          </Text>
        </TouchableOpacity>
      </View>

      {/* 操作输入模态框 */}
      <Modal
        visible={showActionModal}
        onClose={() => setShowActionModal(false)}
        title={actionType ? getActionText(actionType) : ''}
        size="medium"
      >
        <View style={styles.modalContent}>
          <Text style={styles.modalLabel}>
            {actionType === 'reply' ? '回复内容：' : 
             actionType === 'warning' ? '警告内容：' :
             actionType === 'ban' ? '禁封原因：' :
             actionType === 'delete-content' ? '删除原因：' : '内容：'}
          </Text>
          
          <TextInput
            style={styles.textInput}
            placeholder={actionType ? getPlaceholderText(actionType) : ''}
            value={actionContent}
            onChangeText={setActionContent}
            multiline
            numberOfLines={4}
            textAlignVertical="top"
          />

          <View style={styles.modalActions}>
            <TouchableOpacity
              style={[styles.modalButton, styles.cancelButton]}
              onPress={() => setShowActionModal(false)}
              activeOpacity={0.7}
            >
              <Text style={styles.cancelButtonText}>取消</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.modalButton, styles.confirmButton]}
              onPress={handleConfirmAction}
              activeOpacity={0.7}
            >
              <Text style={styles.confirmButtonText}>确定</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: theme.colors.white,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    marginTop: theme.spacing.md,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: theme.spacing.md,
  },
  viewButton: {
    marginBottom: theme.spacing.md,
  },
  actionGrid: {
    gap: theme.spacing.sm,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    borderColor: theme.colors.border,
    backgroundColor: theme.colors.white,
  },
  disabledButton: {
    backgroundColor: theme.colors.surface,
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: theme.spacing.sm,
  },
  modalContent: {
    padding: theme.spacing.md,
  },
  modalLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
  },
  textInput: {
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.md,
    fontSize: 14,
    color: theme.colors.text,
    minHeight: 100,
    marginBottom: theme.spacing.lg,
  },
  modalActions: {
    flexDirection: 'row',
    gap: theme.spacing.md,
  },
  modalButton: {
    flex: 1,
    paddingVertical: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: theme.colors.surface,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.textSecondary,
  },
  confirmButton: {
    backgroundColor: theme.colors.primary,
  },
  confirmButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.white,
  },
});
