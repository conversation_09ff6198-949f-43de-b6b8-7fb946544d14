import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { theme } from '@/config/theme';

interface PostCardProps {
  /** 帖子信息 */
  post: {
    id: string;
    title: string;
    content: string;
    tag: 'seeking' | 'lost-found' | 'idle-items';
    publisher: {
      id: string;
      realName: string;
      avatar?: string;
      room: string;
      phone: string;
    };
    images: string[];
    publishTime: string;
    status: 'normal' | 'deleted';
    deleteReason?: string;
    commentCount: number;
  };
  /** 点击回调 */
  onPress?: (post: any) => void;
}

export function PostCard({ post, onPress }: PostCardProps) {
  const handlePress = () => {
    onPress?.(post);
  };

  const getTagText = (tag: string) => {
    switch (tag) {
      case 'seeking':
        return '寻人寻物';
      case 'lost-found':
        return '失物招领';
      case 'idle-items':
        return '闲置物品';
      default:
        return tag;
    }
  };

  const getTagColor = (tag: string) => {
    switch (tag) {
      case 'seeking':
        return '#FF9800';
      case 'lost-found':
        return '#4CAF50';
      case 'idle-items':
        return '#2196F3';
      default:
        return theme.colors.textSecondary;
    }
  };

  const getStatusColor = (status: string) => {
    return status === 'deleted' ? theme.colors.error : theme.colors.success;
  };

  const getStatusText = (status: string) => {
    return status === 'deleted' ? '已删除' : '正常';
  };

  // 截取内容摘要
  const getContentSummary = (content: string, maxLength = 100) => {
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '...';
  };

  return (
    <TouchableOpacity 
      style={[styles.container, post.status === 'deleted' && styles.deletedContainer]} 
      onPress={handlePress}
      activeOpacity={0.7}
    >
      {/* 状态标识 */}
      <View style={styles.statusContainer}>
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(post.status) }]}>
          <Text style={styles.statusText}>{getStatusText(post.status)}</Text>
        </View>
        <View style={[styles.tagBadge, { backgroundColor: getTagColor(post.tag) }]}>
          <Text style={styles.tagText}>{getTagText(post.tag)}</Text>
        </View>
      </View>

      {/* 主要内容 */}
      <View style={styles.content}>
        {/* 标题 */}
        <Text style={[styles.title, post.status === 'deleted' && styles.deletedText]} numberOfLines={2}>
          {post.status === 'deleted' ? '内容已删除' : post.title}
        </Text>

        {/* 内容摘要 */}
        <Text style={[styles.contentText, post.status === 'deleted' && styles.deletedText]} numberOfLines={3}>
          {post.status === 'deleted' 
            ? `删除原因：${post.deleteReason || '违规内容'}` 
            : getContentSummary(post.content)
          }
        </Text>

        {/* 图片预览 */}
        {post.images.length > 0 && post.status === 'normal' && (
          <View style={styles.imageContainer}>
            {post.images.slice(0, 3).map((image, index) => (
              <Image key={index} source={{ uri: image }} style={styles.previewImage} />
            ))}
            {post.images.length > 3 && (
              <View style={styles.moreImagesOverlay}>
                <Text style={styles.moreImagesText}>+{post.images.length - 3}</Text>
              </View>
            )}
          </View>
        )}

        {/* 底部信息 */}
        <View style={styles.footer}>
          {/* 发布者信息 */}
          <View style={styles.publisherInfo}>
            {post.publisher.avatar ? (
              <Image source={{ uri: post.publisher.avatar }} style={styles.avatar} />
            ) : (
              <View style={styles.avatarPlaceholder}>
                <Ionicons name="person" size={16} color={theme.colors.textSecondary} />
              </View>
            )}
            <View style={styles.publisherDetails}>
              <Text style={styles.publisherName}>{post.publisher.realName}</Text>
              <Text style={styles.publisherRoom}>{post.publisher.room}</Text>
            </View>
          </View>

          {/* 时间和评论数 */}
          <View style={styles.metaInfo}>
            <Text style={styles.publishTime}>{post.publishTime}</Text>
            <View style={styles.commentInfo}>
              <Ionicons name="chatbubble-outline" size={14} color={theme.colors.textSecondary} />
              <Text style={styles.commentCount}>{post.commentCount}</Text>
            </View>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: theme.colors.white,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.md,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  deletedContainer: {
    backgroundColor: theme.colors.surface,
    opacity: 0.7,
  },
  statusContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: theme.spacing.sm,
  },
  statusBadge: {
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 2,
    borderRadius: theme.borderRadius.sm,
  },
  statusText: {
    fontSize: 10,
    color: theme.colors.white,
    fontWeight: '500',
  },
  tagBadge: {
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 2,
    borderRadius: theme.borderRadius.sm,
  },
  tagText: {
    fontSize: 10,
    color: theme.colors.white,
    fontWeight: '500',
  },
  content: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
    lineHeight: 22,
  },
  deletedText: {
    color: theme.colors.textSecondary,
  },
  contentText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    lineHeight: 20,
    marginBottom: theme.spacing.md,
  },
  imageContainer: {
    flexDirection: 'row',
    marginBottom: theme.spacing.md,
    position: 'relative',
  },
  previewImage: {
    width: 60,
    height: 60,
    borderRadius: theme.borderRadius.sm,
    marginRight: theme.spacing.sm,
  },
  moreImagesOverlay: {
    position: 'absolute',
    right: theme.spacing.sm,
    top: 0,
    width: 60,
    height: 60,
    backgroundColor: 'rgba(0,0,0,0.5)',
    borderRadius: theme.borderRadius.sm,
    alignItems: 'center',
    justifyContent: 'center',
  },
  moreImagesText: {
    color: theme.colors.white,
    fontSize: 12,
    fontWeight: '600',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
  },
  publisherInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    marginRight: theme.spacing.sm,
  },
  avatarPlaceholder: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: theme.colors.surface,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: theme.spacing.sm,
  },
  publisherDetails: {
    flex: 1,
  },
  publisherName: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
  },
  publisherRoom: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginTop: 2,
  },
  metaInfo: {
    alignItems: 'flex-end',
  },
  publishTime: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginBottom: 4,
  },
  commentInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  commentCount: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginLeft: 4,
  },
});
