import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { theme } from '@/config/theme';
import { Staff } from '@/types/user';

interface StaffCardProps {
  /** 员工信息 */
  staff: Staff;
  /** 是否选中 */
  selected?: boolean;
  /** 选择回调 */
  onSelect?: (staff: Staff) => void;
  /** 删除门卡回调 */
  onDeleteCard?: (staff: Staff, cardNumber: string) => void;
}

export function StaffCard({
  staff,
  selected = false,
  onSelect,
  onDeleteCard,
}: StaffCardProps) {
  const handleSelect = () => {
    onSelect?.(staff);
  };

  const handleDeleteCard = (cardNumber: string) => {
    onDeleteCard?.(staff, cardNumber);
  };

  const getStatusColor = (status: string) => {
    return status === 'set' || status === 'recorded' 
      ? theme.colors.success 
      : theme.colors.textSecondary;
  };

  const getStatusText = (status: string, type: 'password' | 'face') => {
    if (type === 'password') {
      return status === 'set' ? '已设置' : '未设置';
    }
    return status === 'recorded' ? '已录入' : '未录入';
  };

  return (
    <View style={[styles.container, selected && styles.selectedContainer]}>
      {/* 选择框 */}
      <TouchableOpacity 
        style={styles.checkbox} 
        onPress={handleSelect}
        activeOpacity={0.7}
      >
        <View style={[styles.checkboxInner, selected && styles.checkboxSelected]}>
          {selected && (
            <Ionicons name="checkmark" size={16} color={theme.colors.white} />
          )}
        </View>
      </TouchableOpacity>

      {/* 主要内容 */}
      <View style={styles.content}>
        {/* 头部信息 */}
        <View style={styles.header}>
          <View style={styles.userInfo}>
            {staff.avatar ? (
              <Image source={{ uri: staff.avatar }} style={styles.avatar} />
            ) : (
              <View style={styles.avatarPlaceholder}>
                <Ionicons name="person" size={20} color={theme.colors.textSecondary} />
              </View>
            )}
            <View style={styles.nameSection}>
              <Text style={styles.name}>{staff.realName}</Text>
              <Text style={styles.time}>{staff.addTime}</Text>
            </View>
          </View>
          <View style={styles.positionBadge}>
            <Text style={styles.positionText}>{staff.position}</Text>
          </View>
        </View>

        {/* 基本信息 */}
        <View style={styles.infoSection}>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>组织：</Text>
            <Text style={styles.infoValue}>{staff.organization}</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>手机：</Text>
            <Text style={styles.infoValue}>{staff.phone}</Text>
          </View>
        </View>

        {/* 凭证状态 */}
        <View style={styles.credentialSection}>
          <View style={styles.credentialRow}>
            <Text style={styles.credentialLabel}>门禁密码：</Text>
            <Text style={[styles.credentialStatus, { color: getStatusColor(staff.passwordStatus) }]}>
              {getStatusText(staff.passwordStatus, 'password')}
            </Text>
          </View>
          <View style={styles.credentialRow}>
            <Text style={styles.credentialLabel}>人像：</Text>
            <Text style={[styles.credentialStatus, { color: getStatusColor(staff.faceStatus) }]}>
              {getStatusText(staff.faceStatus, 'face')}
            </Text>
          </View>
          <View style={styles.credentialRow}>
            <Text style={styles.credentialLabel}>门卡：</Text>
            {staff.nfcCards.length > 0 ? (
              <View style={styles.cardContainer}>
                {staff.nfcCards.map((card, index) => (
                  <View key={index} style={styles.cardItem}>
                    <Text style={styles.cardText}>
                      {card.cardType} ({card.cardNumber})
                    </Text>
                    <TouchableOpacity
                      style={styles.deleteButton}
                      onPress={() => handleDeleteCard(card.cardNumber)}
                      activeOpacity={0.7}
                    >
                      <Text style={styles.deleteButtonText}>删除</Text>
                    </TouchableOpacity>
                  </View>
                ))}
              </View>
            ) : (
              <Text style={[styles.credentialStatus, { color: theme.colors.textSecondary }]}>
                未录入
              </Text>
            )}
          </View>
        </View>

        {/* 权限范围 */}
        <View style={styles.accessSection}>
          <Text style={styles.accessLabel}>门禁权限范围：</Text>
          <View style={styles.accessScopeContainer}>
            {staff.accessScope.map((scope, index) => (
              <View key={index} style={styles.accessScopeItem}>
                <Text style={styles.accessScopeText}>{scope}</Text>
              </View>
            ))}
          </View>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: theme.colors.white,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.md,
    flexDirection: 'row',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  selectedContainer: {
    borderWidth: 2,
    borderColor: theme.colors.primary,
  },
  checkbox: {
    marginRight: theme.spacing.md,
    paddingTop: theme.spacing.xs,
  },
  checkboxInner: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: theme.colors.border,
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkboxSelected: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  content: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: theme.spacing.md,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: theme.spacing.sm,
  },
  avatarPlaceholder: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.surface,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: theme.spacing.sm,
  },
  nameSection: {
    flex: 1,
  },
  name: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 2,
  },
  time: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  positionBadge: {
    backgroundColor: theme.colors.info,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 2,
    borderRadius: theme.borderRadius.sm,
  },
  positionText: {
    fontSize: 12,
    color: theme.colors.white,
    fontWeight: '500',
  },
  infoSection: {
    marginBottom: theme.spacing.md,
  },
  infoRow: {
    flexDirection: 'row',
    marginBottom: 4,
  },
  infoLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    width: 60,
  },
  infoValue: {
    fontSize: 14,
    color: theme.colors.text,
    flex: 1,
  },
  credentialSection: {
    borderTopWidth: 1,
    borderTopColor: theme.colors.divider,
    paddingTop: theme.spacing.sm,
    marginBottom: theme.spacing.md,
  },
  credentialRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  credentialLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    width: 80,
  },
  credentialStatus: {
    fontSize: 14,
    fontWeight: '500',
  },
  cardContainer: {
    flex: 1,
  },
  cardItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  cardText: {
    fontSize: 14,
    color: theme.colors.success,
    fontWeight: '500',
    flex: 1,
  },
  deleteButton: {
    backgroundColor: theme.colors.error,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 2,
    borderRadius: theme.borderRadius.sm,
    marginLeft: theme.spacing.sm,
  },
  deleteButtonText: {
    fontSize: 12,
    color: theme.colors.white,
    fontWeight: '500',
  },
  accessSection: {
    borderTopWidth: 1,
    borderTopColor: theme.colors.divider,
    paddingTop: theme.spacing.sm,
  },
  accessLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing.sm,
  },
  accessScopeContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: theme.spacing.xs,
  },
  accessScopeItem: {
    backgroundColor: theme.colors.surface,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 2,
    borderRadius: theme.borderRadius.sm,
    marginRight: theme.spacing.xs,
    marginBottom: theme.spacing.xs,
  },
  accessScopeText: {
    fontSize: 12,
    color: theme.colors.text,
  },
});
