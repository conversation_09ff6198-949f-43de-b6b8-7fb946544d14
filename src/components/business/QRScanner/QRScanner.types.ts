export interface QRScannerProps {
  /**
   * 是否显示扫码界面
   */
  visible: boolean;
  
  /**
   * 扫码成功回调
   */
  onScanSuccess: (data: string) => void;
  
  /**
   * 关闭扫码界面回调
   */
  onClose: () => void;
  
  /**
   * 扫码失败回调
   */
  onScanError?: (error: string) => void;
  
  /**
   * 扫码提示文本
   */
  hint?: string;
  
  /**
   * 是否显示手电筒按钮
   */
  showTorch?: boolean;
}

export interface QRScannerState {
  /**
   * 相机权限状态
   */
  hasPermission: boolean | null;
  
  /**
   * 是否正在扫描
   */
  isScanning: boolean;
  
  /**
   * 手电筒是否开启
   */
  torchOn: boolean;
  
  /**
   * 错误信息
   */
  error: string | null;
}
