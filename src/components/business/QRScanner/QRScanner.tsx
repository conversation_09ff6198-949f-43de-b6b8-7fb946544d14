import { Ionicons } from '@expo/vector-icons';
import { useEffect, useState } from 'react';
import {
    Alert,
    Modal,
    Platform,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';

import { theme } from '@/config/theme';
import { QRScannerProps, QRScannerState } from './QRScanner.types';

// 动态导入 expo-camera，提供fallback
let CameraView: any = null;
let useCameraPermissions: any = null;
let isCameraAvailable = false;
const isWebEnvironment = Platform.OS === 'web';

if (!isWebEnvironment) {
  try {
    const camera = require('expo-camera');
    CameraView = camera.CameraView;
    useCameraPermissions = camera.useCameraPermissions;
    isCameraAvailable = true;
  } catch (error) {
    console.warn('expo-camera not available:', error);
    isCameraAvailable = false;
  }
}

export function QRScanner({
  visible,
  onScanSuccess,
  onClose,
  onScanError,
  hint = '请将二维码放入扫描框内',
  showTorch = true,
}: QRScannerProps) {
  const [state, setState] = useState<QRScannerState>({
    hasPermission: null,
    isScanning: true,
    torchOn: false,
    error: null,
  });

  // 使用expo-camera的权限hook（仅在支持的环境中）
  let permission: any = null;
  let requestPermission: any = null;

  if (isCameraAvailable && useCameraPermissions) {
    [permission, requestPermission] = useCameraPermissions();
  }

  // 请求相机权限
  useEffect(() => {
    if (visible) {
      requestCameraPermission();
    }
  }, [visible]);

  const requestCameraPermission = async () => {
    try {
      if (isWebEnvironment || !isCameraAvailable) {
        // Web环境或相机不可用时直接授予权限，使用模拟模式
        setState(prev => ({
          ...prev,
          hasPermission: true,
          error: null,
        }));
        return;
      }

      // 使用expo-camera的权限系统
      if (permission && requestPermission) {
        if (!permission.granted) {
          const result = await requestPermission();
          setState(prev => ({
            ...prev,
            hasPermission: result.granted,
            error: result.granted ? null : '需要相机权限才能扫描二维码',
          }));
        } else {
          setState(prev => ({
            ...prev,
            hasPermission: true,
            error: null,
          }));
        }
      } else {
        // 如果权限hook不可用，使用模拟模式
        setState(prev => ({
          ...prev,
          hasPermission: true,
          error: null,
        }));
      }
    } catch (error) {
      // 如果权限请求失败，也使用模拟模式
      setState(prev => ({
        ...prev,
        hasPermission: true,
        error: null,
      }));
    }
  };

  // 处理扫码结果
  const handleBarCodeScanned = ({ data }: { data: string }) => {
    if (!state.isScanning) return;

    setState(prev => ({ ...prev, isScanning: false }));
    
    // 简单验证二维码数据
    if (data && data.trim()) {
      onScanSuccess(data.trim());
    } else {
      const errorMsg = '无效的二维码';
      onScanError?.(errorMsg);
      Alert.alert('扫码失败', errorMsg, [
        {
          text: '重新扫描',
          onPress: () => setState(prev => ({ ...prev, isScanning: true })),
        },
        {
          text: '取消',
          onPress: onClose,
        },
      ]);
    }
  };

  // 切换手电筒
  const toggleTorch = () => {
    setState(prev => ({ ...prev, torchOn: !prev.torchOn }));
  };

  // 重新扫描
  const restartScanning = () => {
    setState(prev => ({ ...prev, isScanning: true, error: null }));
  };

  if (!visible) return null;

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="fullScreen"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        {/* 头部 */}
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color={theme.colors.white} />
          </TouchableOpacity>
          <Text style={styles.title}>扫描二维码</Text>
          <View style={styles.placeholder} />
        </View>

        {/* 扫码区域 */}
        <View style={styles.scanArea}>
          {state.hasPermission === null ? (
            <View style={styles.messageContainer}>
              <Text style={styles.messageText}>正在请求相机权限...</Text>
            </View>
          ) : state.hasPermission === false ? (
            <View style={styles.messageContainer}>
              <Ionicons name="camera-outline" size={64} color={theme.colors.white} />
              <Text style={styles.messageText}>需要相机权限才能扫描二维码</Text>
              <TouchableOpacity onPress={requestCameraPermission} style={styles.retryButton}>
                <Text style={styles.retryButtonText}>重新授权</Text>
              </TouchableOpacity>
            </View>
          ) : isWebEnvironment || !isCameraAvailable ? (
            // Web环境或相机不可用时的模拟扫码界面
            <View style={styles.simulateScanContainer}>
              <View style={styles.scanFrame}>
                <View style={styles.scanBox}>
                  <View style={[styles.corner, styles.topLeft]} />
                  <View style={[styles.corner, styles.topRight]} />
                  <View style={[styles.corner, styles.bottomLeft]} />
                  <View style={[styles.corner, styles.bottomRight]} />
                </View>
              </View>
              <View style={styles.simulateScanMessage}>
                <Ionicons name="qr-code-outline" size={64} color={theme.colors.white} />
                <Text style={styles.simulateScanText}>
                  {isWebEnvironment ? 'Web环境模拟扫码' : '模拟扫码模式'}
                </Text>
                <Text style={styles.simulateScanSubtext}>
                  点击下方按钮模拟扫码成功
                </Text>
                <TouchableOpacity
                  style={styles.simulateButton}
                  onPress={() => handleBarCodeScanned({ data: 'MOCK_DEVICE_001' })}
                >
                  <Text style={styles.simulateButtonText}>模拟扫码成功</Text>
                </TouchableOpacity>
              </View>
            </View>
          ) : (
            <>
              <CameraView
                style={StyleSheet.absoluteFillObject}
                facing="back"
                enableTorch={state.torchOn}
                barcodeScannerSettings={{
                  barcodeTypes: ['qr', 'ean13', 'ean8', 'code128'],
                }}
                onBarcodeScanned={state.isScanning ? handleBarCodeScanned : undefined}
              />

              {/* 扫描框 */}
              <View style={styles.scanFrame}>
                <View style={styles.scanBox}>
                  <View style={[styles.corner, styles.topLeft]} />
                  <View style={[styles.corner, styles.topRight]} />
                  <View style={[styles.corner, styles.bottomLeft]} />
                  <View style={[styles.corner, styles.bottomRight]} />
                </View>
              </View>
            </>
          )}
        </View>

        {/* 底部控制区 */}
        <View style={styles.controls}>
          <Text style={styles.hint}>{hint}</Text>
          
          <View style={styles.controlButtons}>
            {showTorch && state.hasPermission && !isWebEnvironment && isCameraAvailable && (
              <TouchableOpacity onPress={toggleTorch} style={styles.controlButton}>
                <Ionicons 
                  name={state.torchOn ? "flashlight" : "flashlight-outline"} 
                  size={24} 
                  color={theme.colors.white} 
                />
                <Text style={styles.controlButtonText}>
                  {state.torchOn ? '关闭手电筒' : '打开手电筒'}
                </Text>
              </TouchableOpacity>
            )}
            
            {!state.isScanning && (
              <TouchableOpacity onPress={restartScanning} style={styles.controlButton}>
                <Ionicons name="refresh" size={24} color={theme.colors.white} />
                <Text style={styles.controlButtonText}>重新扫描</Text>
              </TouchableOpacity>
            )}
          </View>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.black,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 50,
    paddingHorizontal: theme.spacing.lg,
    paddingBottom: theme.spacing.md,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
  },
  closeButton: {
    padding: theme.spacing.sm,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.white,
  },
  placeholder: {
    width: 40,
  },
  scanArea: {
    flex: 1,
    position: 'relative',
  },
  messageContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.xl,
  },
  messageText: {
    fontSize: 16,
    color: theme.colors.white,
    textAlign: 'center',
    marginTop: theme.spacing.lg,
    lineHeight: 24,
  },
  retryButton: {
    marginTop: theme.spacing.xl,
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    backgroundColor: theme.colors.primary,
    borderRadius: theme.borderRadius.md,
  },
  retryButtonText: {
    color: theme.colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  scanFrame: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scanBox: {
    width: 250,
    height: 250,
    position: 'relative',
  },
  corner: {
    position: 'absolute',
    width: 20,
    height: 20,
    borderColor: theme.colors.primary,
    borderWidth: 3,
  },
  topLeft: {
    top: 0,
    left: 0,
    borderRightWidth: 0,
    borderBottomWidth: 0,
  },
  topRight: {
    top: 0,
    right: 0,
    borderLeftWidth: 0,
    borderBottomWidth: 0,
  },
  bottomLeft: {
    bottom: 0,
    left: 0,
    borderRightWidth: 0,
    borderTopWidth: 0,
  },
  bottomRight: {
    bottom: 0,
    right: 0,
    borderLeftWidth: 0,
    borderTopWidth: 0,
  },
  controls: {
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.xl,
    alignItems: 'center',
  },
  hint: {
    fontSize: 14,
    color: theme.colors.white,
    textAlign: 'center',
    marginBottom: theme.spacing.lg,
    opacity: 0.8,
  },
  controlButtons: {
    flexDirection: 'row',
    gap: theme.spacing.xl,
  },
  controlButton: {
    alignItems: 'center',
    padding: theme.spacing.md,
  },
  controlButtonText: {
    fontSize: 12,
    color: theme.colors.white,
    marginTop: theme.spacing.xs,
    textAlign: 'center',
  },
  // 模拟扫码环境样式
  simulateScanContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    position: 'relative',
  },
  simulateScanMessage: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -100 }, { translateY: -150 }],
    alignItems: 'center',
    width: 200,
  },
  simulateScanText: {
    fontSize: 16,
    color: theme.colors.white,
    textAlign: 'center',
    marginTop: theme.spacing.lg,
    fontWeight: '600',
  },
  simulateScanSubtext: {
    fontSize: 14,
    color: theme.colors.white,
    textAlign: 'center',
    marginTop: theme.spacing.sm,
    marginBottom: theme.spacing.xl,
    opacity: 0.8,
  },
  simulateButton: {
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    backgroundColor: theme.colors.primary,
    borderRadius: theme.borderRadius.md,
  },
  simulateButtonText: {
    color: theme.colors.white,
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
  },
});
