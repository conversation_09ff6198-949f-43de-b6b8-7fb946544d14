import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  Image, 
  ScrollView,
  Dimensions 
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { Modal } from '@/components/ui';
import { theme } from '@/config/theme';

interface MediaPreviewProps {
  /** 图片列表 */
  images?: string[];
  /** 视频列表 */
  videos?: string[];
  /** 最大显示数量 */
  maxDisplay?: number;
  /** 网格列数 */
  columns?: number;
}

const { width: screenWidth } = Dimensions.get('window');

export function MediaPreview({ 
  images = [], 
  videos = [], 
  maxDisplay = 9,
  columns = 3 
}: MediaPreviewProps) {
  const [showImageModal, setShowImageModal] = useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);

  const allMedia = [...images, ...videos];
  const displayMedia = allMedia.slice(0, maxDisplay);
  const hasMore = allMedia.length > maxDisplay;

  // 计算图片尺寸
  const containerPadding = theme.spacing.md * 2;
  const gap = theme.spacing.sm;
  const imageSize = (screenWidth - containerPadding - gap * (columns - 1)) / columns;

  const handleImagePress = (index: number) => {
    setSelectedImageIndex(index);
    setShowImageModal(true);
  };

  const handleVideoPress = (videoUrl: string) => {
    // TODO: 实现视频播放功能
    console.log('Play video:', videoUrl);
  };

  const renderMediaItem = (mediaUrl: string, index: number) => {
    const isVideo = videos.includes(mediaUrl);
    const isLastItem = index === displayMedia.length - 1;
    const showMoreOverlay = hasMore && isLastItem;

    return (
      <TouchableOpacity
        key={index}
        style={[styles.mediaItem, { width: imageSize, height: imageSize }]}
        onPress={() => {
          if (isVideo) {
            handleVideoPress(mediaUrl);
          } else {
            handleImagePress(images.indexOf(mediaUrl));
          }
        }}
        activeOpacity={0.7}
      >
        <Image 
          source={{ uri: mediaUrl }} 
          style={styles.mediaImage}
          resizeMode="cover"
        />
        
        {/* 视频播放图标 */}
        {isVideo && (
          <View style={styles.videoOverlay}>
            <Ionicons name="play-circle" size={32} color="rgba(255,255,255,0.9)" />
          </View>
        )}

        {/* 更多媒体覆盖层 */}
        {showMoreOverlay && (
          <View style={styles.moreOverlay}>
            <Text style={styles.moreText}>+{allMedia.length - maxDisplay}</Text>
          </View>
        )}
      </TouchableOpacity>
    );
  };

  const renderImageModal = () => (
    <Modal
      visible={showImageModal}
      onClose={() => setShowImageModal(false)}
      size="fullscreen"
      animationType="fade"
      showCloseButton={false}
    >
      <View style={styles.modalContainer}>
        {/* 关闭按钮 */}
        <TouchableOpacity
          style={styles.closeButton}
          onPress={() => setShowImageModal(false)}
          activeOpacity={0.7}
        >
          <Ionicons name="close" size={24} color={theme.colors.white} />
        </TouchableOpacity>

        {/* 图片展示 */}
        <ScrollView
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          contentOffset={{ x: selectedImageIndex * screenWidth, y: 0 }}
        >
          {images.map((image, index) => (
            <View key={index} style={styles.imageContainer}>
              <Image 
                source={{ uri: image }} 
                style={styles.fullscreenImage}
                resizeMode="contain"
              />
            </View>
          ))}
        </ScrollView>

        {/* 图片指示器 */}
        {images.length > 1 && (
          <View style={styles.indicator}>
            <Text style={styles.indicatorText}>
              {selectedImageIndex + 1} / {images.length}
            </Text>
          </View>
        )}
      </View>
    </Modal>
  );

  if (allMedia.length === 0) {
    return null;
  }

  return (
    <View style={styles.container}>
      <View style={styles.mediaGrid}>
        {displayMedia.map((media, index) => renderMediaItem(media, index))}
      </View>
      {renderImageModal()}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginVertical: theme.spacing.sm,
  },
  mediaGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: theme.spacing.sm,
  },
  mediaItem: {
    borderRadius: theme.borderRadius.md,
    overflow: 'hidden',
    position: 'relative',
  },
  mediaImage: {
    width: '100%',
    height: '100%',
  },
  videoOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.3)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  moreOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.6)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  moreText: {
    color: theme.colors.white,
    fontSize: 18,
    fontWeight: '600',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'black',
  },
  closeButton: {
    position: 'absolute',
    top: 50,
    right: 20,
    zIndex: 1,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0,0,0,0.5)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  imageContainer: {
    width: screenWidth,
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  fullscreenImage: {
    width: screenWidth,
    height: '100%',
  },
  indicator: {
    position: 'absolute',
    bottom: 50,
    left: 0,
    right: 0,
    alignItems: 'center',
  },
  indicatorText: {
    color: theme.colors.white,
    fontSize: 16,
    fontWeight: '500',
    backgroundColor: 'rgba(0,0,0,0.5)',
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.borderRadius.md,
  },
});
