import { StyleSheet, TextStyle, ViewStyle } from 'react-native';
import { LoadingSize, LoadingType } from './Loading.types';

// 颜色配置
export const colors = {
  primary: '#FF6B35',
  backdrop: 'rgba(0, 0, 0, 0.5)',
  background: '#FFFFFF',
  text: '#000000',
  textSecondary: '#8E8E93',
};

// 尺寸配置
export const sizes = {
  small: {
    indicatorSize: 'small' as const,
    containerPadding: 12,
    fontSize: 12,
    minWidth: 80,
    minHeight: 80,
  },
  medium: {
    indicatorSize: 'large' as const,
    containerPadding: 20,
    fontSize: 14,
    minWidth: 120,
    minHeight: 120,
  },
  large: {
    indicatorSize: 'large' as const,
    containerPadding: 30,
    fontSize: 16,
    minWidth: 160,
    minHeight: 160,
  },
};

// 获取容器样式
export const getContainerStyles = (
  type: LoadingType,
  size: LoadingSize,
  showBackdrop: boolean
): ViewStyle => {
  const sizeConfig = sizes[size];

  const baseStyle: ViewStyle = {
    justifyContent: 'center',
    alignItems: 'center',
    padding: sizeConfig.containerPadding,
  };

  switch (type) {
    case 'fullscreen':
      return {
        ...baseStyle,
        ...StyleSheet.absoluteFillObject,
        backgroundColor: showBackdrop ? colors.backdrop : 'transparent',
      };
    case 'overlay':
      return {
        ...baseStyle,
        backgroundColor: colors.background,
        borderRadius: 12,
        minWidth: sizeConfig.minWidth,
        minHeight: sizeConfig.minHeight,
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 8,
        elevation: 5,
      };
    case 'inline':
    default:
      return {
        ...baseStyle,
        backgroundColor: 'transparent',
        minWidth: sizeConfig.minWidth / 2,
        minHeight: sizeConfig.minHeight / 2,
      };
  }
};

// 获取文本样式
export const getTextStyles = (size: LoadingSize): TextStyle => {
  const sizeConfig = sizes[size];

  return {
    fontSize: sizeConfig.fontSize,
    color: colors.text,
    textAlign: 'center',
    marginTop: 12,
    fontWeight: '500',
  };
};

// 获取背景遮罩样式
export const getBackdropStyles = (opacity: number): ViewStyle => ({
  ...StyleSheet.absoluteFillObject,
  backgroundColor: `rgba(0, 0, 0, ${opacity})`,
});

// 基础样式
export const styles = StyleSheet.create({
  overlay: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  backdrop: {
    ...StyleSheet.absoluteFillObject,
  },
  container: {
    // 由 getContainerStyles 函数提供
  },
  content: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  indicator: {
    // ActivityIndicator 的基础样式
  },
  text: {
    // 由 getTextStyles 函数提供
  },
});
