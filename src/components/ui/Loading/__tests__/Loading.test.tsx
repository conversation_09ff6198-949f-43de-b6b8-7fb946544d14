import { render } from '@testing-library/react-native';
import React from 'react';
import { Text } from 'react-native';
import { Loading } from '../Loading';

describe('Loading', () => {
  it('renders correctly when visible', () => {
    const { getByText } = render(<Loading visible={true} text='Loading...' />);
    expect(getByText('Loading...')).toBeTruthy();
  });

  it('does not render when not visible', () => {
    const { queryByText } = render(
      <Loading visible={false} text='Loading...' />
    );
    expect(queryByText('Loading...')).toBeNull();
  });

  it('renders with loading text', () => {
    const { getByText } = render(<Loading visible={true} text='Loading...' />);
    expect(getByText('Loading...')).toBeTruthy();
  });

  it('renders with custom children', () => {
    const { getByText } = render(
      <Loading visible={true}>
        <Text>Custom Loading Content</Text>
      </Loading>
    );
    expect(getByText('Custom Loading Content')).toBeTruthy();
  });

  it('renders with different types', () => {
    const { getByText } = render(
      <Loading visible={true} type='overlay' text='Loading overlay' />
    );
    expect(getByText('Loading overlay')).toBeTruthy();
  });

  it('renders with different sizes', () => {
    const { getByText } = render(
      <Loading visible={true} size='small' text='Small loading' />
    );
    expect(getByText('Small loading')).toBeTruthy();
  });

  it('shows backdrop when showBackdrop is true', () => {
    const { getByText } = render(
      <Loading visible={true} showBackdrop={true} text='With backdrop' />
    );
    expect(getByText('With backdrop')).toBeTruthy();
  });

  it('hides backdrop when showBackdrop is false', () => {
    const { getByText } = render(
      <Loading visible={true} showBackdrop={false} text='No backdrop' />
    );
    expect(getByText('No backdrop')).toBeTruthy();
  });

  it('renders with different colors', () => {
    const { getByText } = render(
      <Loading visible={true} text='Colored loading' color='red' />
    );
    expect(getByText('Colored loading')).toBeTruthy();
  });

  it('renders inline type correctly', () => {
    const { getByText } = render(
      <Loading visible={true} type='inline' text='Inline loading' />
    );
    expect(getByText('Inline loading')).toBeTruthy();
  });

  it('renders fullscreen type correctly', () => {
    const { getByText } = render(
      <Loading visible={true} type='fullscreen' text='Fullscreen loading' />
    );
    expect(getByText('Fullscreen loading')).toBeTruthy();
  });

  it('handles onClose callback', () => {
    const mockOnClose = jest.fn();
    const { getByText } = render(
      <Loading
        visible={true}
        text='Closable loading'
        onClose={mockOnClose}
        closeOnBackdropPress={true}
      />
    );
    expect(getByText('Closable loading')).toBeTruthy();
  });

  it('applies custom container style', () => {
    const { getByText } = render(
      <Loading
        visible={true}
        text='Styled loading'
        containerStyle={{ backgroundColor: 'blue' }}
      />
    );
    expect(getByText('Styled loading')).toBeTruthy();
  });

  it('applies custom content style', () => {
    const { getByText } = render(
      <Loading
        visible={true}
        text='Custom content'
        contentStyle={{ padding: 20 }}
      />
    );
    expect(getByText('Custom content')).toBeTruthy();
  });

  it('renders with custom backdrop opacity', () => {
    const { getByText } = render(
      <Loading visible={true} text='Custom opacity' backdropOpacity={0.8} />
    );
    expect(getByText('Custom opacity')).toBeTruthy();
  });

  it('renders all size variants correctly', () => {
    const { rerender, getByText } = render(
      <Loading visible={true} size='small' text='Small' />
    );
    expect(getByText('Small')).toBeTruthy();

    rerender(<Loading visible={true} size='medium' text='Medium' />);
    expect(getByText('Medium')).toBeTruthy();

    rerender(<Loading visible={true} size='large' text='Large' />);
    expect(getByText('Large')).toBeTruthy();
  });
});
