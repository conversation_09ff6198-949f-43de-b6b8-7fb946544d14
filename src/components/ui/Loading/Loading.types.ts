import { ReactNode } from 'react';
import { StyleProp, ViewStyle } from 'react-native';

export type LoadingSize = 'small' | 'medium' | 'large';
export type LoadingType = 'overlay' | 'inline' | 'fullscreen';

export interface LoadingProps {
  /**
   * 是否显示加载器
   * @default false
   */
  visible?: boolean;

  /**
   * 加载器类型
   * @default 'overlay'
   */
  type?: LoadingType;

  /**
   * 加载器尺寸
   * @default 'medium'
   */
  size?: LoadingSize;

  /**
   * 加载文本
   */
  text?: string;

  /**
   * 自定义加载内容
   */
  children?: ReactNode;

  /**
   * 加载器颜色
   */
  color?: string;

  /**
   * 背景透明度
   * @default 0.5
   */
  backdropOpacity?: number;

  /**
   * 是否显示背景遮罩
   * @default true
   */
  showBackdrop?: boolean;

  /**
   * 容器样式
   */
  containerStyle?: StyleProp<ViewStyle>;

  /**
   * 内容区域样式
   */
  contentStyle?: StyleProp<ViewStyle>;

  /**
   * 点击背景是否关闭
   * @default false
   */
  closeOnBackdropPress?: boolean;

  /**
   * 关闭回调
   */
  onClose?: () => void;
}
