import React from 'react';
import {
  ActivityIndicator,
  Modal,
  Text,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import {
  colors,
  getBackdropStyles,
  getContainerStyles,
  getTextStyles,
  sizes,
  styles,
} from './Loading.styles';
import { LoadingProps } from './Loading.types';

export const Loading: React.FC<LoadingProps> = ({
  visible = false,
  type = 'overlay',
  size = 'medium',
  text,
  children,
  color = colors.primary,
  backdropOpacity = 0.5,
  showBackdrop = true,
  containerStyle,
  contentStyle,
  closeOnBackdropPress = false,
  onClose,
}) => {
  const sizeConfig = sizes[size];
  const containerStyles = getContainerStyles(type, size, showBackdrop);
  const textStyles = getTextStyles(size);

  // 处理背景点击
  const handleBackdropPress = () => {
    if (closeOnBackdropPress && onClose) {
      onClose();
    }
  };

  // 渲染加载指示器
  const renderIndicator = () => {
    return (
      <ActivityIndicator
        size={sizeConfig.indicatorSize}
        color={color}
        style={styles.indicator}
      />
    );
  };

  // 渲染加载文本
  const renderText = () => {
    if (!text) return null;

    return <Text style={[textStyles, { color }]}>{text}</Text>;
  };

  // 渲染加载内容
  const renderContent = () => {
    return (
      <View style={[styles.content, contentStyle]}>
        {children || (
          <>
            {renderIndicator()}
            {renderText()}
          </>
        )}
      </View>
    );
  };

  // 渲染背景遮罩
  const renderBackdrop = () => {
    if (!showBackdrop || type === 'inline') return null;

    return (
      <TouchableWithoutFeedback onPress={handleBackdropPress}>
        <View style={getBackdropStyles(backdropOpacity)} />
      </TouchableWithoutFeedback>
    );
  };

  // 内联类型直接渲染
  if (type === 'inline') {
    if (!visible) return null;

    return (
      <View style={[containerStyles, containerStyle]}>{renderContent()}</View>
    );
  }

  // 全屏和覆盖类型使用Modal
  return (
    <Modal
      visible={visible}
      transparent
      animationType='fade'
      statusBarTranslucent
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        {type === 'fullscreen' ? (
          // 全屏类型
          <View style={[containerStyles, containerStyle]}>
            {renderContent()}
          </View>
        ) : (
          // 覆盖类型
          <>
            {renderBackdrop()}
            <View style={[containerStyles, containerStyle]}>
              {renderContent()}
            </View>
          </>
        )}
      </View>
    </Modal>
  );
};

export default Loading;
