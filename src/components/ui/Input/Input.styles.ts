import { StyleSheet, TextStyle, ViewStyle } from 'react-native';
import { InputSize, InputVariant } from './Input.types';

// 颜色配置
export const colors = {
  primary: '#FF6B35',
  primaryLight: '#FF8C65',
  primaryDark: '#E55A2B',
  text: '#000000',
  textSecondary: '#8E8E93',
  textDisabled: '#C7C7CC',
  placeholder: '#C7C7CC',
  border: '#E5E5EA',
  borderFocus: '#FF6B35',
  borderError: '#FF4757',
  background: '#FFFFFF',
  backgroundFilled: '#F2F2F7',
  backgroundDisabled: '#F9F9F9',
  error: '#FF4757',
  success: '#4CD964',
  clear: 'rgba(0, 0, 0, 0.3)',
};

// 尺寸配置
export const sizes = {
  small: {
    height: 36,
    paddingHorizontal: 12,
    fontSize: 14,
    iconSize: 16,
    borderRadius: 6,
    labelFontSize: 12,
  },
  medium: {
    height: 44,
    paddingHorizontal: 16,
    fontSize: 16,
    iconSize: 20,
    borderRadius: 8,
    labelFontSize: 14,
  },
  large: {
    height: 52,
    paddingHorizontal: 20,
    fontSize: 18,
    iconSize: 24,
    borderRadius: 10,
    labelFontSize: 16,
  },
};

// 获取容器样式
export const getContainerStyles = (
  variant: InputVariant,
  size: InputSize,
  focused: boolean,
  error: boolean,
  disabled: boolean
): ViewStyle => {
  const sizeConfig = sizes[size];

  const baseStyle: ViewStyle = {
    height: sizeConfig.height,
    borderRadius: sizeConfig.borderRadius,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: sizeConfig.paddingHorizontal,
    borderWidth: 1,
  };

  // 根据变体设置样式
  let backgroundColor: string;
  let borderColor: string;

  if (disabled) {
    backgroundColor = colors.backgroundDisabled;
    borderColor = colors.border;
  } else if (error) {
    backgroundColor =
      variant === 'filled' ? colors.backgroundFilled : colors.background;
    borderColor = colors.borderError;
  } else if (focused) {
    backgroundColor =
      variant === 'filled' ? colors.backgroundFilled : colors.background;
    borderColor = colors.borderFocus;
  } else {
    switch (variant) {
      case 'filled':
        backgroundColor = colors.backgroundFilled;
        borderColor = 'transparent';
        break;
      case 'outline':
        backgroundColor = colors.background;
        borderColor = colors.border;
        break;
      default:
        backgroundColor = colors.background;
        borderColor = colors.border;
    }
  }

  return {
    ...baseStyle,
    backgroundColor,
    borderColor,
  };
};

// 获取输入框样式
export const getInputStyles = (
  size: InputSize,
  disabled: boolean,
  hasLeftIcon: boolean,
  hasRightIcon: boolean
): TextStyle => {
  const sizeConfig = sizes[size];

  return {
    flex: 1,
    fontSize: sizeConfig.fontSize,
    color: disabled ? colors.textDisabled : colors.text,
    marginLeft: hasLeftIcon ? 8 : 0,
    marginRight: hasRightIcon ? 8 : 0,
    paddingVertical: 0, // 移除默认的垂直内边距
  };
};

// 获取标签样式
export const getLabelStyles = (
  size: InputSize,
  _required: boolean,
  error: boolean,
  disabled: boolean
): TextStyle => {
  const sizeConfig = sizes[size];

  let color = colors.text;
  if (disabled) {
    color = colors.textDisabled;
  } else if (error) {
    color = colors.error;
  }

  return {
    fontSize: sizeConfig.labelFontSize,
    fontWeight: '500',
    color,
    marginBottom: 6,
  };
};

// 获取提示文本样式
export const getHelperTextStyles = (
  size: InputSize,
  error: boolean
): TextStyle => {
  const sizeConfig = sizes[size];

  return {
    fontSize: sizeConfig.labelFontSize,
    color: error ? colors.error : colors.textSecondary,
    marginTop: 6,
  };
};

// 基础样式
export const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  inputContainer: {
    position: 'relative',
  },
  input: {
    // 基础输入框样式由 getInputStyles 函数提供
  },
  leftIcon: {
    marginRight: 8,
  },
  rightIcon: {
    marginLeft: 8,
  },
  rightIconsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  clearButton: {
    padding: 4,
    marginLeft: 4,
  },
  passwordToggle: {
    padding: 4,
    marginLeft: 4,
  },
  requiredAsterisk: {
    color: colors.error,
    marginLeft: 2,
  },
  placeholderStyle: {
    color: colors.placeholder,
  },
});
