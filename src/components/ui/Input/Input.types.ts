import { ReactNode } from 'react';
import { StyleProp, TextInputProps, TextStyle, ViewStyle } from 'react-native';

export type InputVariant = 'default' | 'filled' | 'outline';
export type InputSize = 'small' | 'medium' | 'large';

export interface InputProps extends Omit<TextInputProps, 'style'> {
  /**
   * 输入框样式变体
   * @default 'default'
   */
  variant?: InputVariant;

  /**
   * 输入框尺寸
   * @default 'medium'
   */
  size?: InputSize;

  /**
   * 标签文本
   */
  label?: string;

  /**
   * 占位符文本
   */
  placeholder?: string;

  /**
   * 是否为必填项
   * @default false
   */
  required?: boolean;

  /**
   * 是否禁用
   * @default false
   */
  disabled?: boolean;

  /**
   * 错误状态
   * @default false
   */
  error?: boolean;

  /**
   * 错误提示文本
   */
  errorText?: string;

  /**
   * 帮助文本
   */
  helperText?: string;

  /**
   * 左侧图标
   */
  leftIcon?: ReactNode;

  /**
   * 右侧图标
   */
  rightIcon?: ReactNode;

  /**
   * 是否显示清除按钮
   * @default false
   */
  clearable?: boolean;

  /**
   * 是否显示密码切换按钮（仅当secureTextEntry为true时生效）
   * @default false
   */
  showPasswordToggle?: boolean;

  /**
   * 容器样式
   */
  containerStyle?: StyleProp<ViewStyle>;

  /**
   * 输入框样式
   */
  inputStyle?: StyleProp<TextStyle>;

  /**
   * 清除按钮回调
   */
  onClear?: () => void;

  /**
   * 获取焦点回调
   */
  onFocus?: () => void;

  /**
   * 失去焦点回调
   */
  onBlur?: () => void;

  /**
   * 验证函数
   */
  validator?: (value: string) => string | null;

  /**
   * 格式化函数（用于格式化显示的值）
   */
  formatter?: (value: string) => string;
}
