import { fireEvent, render } from '@testing-library/react-native';
import React from 'react';
import { Text } from 'react-native';
import { Input } from '../Input';

describe('Input', () => {
  it('renders correctly with default props', () => {
    const { getByDisplayValue } = render(
      <Input value='test value' onChangeText={() => {}} />
    );
    expect(getByDisplayValue('test value')).toBeTruthy();
  });

  it('renders with label', () => {
    const { getByText } = render(
      <Input label='Email' value='' onChangeText={() => {}} />
    );
    expect(getByText('Email')).toBeTruthy();
  });

  it('renders with placeholder', () => {
    const { getByPlaceholderText } = render(
      <Input placeholder='Enter email' value='' onChangeText={() => {}} />
    );
    expect(getByPlaceholderText('Enter email')).toBeTruthy();
  });

  it('shows required indicator when required', () => {
    const { getByText } = render(
      <Input label='Email' required value='' onChangeText={() => {}} />
    );
    expect(getByText('Email *')).toBeTruthy();
  });

  it('shows error state and error text', () => {
    const { getByText } = render(
      <Input
        label='Email'
        error
        errorText='Invalid email'
        value=''
        onChangeText={() => {}}
      />
    );
    expect(getByText('Invalid email')).toBeTruthy();
  });

  it('shows helper text', () => {
    const { getByText } = render(
      <Input
        label='Password'
        helperText='Must be at least 8 characters'
        value=''
        onChangeText={() => {}}
      />
    );
    expect(getByText('Must be at least 8 characters')).toBeTruthy();
  });

  it('handles text change correctly', () => {
    const mockChange = jest.fn();
    const { getByDisplayValue } = render(
      <Input value='initial' onChangeText={mockChange} />
    );

    const input = getByDisplayValue('initial');
    fireEvent.changeText(input, 'new text');
    expect(mockChange).toHaveBeenCalledWith('new text');
  });

  it('handles focus and blur events', () => {
    const mockFocus = jest.fn();
    const mockBlur = jest.fn();
    const { getByTestId } = render(
      <Input
        testID='input'
        value=''
        onChangeText={() => {}}
        onFocus={mockFocus}
        onBlur={mockBlur}
      />
    );

    const input = getByTestId('input');
    fireEvent(input, 'focus');
    expect(mockFocus).toHaveBeenCalled();

    fireEvent(input, 'blur');
    expect(mockBlur).toHaveBeenCalled();
  });

  it('renders with left and right icons', () => {
    const leftIcon = <Text testID='left-icon'>📧</Text>;
    const rightIcon = <Text testID='right-icon'>👁</Text>;

    const { getByTestId } = render(
      <Input
        value=''
        onChangeText={() => {}}
        leftIcon={leftIcon}
        rightIcon={rightIcon}
      />
    );

    expect(getByTestId('left-icon')).toBeTruthy();
    expect(getByTestId('right-icon')).toBeTruthy();
  });

  it('shows clear button when clearable and has value', () => {
    const mockClear = jest.fn();
    render(
      <Input
        value='some text'
        onChangeText={() => {}}
        clearable
        onClear={mockClear}
      />
    );

    // Clear button should be present when there's a value
    // Note: This test might need adjustment based on actual implementation
  });

  it('shows password toggle when showPasswordToggle is true', () => {
    const { getByTestId } = render(
      <Input
        testID='input'
        value='password'
        onChangeText={() => {}}
        secureTextEntry
        showPasswordToggle
      />
    );

    // Password toggle button should be present
    expect(getByTestId('input')).toBeTruthy();
  });

  it('is disabled when disabled prop is true', () => {
    const { getByTestId } = render(
      <Input
        testID='disabled-input'
        value=''
        onChangeText={() => {}}
        disabled
      />
    );

    const input = getByTestId('disabled-input');
    expect(input.props.editable).toBe(false);
  });

  it('shows error state correctly', () => {
    const { getByText } = render(
      <Input
        value=''
        onChangeText={() => {}}
        error
        errorText='This field is required'
      />
    );

    expect(getByText('This field is required')).toBeTruthy();
  });

  it('shows helper text when provided', () => {
    const { getByText } = render(
      <Input
        value=''
        onChangeText={() => {}}
        helperText='Enter your email address'
      />
    );

    expect(getByText('Enter your email address')).toBeTruthy();
  });

  it('shows required indicator when required', () => {
    const { getByText } = render(
      <Input label='Email' value='' onChangeText={() => {}} required />
    );

    expect(getByText('Email')).toBeTruthy();
  });

  it('handles validator function correctly', () => {
    const mockValidator = jest.fn(value =>
      value.length < 3 ? 'Too short' : null
    );
    const mockChange = jest.fn();

    const { getByDisplayValue } = render(
      <Input value='ab' onChangeText={mockChange} validator={mockValidator} />
    );

    const input = getByDisplayValue('ab');
    fireEvent.changeText(input, 'a');

    expect(mockValidator).toHaveBeenCalledWith('a');
    expect(mockChange).toHaveBeenCalledWith('a');
  });

  it('handles formatter function correctly', () => {
    const mockFormatter = jest.fn(value => value.toUpperCase());
    const mockChange = jest.fn();

    const { getByDisplayValue } = render(
      <Input value='' onChangeText={mockChange} formatter={mockFormatter} />
    );

    const input = getByDisplayValue('');
    fireEvent.changeText(input, 'hello');

    expect(mockFormatter).toHaveBeenCalledWith('hello');
    expect(mockChange).toHaveBeenCalledWith('HELLO');
  });

  it('handles clear button when clearable', () => {
    const mockChange = jest.fn();
    const mockClear = jest.fn();

    const { getByDisplayValue } = render(
      <Input
        value='test value'
        onChangeText={mockChange}
        clearable
        onClear={mockClear}
      />
    );

    expect(getByDisplayValue('test value')).toBeTruthy();
    // Clear button functionality would be tested with actual clear button interaction
  });

  it('handles different variants correctly', () => {
    const { rerender, getByDisplayValue } = render(
      <Input value='test' onChangeText={() => {}} variant='default' />
    );

    expect(getByDisplayValue('test')).toBeTruthy();

    rerender(<Input value='test' onChangeText={() => {}} variant='filled' />);

    expect(getByDisplayValue('test')).toBeTruthy();

    rerender(<Input value='test' onChangeText={() => {}} variant='outline' />);

    expect(getByDisplayValue('test')).toBeTruthy();
  });

  it('handles different sizes correctly', () => {
    const { rerender, getByDisplayValue } = render(
      <Input value='test' onChangeText={() => {}} size='small' />
    );

    expect(getByDisplayValue('test')).toBeTruthy();

    rerender(<Input value='test' onChangeText={() => {}} size='medium' />);

    expect(getByDisplayValue('test')).toBeTruthy();

    rerender(<Input value='test' onChangeText={() => {}} size='large' />);

    expect(getByDisplayValue('test')).toBeTruthy();
  });

  it('renders with different variants', () => {
    const { getByTestId } = render(
      <>
        <Input
          testID='default'
          variant='default'
          value=''
          onChangeText={() => {}}
        />
        <Input
          testID='filled'
          variant='filled'
          value=''
          onChangeText={() => {}}
        />
        <Input
          testID='outline'
          variant='outline'
          value=''
          onChangeText={() => {}}
        />
      </>
    );

    expect(getByTestId('default')).toBeTruthy();
    expect(getByTestId('filled')).toBeTruthy();
    expect(getByTestId('outline')).toBeTruthy();
  });

  it('renders with different sizes', () => {
    const { getByTestId } = render(
      <>
        <Input testID='small' size='small' value='' onChangeText={() => {}} />
        <Input testID='medium' size='medium' value='' onChangeText={() => {}} />
        <Input testID='large' size='large' value='' onChangeText={() => {}} />
      </>
    );

    expect(getByTestId('small')).toBeTruthy();
    expect(getByTestId('medium')).toBeTruthy();
    expect(getByTestId('large')).toBeTruthy();
  });
});
