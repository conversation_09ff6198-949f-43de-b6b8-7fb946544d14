import React, { forwardRef, useCallback, useEffect, useState } from 'react';
import { Text, TextInput, TouchableOpacity, View } from 'react-native';
import {
  colors,
  getContainerStyles,
  getHelperTextStyles,
  getInputStyles,
  getLabelStyles,
  styles,
} from './Input.styles';
import { InputProps } from './Input.types';

export const Input = forwardRef<TextInput, InputProps>(
  (
    {
      variant = 'default',
      size = 'medium',
      label,
      placeholder,
      required = false,
      disabled = false,
      error = false,
      errorText,
      helperText,
      leftIcon,
      rightIcon,
      clearable = false,
      showPasswordToggle = false,
      containerStyle,
      inputStyle,
      value,
      onChangeText,
      onClear,
      onFocus: onFocusProp,
      onBlur: onBlurProp,
      validator,
      formatter,
      secureTextEntry,
      ...textInputProps
    },
    ref
  ) => {
    const [focused, setFocused] = useState(false);
    const [internalValue, setInternalValue] = useState(value || '');
    const [validationError, setValidationError] = useState<string | null>(null);
    const [showPassword, setShowPassword] = useState(false);

    // 同步外部value
    useEffect(() => {
      if (value !== undefined) {
        setInternalValue(value);
      }
    }, [value]);

    // 判断是否有错误
    const hasError = error || !!validationError || !!errorText;

    // 获取样式
    const containerStyles = getContainerStyles(
      variant,
      size,
      focused,
      hasError,
      disabled
    );
    const inputStyles = getInputStyles(
      size,
      disabled,
      !!leftIcon,
      !!(rightIcon || clearable || showPasswordToggle)
    );
    const labelStyles = getLabelStyles(size, required, hasError, disabled);
    const helperTextStyles = getHelperTextStyles(size, hasError);

    // 处理文本变化
    const handleTextChange = useCallback(
      (text: string) => {
        let formattedText = text;

        // 应用格式化函数
        if (formatter) {
          formattedText = formatter(text);
        }

        setInternalValue(formattedText);

        // 验证输入
        if (validator) {
          const error = validator(formattedText);
          setValidationError(error);
        } else {
          setValidationError(null);
        }

        // 调用外部回调
        if (onChangeText) {
          onChangeText(formattedText);
        }
      },
      [formatter, validator, onChangeText]
    );

    // 处理聚焦
    const handleFocus = useCallback(() => {
      setFocused(true);
      if (onFocusProp) {
        onFocusProp();
      }
    }, [onFocusProp]);

    // 处理失焦
    const handleBlur = useCallback(() => {
      setFocused(false);
      if (onBlurProp) {
        onBlurProp();
      }
    }, [onBlurProp]);

    // 处理清除
    const handleClear = useCallback(() => {
      setInternalValue('');
      setValidationError(null);

      if (onChangeText) {
        onChangeText('');
      }

      if (onClear) {
        onClear();
      }
    }, [onChangeText, onClear]);

    // 切换密码显示
    const togglePasswordVisibility = useCallback(() => {
      setShowPassword(!showPassword);
    }, [showPassword]);

    // 渲染标签
    const renderLabel = () => {
      if (!label) return null;

      return (
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text style={labelStyles}>{label}</Text>
          {required && <Text style={styles.requiredAsterisk}>*</Text>}
        </View>
      );
    };

    // 渲染左侧图标
    const renderLeftIcon = () => {
      if (!leftIcon) return null;

      return <View style={styles.leftIcon}>{leftIcon}</View>;
    };

    // 渲染右侧图标区域
    const renderRightIcons = () => {
      const hasRightContent =
        rightIcon || clearable || (showPasswordToggle && secureTextEntry);

      if (!hasRightContent) return null;

      return (
        <View style={styles.rightIconsContainer}>
          {/* 自定义右侧图标 */}
          {rightIcon && <View style={styles.rightIcon}>{rightIcon}</View>}

          {/* 清除按钮 */}
          {clearable && internalValue.length > 0 && !disabled && (
            <TouchableOpacity
              style={styles.clearButton}
              onPress={handleClear}
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            >
              <Text style={{ color: colors.clear, fontSize: 16 }}>✕</Text>
            </TouchableOpacity>
          )}

          {/* 密码切换按钮 */}
          {showPasswordToggle && secureTextEntry && (
            <TouchableOpacity
              style={styles.passwordToggle}
              onPress={togglePasswordVisibility}
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            >
              <Text style={{ color: colors.textSecondary, fontSize: 16 }}>
                {showPassword ? '👁️' : '🙈'}
              </Text>
            </TouchableOpacity>
          )}
        </View>
      );
    };

    // 渲染帮助文本
    const renderHelperText = () => {
      const text = validationError || errorText || helperText;

      if (!text) return null;

      return <Text style={helperTextStyles}>{text}</Text>;
    };

    return (
      <View style={[styles.container, containerStyle]}>
        {/* 标签 */}
        {renderLabel()}

        {/* 输入框容器 */}
        <View style={[styles.inputContainer, containerStyles]}>
          {/* 左侧图标 */}
          {renderLeftIcon()}

          {/* 输入框 */}
          <TextInput
            ref={ref}
            style={[inputStyles, inputStyle]}
            value={internalValue}
            onChangeText={handleTextChange}
            onFocus={handleFocus}
            onBlur={handleBlur}
            placeholder={placeholder}
            placeholderTextColor={colors.placeholder}
            editable={!disabled}
            secureTextEntry={secureTextEntry && !showPassword}
            autoCapitalize='none'
            autoCorrect={false}
            {...textInputProps}
          />

          {/* 右侧图标区域 */}
          {renderRightIcons()}
        </View>

        {/* 帮助文本 */}
        {renderHelperText()}
      </View>
    );
  }
);

Input.displayName = 'Input';

export default Input;
