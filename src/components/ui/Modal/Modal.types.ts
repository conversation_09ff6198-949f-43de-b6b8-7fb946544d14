import { ReactNode } from 'react';
import { StyleProp, ViewStyle } from 'react-native';

export type ModalSize = 'small' | 'medium' | 'large' | 'fullscreen';
export type ModalPosition = 'center' | 'top' | 'bottom';

export interface ModalProps {
  /**
   * 是否可见
   */
  visible: boolean;

  /**
   * 关闭模态框回调
   */
  onClose: () => void;

  /**
   * 模态框内容
   */
  children: ReactNode;

  /**
   * 模态框尺寸
   * @default 'medium'
   */
  size?: ModalSize;

  /**
   * 模态框位置
   * @default 'center'
   */
  position?: ModalPosition;

  /**
   * 标题
   */
  title?: string;

  /**
   * 是否显示关闭按钮
   * @default true
   */
  showCloseButton?: boolean;

  /**
   * 点击背景是否关闭
   * @default true
   */
  closeOnBackdropPress?: boolean;

  /**
   * 是否显示背景遮罩
   * @default true
   */
  showBackdrop?: boolean;

  /**
   * 背景遮罩透明度
   * @default 0.5
   */
  backdropOpacity?: number;

  /**
   * 动画类型
   * @default 'slide'
   */
  animationType?: 'none' | 'slide' | 'fade' | 'scale';

  /**
   * 动画持续时间（毫秒）
   * @default 300
   */
  animationDuration?: number;

  /**
   * 容器样式
   */
  containerStyle?: StyleProp<ViewStyle>;

  /**
   * 内容区域样式
   */
  contentStyle?: StyleProp<ViewStyle>;

  /**
   * 头部区域样式
   */
  headerStyle?: StyleProp<ViewStyle>;

  /**
   * 自定义头部内容
   */
  customHeader?: ReactNode;

  /**
   * 自定义关闭按钮
   */
  customCloseButton?: ReactNode;

  /**
   * 模态框出现时的回调
   */
  onShow?: () => void;

  /**
   * 模态框隐藏时的回调
   */
  onHide?: () => void;

  /**
   * 安全区域处理
   * @default true
   */
  useSafeArea?: boolean;
}
