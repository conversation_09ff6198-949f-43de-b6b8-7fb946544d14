import { fireEvent, render } from '@testing-library/react-native';
import React from 'react';
import { Text } from 'react-native';
import { Modal } from '../Modal';

// Mock Animated
jest.mock('react-native', () => {
  const RN = jest.requireActual('react-native');
  RN.Animated.timing = () => ({
    start: jest.fn(callback => callback && callback()),
  });
  RN.Animated.spring = () => ({
    start: jest.fn(callback => callback && callback()),
  });
  RN.Animated.parallel = () => ({
    start: jest.fn(callback => callback && callback()),
  });
  return RN;
});

describe('Modal', () => {
  const mockOnClose = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly when visible', () => {
    const { getByText } = render(
      <Modal visible={true} onClose={mockOnClose}>
        <Text>Modal Content</Text>
      </Modal>
    );
    expect(getByText('Modal Content')).toBeTruthy();
  });

  it('does not render when not visible', () => {
    const { queryByText } = render(
      <Modal visible={false} onClose={mockOnClose}>
        <Text>Modal Content</Text>
      </Modal>
    );
    expect(queryByText('Modal Content')).toBeNull();
  });

  it('renders with title', () => {
    const { getByText } = render(
      <Modal visible={true} onClose={mockOnClose} title='Test Modal'>
        <Text>Modal Content</Text>
      </Modal>
    );
    expect(getByText('Test Modal')).toBeTruthy();
  });

  it('renders close button by default', () => {
    const { getByText } = render(
      <Modal visible={true} onClose={mockOnClose} title='Test Modal'>
        <Text>Modal Content</Text>
      </Modal>
    );
    expect(getByText('✕')).toBeTruthy();
  });

  it('hides close button when showCloseButton is false', () => {
    const { queryByText } = render(
      <Modal
        visible={true}
        onClose={mockOnClose}
        title='Test Modal'
        showCloseButton={false}
      >
        <Text>Modal Content</Text>
      </Modal>
    );
    expect(queryByText('✕')).toBeNull();
  });

  it('calls onClose when close button is pressed', () => {
    const { getByText } = render(
      <Modal visible={true} onClose={mockOnClose} title='Test Modal'>
        <Text>Modal Content</Text>
      </Modal>
    );

    const closeButton = getByText('✕');
    fireEvent.press(closeButton);
    expect(mockOnClose).toHaveBeenCalledTimes(1);
  });

  it('calls onClose when backdrop is pressed', () => {
    render(
      <Modal visible={true} onClose={mockOnClose} closeOnBackdropPress={true}>
        <Text>Modal Content</Text>
      </Modal>
    );

    // This would need to be adjusted based on actual implementation
    // The backdrop press is usually handled by the parent container
  });

  it('does not call onClose when backdrop is pressed but closeOnBackdropPress is false', () => {
    render(
      <Modal visible={true} onClose={mockOnClose} closeOnBackdropPress={false}>
        <Text>Modal Content</Text>
      </Modal>
    );
    // Test implementation would depend on how backdrop press is handled
  });

  it('renders with different sizes', () => {
    const { getByText } = render(
      <>
        <Modal visible={true} onClose={mockOnClose} size='small'>
          <Text>Small Modal</Text>
        </Modal>
        <Modal visible={true} onClose={mockOnClose} size='medium'>
          <Text>Medium Modal</Text>
        </Modal>
        <Modal visible={true} onClose={mockOnClose} size='large'>
          <Text>Large Modal</Text>
        </Modal>
        <Modal visible={true} onClose={mockOnClose} size='fullscreen'>
          <Text>Fullscreen Modal</Text>
        </Modal>
      </>
    );

    expect(getByText('Small Modal')).toBeTruthy();
    expect(getByText('Medium Modal')).toBeTruthy();
    expect(getByText('Large Modal')).toBeTruthy();
    expect(getByText('Fullscreen Modal')).toBeTruthy();
  });

  it('renders with different positions', () => {
    const { getByText } = render(
      <>
        <Modal visible={true} onClose={mockOnClose} position='top'>
          <Text>Top Modal</Text>
        </Modal>
        <Modal visible={true} onClose={mockOnClose} position='center'>
          <Text>Center Modal</Text>
        </Modal>
        <Modal visible={true} onClose={mockOnClose} position='bottom'>
          <Text>Bottom Modal</Text>
        </Modal>
      </>
    );

    expect(getByText('Top Modal')).toBeTruthy();
    expect(getByText('Center Modal')).toBeTruthy();
    expect(getByText('Bottom Modal')).toBeTruthy();
  });

  it('renders with custom header', () => {
    const customHeader = <Text testID='custom-header'>Custom Header</Text>;

    const { getByTestId } = render(
      <Modal visible={true} onClose={mockOnClose} customHeader={customHeader}>
        <Text>Modal Content</Text>
      </Modal>
    );

    expect(getByTestId('custom-header')).toBeTruthy();
  });

  it('renders with custom close button', () => {
    const customCloseButton = <Text testID='custom-close'>Custom Close</Text>;

    const { getByTestId } = render(
      <Modal
        visible={true}
        onClose={mockOnClose}
        title='Test Modal'
        customCloseButton={customCloseButton}
      >
        <Text>Modal Content</Text>
      </Modal>
    );

    expect(getByTestId('custom-close')).toBeTruthy();
  });

  it('calls onShow when modal becomes visible', () => {
    const mockOnShow = jest.fn();

    render(
      <Modal visible={true} onClose={mockOnClose} onShow={mockOnShow}>
        <Text>Modal Content</Text>
      </Modal>
    );

    expect(mockOnShow).toHaveBeenCalled();
  });

  it('calls onHide when modal becomes hidden', () => {
    const mockOnHide = jest.fn();

    const { rerender } = render(
      <Modal visible={true} onClose={mockOnClose} onHide={mockOnHide}>
        <Text>Modal Content</Text>
      </Modal>
    );

    rerender(
      <Modal visible={false} onClose={mockOnClose} onHide={mockOnHide}>
        <Text>Modal Content</Text>
      </Modal>
    );

    expect(mockOnHide).toHaveBeenCalled();
  });
});
