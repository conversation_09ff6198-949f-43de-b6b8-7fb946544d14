import { Dimensions, StyleSheet, ViewStyle } from 'react-native';
import { ModalPosition, ModalSize } from './Modal.types';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// 颜色配置
export const colors = {
  backdrop: 'rgba(0, 0, 0, 0.5)',
  background: '#FFFFFF',
  border: '#E5E5EA',
  text: '#000000',
  textSecondary: '#8E8E93',
  close: '#8E8E93',
  closeHover: '#FF6B35',
};

// 尺寸配置
export const sizes = {
  small: {
    width: Math.min(320, screenWidth * 0.8),
    maxHeight: screenHeight * 0.4,
  },
  medium: {
    width: Math.min(480, screenWidth * 0.9),
    maxHeight: screenHeight * 0.6,
  },
  large: {
    width: Math.min(640, screenWidth * 0.95),
    maxHeight: screenHeight * 0.8,
  },
  fullscreen: {
    width: screenWidth,
    maxHeight: screenHeight,
  },
};

// 获取模态框容器样式
export const getContainerStyles = (
  size: ModalSize,
  position: ModalPosition
): ViewStyle => {
  const sizeConfig = sizes[size];

  const baseStyle: ViewStyle = {
    backgroundColor: colors.background,
    borderRadius: size === 'fullscreen' ? 0 : 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 5,
  };

  // 设置尺寸
  if (size === 'fullscreen') {
    return {
      ...baseStyle,
      width: sizeConfig.width,
      height: sizeConfig.maxHeight,
    };
  }

  return {
    ...baseStyle,
    width: sizeConfig.width,
    maxHeight: sizeConfig.maxHeight,
    minHeight: 200,
  };
};

// 获取模态框位置样式
export const getPositionStyles = (position: ModalPosition): ViewStyle => {
  switch (position) {
    case 'top':
      return {
        justifyContent: 'flex-start',
        paddingTop: 60,
      };
    case 'bottom':
      return {
        justifyContent: 'flex-end',
        paddingBottom: 60,
      };
    case 'center':
    default:
      return {
        justifyContent: 'center',
      };
  }
};

// 获取背景遮罩样式
export const getBackdropStyles = (opacity: number): ViewStyle => ({
  ...StyleSheet.absoluteFillObject,
  backgroundColor: `rgba(0, 0, 0, ${opacity})`,
});

// 基础样式
export const styles = StyleSheet.create({
  overlay: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  backdrop: {
    ...StyleSheet.absoluteFillObject,
  },
  container: {
    // 由 getContainerStyles 函数提供
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    minHeight: 56,
  },
  headerWithoutBorder: {
    borderBottomWidth: 0,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    flex: 1,
    textAlign: 'center',
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    right: 16,
    top: 12,
  },
  closeButtonText: {
    fontSize: 20,
    color: colors.close,
    fontWeight: '300',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  contentWithoutPadding: {
    padding: 0,
  },
  safeArea: {
    flex: 1,
  },
});
