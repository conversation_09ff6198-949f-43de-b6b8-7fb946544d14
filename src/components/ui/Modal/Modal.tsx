import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
  Animated,
  BackHandler,
  Platform,
  Modal as RNModal,
  SafeAreaView,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import {
  getBackdropStyles,
  getContainerStyles,
  getPositionStyles,
  styles,
} from './Modal.styles';
import { ModalProps } from './Modal.types';

export const Modal: React.FC<ModalProps> = ({
  visible,
  onClose,
  children,
  size = 'medium',
  position = 'center',
  title,
  showCloseButton = true,
  closeOnBackdropPress = true,
  showBackdrop = true,
  backdropOpacity = 0.5,
  animationType = 'slide',
  animationDuration = 300,
  containerStyle,
  contentStyle,
  headerStyle,
  customHeader,
  customCloseButton,
  onShow,
  onHide,
  useSafeArea = true,
}) => {
  const [isVisible, setIsVisible] = useState(false);

  // 动画值
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.3)).current;
  const slideAnim = useRef(new Animated.Value(300)).current;

  // 显示模态框动画
  const showModal = useCallback(() => {
    if (onShow) {
      onShow();
    }

    const animations: Animated.CompositeAnimation[] = [];

    if (animationType === 'fade' || showBackdrop) {
      animations.push(
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: animationDuration,
          useNativeDriver: true,
        })
      );
    }

    if (animationType === 'scale') {
      animations.push(
        Animated.spring(scaleAnim, {
          toValue: 1,
          useNativeDriver: true,
          tension: 50,
          friction: 8,
        })
      );
    }

    if (animationType === 'slide') {
      animations.push(
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: animationDuration,
          useNativeDriver: true,
        })
      );
    }

    if (animations.length > 0) {
      Animated.parallel(animations).start();
    }
  }, [
    animationType,
    animationDuration,
    fadeAnim,
    scaleAnim,
    slideAnim,
    onShow,
    showBackdrop,
  ]);

  // 隐藏模态框动画
  const hideModal = useCallback(() => {
    const animations: Animated.CompositeAnimation[] = [];

    if (animationType === 'fade' || showBackdrop) {
      animations.push(
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: animationDuration,
          useNativeDriver: true,
        })
      );
    }

    if (animationType === 'scale') {
      animations.push(
        Animated.timing(scaleAnim, {
          toValue: 0.3,
          duration: animationDuration,
          useNativeDriver: true,
        })
      );
    }

    if (animationType === 'slide') {
      animations.push(
        Animated.timing(slideAnim, {
          toValue: 300,
          duration: animationDuration,
          useNativeDriver: true,
        })
      );
    }

    const onComplete = () => {
      setIsVisible(false);
      if (onHide) {
        onHide();
      }
    };

    if (animations.length > 0) {
      Animated.parallel(animations).start(onComplete);
    } else {
      onComplete();
    }
  }, [
    animationType,
    animationDuration,
    fadeAnim,
    scaleAnim,
    slideAnim,
    onHide,
    showBackdrop,
  ]);

  // 监听visible变化
  useEffect(() => {
    if (visible) {
      setIsVisible(true);
      showModal();
    } else {
      hideModal();
    }
  }, [visible, showModal, hideModal]);

  // Android返回键处理
  useEffect(() => {
    if (Platform.OS === 'android' && visible) {
      const backHandler = BackHandler.addEventListener(
        'hardwareBackPress',
        () => {
          if (closeOnBackdropPress) {
            onClose();
            return true;
          }
          return false;
        }
      );

      return () => backHandler.remove();
    }
  }, [visible, closeOnBackdropPress, onClose]);

  // 处理背景点击
  const handleBackdropPress = useCallback(() => {
    if (closeOnBackdropPress) {
      onClose();
    }
  }, [closeOnBackdropPress, onClose]);

  // 获取动画样式
  const getAnimatedStyle = () => {
    const baseStyle = getContainerStyles(size, position);

    switch (animationType) {
      case 'fade':
        return {
          ...baseStyle,
          opacity: fadeAnim,
        };
      case 'scale':
        return {
          ...baseStyle,
          opacity: fadeAnim,
          transform: [{ scale: scaleAnim }],
        };
      case 'slide':
        return {
          ...baseStyle,
          transform: [{ translateY: slideAnim }],
        };
      default:
        return baseStyle;
    }
  };

  // 渲染头部
  const renderHeader = () => {
    if (customHeader) {
      return <View style={[styles.header, headerStyle]}>{customHeader}</View>;
    }

    if (!title && !showCloseButton) {
      return null;
    }

    return (
      <View
        style={[
          styles.header,
          !title && styles.headerWithoutBorder,
          headerStyle,
        ]}
      >
        {title && <Text style={styles.title}>{title}</Text>}
        {showCloseButton && (
          <TouchableOpacity
            style={styles.closeButton}
            onPress={onClose}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            {customCloseButton || <Text style={styles.closeButtonText}>✕</Text>}
          </TouchableOpacity>
        )}
      </View>
    );
  };

  // 渲染内容区域
  const renderContent = () => {
    const ContentWrapper = useSafeArea ? SafeAreaView : View;
    const wrapperStyle = useSafeArea ? styles.safeArea : undefined;

    return (
      <ContentWrapper style={wrapperStyle}>
        <Animated.View style={[getAnimatedStyle(), containerStyle]}>
          {renderHeader()}
          <View style={[styles.content, contentStyle]}>{children}</View>
        </Animated.View>
      </ContentWrapper>
    );
  };

  // 渲染背景遮罩
  const renderBackdrop = () => {
    if (!showBackdrop) return null;

    return (
      <TouchableWithoutFeedback onPress={handleBackdropPress}>
        <Animated.View
          style={[
            getBackdropStyles(backdropOpacity),
            {
              opacity: fadeAnim,
            },
          ]}
        />
      </TouchableWithoutFeedback>
    );
  };

  return (
    <RNModal
      visible={isVisible}
      transparent
      animationType='none'
      statusBarTranslucent
      onRequestClose={onClose}
    >
      <View style={[styles.overlay, getPositionStyles(position)]}>
        {renderBackdrop()}
        {renderContent()}
      </View>
    </RNModal>
  );
};

export default Modal;
