import { fireEvent, render } from '@testing-library/react-native';
import React from 'react';
import { Text } from 'react-native';
import { Toast } from '../Toast';

// Mock setTimeout/clearTimeout
jest.useFakeTimers();

// Mock Animated
jest.mock('react-native', () => {
  const RN = jest.requireActual('react-native');
  RN.Animated.timing = () => ({
    start: jest.fn(callback => callback && callback()),
  });
  RN.Animated.parallel = () => ({
    start: jest.fn(callback => callback && callback()),
  });
  return RN;
});

describe('Toast', () => {
  beforeEach(() => {
    jest.clearAllTimers();
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
  });

  it('renders correctly when visible', () => {
    const { getByText } = render(
      <Toast visible={true} message='Test message' onClose={() => {}} />
    );
    expect(getByText('Test message')).toBeTruthy();
  });

  it('does not render when not visible', () => {
    const { queryByText } = render(
      <Toast visible={false} message='Test message' onClose={() => {}} />
    );
    expect(queryByText('Test message')).toBeNull();
  });

  it('renders with description', () => {
    const { getByText } = render(
      <Toast
        visible={true}
        message='Error'
        description='Something went wrong'
        onClose={() => {}}
      />
    );
    expect(getByText('Error')).toBeTruthy();
    expect(getByText('Something went wrong')).toBeTruthy();
  });

  it('renders different types with correct icons', () => {
    const { getByText, rerender } = render(
      <Toast
        visible={true}
        type='success'
        message='Success'
        onClose={() => {}}
      />
    );
    expect(getByText('✓')).toBeTruthy();

    rerender(
      <Toast visible={true} type='error' message='Error' onClose={() => {}} />
    );
    expect(getByText('✕')).toBeTruthy();

    rerender(
      <Toast
        visible={true}
        type='warning'
        message='Warning'
        onClose={() => {}}
      />
    );
    expect(getByText('⚠')).toBeTruthy();

    rerender(
      <Toast visible={true} type='info' message='Info' onClose={() => {}} />
    );
    expect(getByText('ℹ')).toBeTruthy();
  });

  it('renders with custom icon', () => {
    const customIcon = <Text>🔥</Text>;
    const { getByText } = render(
      <Toast
        visible={true}
        message='Custom icon'
        icon={customIcon}
        onClose={() => {}}
      />
    );
    expect(getByText('🔥')).toBeTruthy();
  });

  it('hides icon when showIcon is false', () => {
    const { queryByText } = render(
      <Toast
        visible={true}
        type='success'
        message='No icon'
        showIcon={false}
        onClose={() => {}}
      />
    );
    expect(queryByText('✓')).toBeNull();
  });

  it('shows close button by default', () => {
    const { getByText } = render(
      <Toast visible={true} message='Closable' onClose={() => {}} />
    );
    expect(getByText('×')).toBeTruthy();
  });

  it('hides close button when closable is false', () => {
    const { queryByText } = render(
      <Toast
        visible={true}
        message='Not closable'
        closable={false}
        onClose={() => {}}
      />
    );
    expect(queryByText('×')).toBeNull();
  });

  it('calls onClose when close button is pressed', () => {
    const mockOnClose = jest.fn();
    const { getByText } = render(
      <Toast visible={true} message='Closable' onClose={mockOnClose} />
    );

    const closeButton = getByText('×');
    fireEvent.press(closeButton);
    expect(mockOnClose).toHaveBeenCalled();
  });

  it('auto-hides after duration', () => {
    const mockOnClose = jest.fn();
    render(
      <Toast
        visible={true}
        message='Auto hide'
        duration={3000}
        onClose={mockOnClose}
      />
    );

    jest.advanceTimersByTime(3000);
    expect(mockOnClose).toHaveBeenCalled();
  });

  it('does not auto-hide when duration is 0', () => {
    const mockOnClose = jest.fn();
    render(
      <Toast
        visible={true}
        message='No auto hide'
        duration={0}
        onClose={mockOnClose}
      />
    );

    jest.advanceTimersByTime(5000);
    expect(mockOnClose).not.toHaveBeenCalled();
  });

  it('renders in different positions', () => {
    const { getByText, rerender } = render(
      <Toast
        visible={true}
        position='top'
        message='Top position'
        onClose={() => {}}
      />
    );
    expect(getByText('Top position')).toBeTruthy();

    rerender(
      <Toast
        visible={true}
        position='center'
        message='Center position'
        onClose={() => {}}
      />
    );
    expect(getByText('Center position')).toBeTruthy();

    rerender(
      <Toast
        visible={true}
        position='bottom'
        message='Bottom position'
        onClose={() => {}}
      />
    );
    expect(getByText('Bottom position')).toBeTruthy();
  });

  it('calls onShow when toast becomes visible', () => {
    const mockOnShow = jest.fn();
    render(
      <Toast
        visible={true}
        message='Show callback'
        onShow={mockOnShow}
        onClose={() => {}}
      />
    );
    expect(mockOnShow).toHaveBeenCalled();
  });

  it('calls onHide when toast becomes hidden', () => {
    const mockOnHide = jest.fn();
    const { rerender } = render(
      <Toast
        visible={true}
        message='Hide callback'
        onHide={mockOnHide}
        onClose={() => {}}
      />
    );

    rerender(
      <Toast
        visible={false}
        message='Hide callback'
        onHide={mockOnHide}
        onClose={() => {}}
      />
    );

    expect(mockOnHide).toHaveBeenCalled();
  });
});
