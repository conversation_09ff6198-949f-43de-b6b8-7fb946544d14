import { ReactNode } from 'react';
import { StyleProp, ViewStyle } from 'react-native';

export type ToastType = 'success' | 'error' | 'warning' | 'info';
export type ToastPosition = 'top' | 'center' | 'bottom';

export interface ToastProps {
  /**
   * 是否可见
   */
  visible: boolean;

  /**
   * Toast类型
   * @default 'info'
   */
  type?: ToastType;

  /**
   * Toast位置
   * @default 'top'
   */
  position?: ToastPosition;

  /**
   * 提示文本
   */
  message: string;

  /**
   * 详细描述
   */
  description?: string;

  /**
   * 自定义图标
   */
  icon?: ReactNode;

  /**
   * 自动隐藏延迟（毫秒）
   * @default 3000
   */
  duration?: number;

  /**
   * 是否可手动关闭
   * @default true
   */
  closable?: boolean;

  /**
   * 是否显示图标
   * @default true
   */
  showIcon?: boolean;

  /**
   * 容器样式
   */
  containerStyle?: StyleProp<ViewStyle>;

  /**
   * 内容样式
   */
  contentStyle?: StyleProp<ViewStyle>;

  /**
   * 关闭回调
   */
  onClose?: () => void;

  /**
   * 显示回调
   */
  onShow?: () => void;

  /**
   * 隐藏回调
   */
  onHide?: () => void;
}
