import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
  Animated,
  Modal,
  Platform,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {
  getIconColor,
  getToastPositionStyles,
  getToastTypeStyles,
  styles,
} from './Toast.styles';
import { ToastProps } from './Toast.types';

// 默认图标组件
const DefaultIcon: React.FC<{ type: string; color: string }> = ({
  type,
  color,
}) => {
  const getIconText = () => {
    switch (type) {
      case 'success':
        return '✓';
      case 'error':
        return '✕';
      case 'warning':
        return '⚠';
      case 'info':
      default:
        return 'ℹ';
    }
  };

  return (
    <Text style={{ fontSize: 20, color, fontWeight: 'bold' }}>
      {getIconText()}
    </Text>
  );
};

export const Toast: React.FC<ToastProps> = ({
  visible,
  type = 'info',
  position = 'top',
  message,
  description,
  icon,
  duration = 3000,
  closable = true,
  showIcon = true,
  containerStyle,
  contentStyle,
  onClose,
  onShow,
  onHide,
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(-100)).current;
  const timeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  // 显示动画
  const showToast = useCallback(() => {
    setIsVisible(true);
    if (onShow) {
      onShow();
    }

    const animations = [
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
    ];

    if (position === 'top') {
      slideAnim.setValue(-100);
      animations.push(
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        })
      );
    } else if (position === 'bottom') {
      slideAnim.setValue(100);
      animations.push(
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        })
      );
    }

    Animated.parallel(animations).start();
  }, [fadeAnim, slideAnim, position, onShow]);

  // 隐藏动画
  const hideToast = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }

    const animations = [
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ];

    if (position === 'top') {
      animations.push(
        Animated.timing(slideAnim, {
          toValue: -100,
          duration: 300,
          useNativeDriver: true,
        })
      );
    } else if (position === 'bottom') {
      animations.push(
        Animated.timing(slideAnim, {
          toValue: 100,
          duration: 300,
          useNativeDriver: true,
        })
      );
    }

    Animated.parallel(animations).start(() => {
      setIsVisible(false);
      if (onHide) {
        onHide();
      }
      if (onClose) {
        onClose();
      }
    });
  }, [fadeAnim, slideAnim, position, onHide, onClose]);

  // 监听visible变化
  useEffect(() => {
    if (visible) {
      showToast();

      // 自动隐藏
      if (duration > 0) {
        timeoutRef.current = setTimeout(() => {
          hideToast();
        }, duration);
      }
    } else {
      hideToast();
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [visible, showToast, hideToast, duration]);

  // 手动关闭
  const handleClose = () => {
    hideToast();
  };

  // 获取动画样式
  const getAnimatedStyle = () => {
    const baseStyle = {
      opacity: fadeAnim,
    };

    if (position === 'top' || position === 'bottom') {
      return {
        ...baseStyle,
        transform: [{ translateY: slideAnim }],
      };
    }

    return baseStyle;
  };

  // 渲染图标
  const renderIcon = () => {
    if (!showIcon) return null;

    const iconColor = getIconColor(type);

    return (
      <View style={styles.iconContainer}>
        {icon || <DefaultIcon type={type} color={iconColor} />}
      </View>
    );
  };

  // 渲染关闭按钮
  const renderCloseButton = () => {
    if (!closable) return null;

    return (
      <TouchableOpacity
        style={styles.closeButton}
        onPress={handleClose}
        hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
      >
        <Text style={styles.closeButtonText}>×</Text>
      </TouchableOpacity>
    );
  };

  // 渲染内容
  const renderContent = () => {
    const typeStyles = getToastTypeStyles(type);
    const positionStyles = getToastPositionStyles(position);

    return (
      <Animated.View
        style={[
          styles.container,
          typeStyles,
          positionStyles,
          getAnimatedStyle(),
          containerStyle,
        ]}
      >
        <View style={[styles.content, contentStyle]}>
          {renderIcon()}
          <View style={styles.textContainer}>
            <Text style={styles.message}>{message}</Text>
            {description && (
              <Text style={styles.description}>{description}</Text>
            )}
          </View>
          {renderCloseButton()}
        </View>
      </Animated.View>
    );
  };

  if (!isVisible && !visible) {
    return null;
  }

  // 使用Portal渲染到屏幕顶层
  return (
    <Modal
      transparent
      visible={isVisible}
      animationType='none'
      statusBarTranslucent={Platform.OS === 'android'}
    >
      {renderContent()}
    </Modal>
  );
};

export default Toast;
