import { StyleSheet, ViewStyle } from 'react-native';
import { ToastPosition, ToastType } from './Toast.types';

// 颜色配置
export const colors = {
  success: '#4CAF50',
  successBg: '#E8F5E8',
  error: '#F44336',
  errorBg: '#FDEDED',
  warning: '#FF9800',
  warningBg: '#FFF8E1',
  info: '#2196F3',
  infoBg: '#E3F2FD',
  text: '#333333',
  white: '#FFFFFF',
  overlay: 'rgba(0, 0, 0, 0.3)',
};

// 获取Toast类型样式
export const getToastTypeStyles = (type: ToastType): ViewStyle => {
  switch (type) {
    case 'success':
      return {
        backgroundColor: colors.successBg,
        borderLeftColor: colors.success,
      };
    case 'error':
      return {
        backgroundColor: colors.errorBg,
        borderLeftColor: colors.error,
      };
    case 'warning':
      return {
        backgroundColor: colors.warningBg,
        borderLeftColor: colors.warning,
      };
    case 'info':
    default:
      return {
        backgroundColor: colors.infoBg,
        borderLeftColor: colors.info,
      };
  }
};

// 获取Toast位置样式
export const getToastPositionStyles = (position: ToastPosition): ViewStyle => {
  const baseStyle: ViewStyle = {
    position: 'absolute',
    left: 16,
    right: 16,
    zIndex: 9999,
  };

  switch (position) {
    case 'top':
      return {
        ...baseStyle,
        top: 60,
      };
    case 'center':
      return {
        ...baseStyle,
        top: '50%',
        transform: [{ translateY: -50 }],
      };
    case 'bottom':
      return {
        ...baseStyle,
        bottom: 60,
      };
    default:
      return {
        ...baseStyle,
        top: 60,
      };
  }
};

// 获取图标颜色
export const getIconColor = (type: ToastType): string => {
  switch (type) {
    case 'success':
      return colors.success;
    case 'error':
      return colors.error;
    case 'warning':
      return colors.warning;
    case 'info':
    default:
      return colors.info;
  }
};

// 基础样式
export const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: colors.overlay,
    zIndex: 9998,
  },
  container: {
    borderRadius: 8,
    borderLeftWidth: 4,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  iconContainer: {
    marginRight: 12,
    paddingTop: 2,
  },
  textContainer: {
    flex: 1,
  },
  message: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 4,
  },
  description: {
    fontSize: 14,
    color: colors.text,
    opacity: 0.8,
    lineHeight: 20,
  },
  closeButton: {
    marginLeft: 12,
    padding: 4,
  },
  closeButtonText: {
    fontSize: 18,
    color: colors.text,
    opacity: 0.6,
  },
});
