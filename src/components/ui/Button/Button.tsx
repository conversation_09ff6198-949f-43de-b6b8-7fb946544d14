import React, { useCallback, useState } from 'react';
import {
  ActivityIndicator,
  Animated,
  Platform,
  Pressable,
  Text,
  View,
} from 'react-native';
import { getButtonStyles, getTextStyles, styles } from './Button.styles';
import { ButtonProps } from './Button.types';

// 安全导入触觉反馈
let HapticFeedback: any = null;
try {
  // eslint-disable-next-line @typescript-eslint/no-require-imports
  HapticFeedback = require('expo-haptics').HapticFeedback;
} catch {
  // expo-haptics 不可用时忽略
}

export const Button: React.FC<ButtonProps> = ({
  children,
  variant = 'primary',
  size = 'medium',
  loading = false,
  loadingText,
  disabled = false,
  fullWidth = false,
  leftIcon,
  rightIcon,
  onPress,
  containerStyle,
  ...pressableProps
}) => {
  const [isPressed, setIsPressed] = useState(false);
  const [scaleAnim] = useState(new Animated.Value(1));

  // 判断是否应该禁用
  const isDisabled = disabled || loading;

  // 获取样式
  const buttonStyles = getButtonStyles(variant, size, isDisabled, fullWidth);
  const textStyles = getTextStyles(variant, size, isDisabled);

  // 处理按下动画
  const handlePressIn = useCallback(() => {
    if (isDisabled) return;

    setIsPressed(true);
    Animated.spring(scaleAnim, {
      toValue: 0.95,
      useNativeDriver: true,
      speed: 50,
      bounciness: 4,
    }).start();
  }, [isDisabled, scaleAnim]);

  // 处理释放动画
  const handlePressOut = useCallback(() => {
    setIsPressed(false);
    Animated.spring(scaleAnim, {
      toValue: 1,
      useNativeDriver: true,
      speed: 50,
      bounciness: 4,
    }).start();
  }, [scaleAnim]);

  // 处理点击事件
  const handlePress = useCallback(() => {
    if (isDisabled || !onPress) return;

    // 添加触觉反馈（iOS）
    if (Platform.OS === 'ios' && HapticFeedback) {
      try {
        HapticFeedback.impactAsync?.(HapticFeedback.ImpactFeedbackStyle.Light);
      } catch {
        // 忽略触觉反馈错误
      }
    }

    onPress();
  }, [isDisabled, onPress]);

  // 渲染加载指示器
  const renderLoadingIndicator = () => {
    if (!loading) return null;

    const indicatorColor =
      variant === 'outline' || variant === 'ghost' ? '#FF6B35' : '#FFFFFF';

    return (
      <ActivityIndicator
        size='small'
        color={indicatorColor}
        style={styles.loadingIndicator}
      />
    );
  };

  // 渲染左侧图标
  const renderLeftIcon = () => {
    if (!leftIcon || loading) return null;

    return <View style={styles.leftIcon}>{leftIcon}</View>;
  };

  // 渲染右侧图标
  const renderRightIcon = () => {
    if (!rightIcon || loading) return null;

    return <View style={styles.rightIcon}>{rightIcon}</View>;
  };

  // 渲染按钮文本
  const renderText = () => {
    const text = loading && loadingText ? loadingText : children;

    if (typeof text === 'string') {
      return <Text style={textStyles}>{text}</Text>;
    }

    return text;
  };

  return (
    <View style={containerStyle}>
      <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
        <Pressable
          style={[styles.button, buttonStyles]}
          onPress={handlePress}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          disabled={isDisabled}
          android_ripple={{
            color: 'rgba(255, 255, 255, 0.2)',
            borderless: false,
          }}
          {...pressableProps}
        >
          {/* 按下状态覆盖层（iOS） */}
          {isPressed && Platform.OS === 'ios' && (
            <View style={styles.pressedOverlay} />
          )}

          {/* 按钮内容 */}
          <View style={styles.content}>
            {renderLoadingIndicator()}
            {renderLeftIcon()}
            {renderText()}
            {renderRightIcon()}
          </View>
        </Pressable>
      </Animated.View>
    </View>
  );
};

export default Button;
