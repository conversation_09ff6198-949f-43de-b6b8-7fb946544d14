import { ReactNode } from 'react';
import { PressableProps, StyleProp, ViewStyle } from 'react-native';

export type ButtonVariant =
  | 'primary'
  | 'secondary'
  | 'danger'
  | 'outline'
  | 'ghost';
export type ButtonSize = 'small' | 'medium' | 'large';

export interface ButtonProps
  extends Omit<PressableProps, 'children' | 'style'> {
  /**
   * 按钮文本或内容
   */
  children: ReactNode;

  /**
   * 按钮样式变体
   * @default 'primary'
   */
  variant?: ButtonVariant;

  /**
   * 按钮尺寸
   * @default 'medium'
   */
  size?: ButtonSize;

  /**
   * 是否为加载状态
   * @default false
   */
  loading?: boolean;

  /**
   * 加载状态文本
   */
  loadingText?: string;

  /**
   * 是否禁用
   * @default false
   */
  disabled?: boolean;

  /**
   * 是否全宽
   * @default false
   */
  fullWidth?: boolean;

  /**
   * 左侧图标
   */
  leftIcon?: ReactNode;

  /**
   * 右侧图标
   */
  rightIcon?: ReactNode;

  /**
   * 容器样式（应用于最外层View）
   */
  containerStyle?: StyleProp<ViewStyle>;

  /**
   * 自定义样式类名
   */
  className?: string;

  /**
   * 点击事件
   */
  onPress?: () => void;
}
