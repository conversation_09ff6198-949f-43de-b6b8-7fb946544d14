import { fireEvent, render } from '@testing-library/react-native';
import React from 'react';
import { Text } from 'react-native';
import { Button } from '../Button';

describe('Button', () => {
  it('renders correctly with default props', () => {
    const { getByText } = render(<Button>Test Button</Button>);
    expect(getByText('Test Button')).toBeTruthy();
  });

  it('renders with different variants', () => {
    const { getByTestId } = render(
      <>
        <Button testID='primary'>Primary</Button>
        <Button testID='secondary' variant='secondary'>
          Secondary
        </Button>
        <Button testID='danger' variant='danger'>
          Danger
        </Button>
        <Button testID='outline' variant='outline'>
          Outline
        </Button>
        <Button testID='ghost' variant='ghost'>
          Ghost
        </Button>
      </>
    );

    expect(getByTestId('primary')).toBeTruthy();
    expect(getByTestId('secondary')).toBeTruthy();
    expect(getByTestId('danger')).toBeTruthy();
    expect(getByTestId('outline')).toBeTruthy();
    expect(getByTestId('ghost')).toBeTruthy();
  });

  it('renders with different sizes', () => {
    const { getByTestId } = render(
      <>
        <Button testID='small' size='small'>
          Small
        </Button>
        <Button testID='medium' size='medium'>
          Medium
        </Button>
        <Button testID='large' size='large'>
          Large
        </Button>
      </>
    );

    expect(getByTestId('small')).toBeTruthy();
    expect(getByTestId('medium')).toBeTruthy();
    expect(getByTestId('large')).toBeTruthy();
  });

  it('handles onPress correctly', () => {
    const mockPress = jest.fn();
    const { getByText } = render(<Button onPress={mockPress}>Press Me</Button>);

    fireEvent.press(getByText('Press Me'));
    expect(mockPress).toHaveBeenCalledTimes(1);
  });

  it('shows loading state correctly', () => {
    const { getByText } = render(
      <Button loading loadingText='Loading...'>
        Submit
      </Button>
    );

    expect(getByText('Loading...')).toBeTruthy();
  });

  it('is disabled when disabled prop is true', () => {
    const mockPress = jest.fn();
    const { getByText } = render(
      <Button disabled onPress={mockPress}>
        Disabled
      </Button>
    );

    const button = getByText('Disabled');
    fireEvent.press(button);
    expect(mockPress).not.toHaveBeenCalled();
  });

  it('is disabled when loading', () => {
    const mockPress = jest.fn();
    const { getByTestId } = render(
      <Button testID='loading-button' loading onPress={mockPress}>
        Loading
      </Button>
    );

    const button = getByTestId('loading-button');
    fireEvent.press(button);
    expect(mockPress).not.toHaveBeenCalled();
  });

  it('renders with left and right icons', () => {
    const leftIcon = <Text testID='left-icon'>🔥</Text>;
    const rightIcon = <Text testID='right-icon'>→</Text>;

    const { getByTestId } = render(
      <Button leftIcon={leftIcon} rightIcon={rightIcon}>
        Button with Icons
      </Button>
    );

    expect(getByTestId('left-icon')).toBeTruthy();
    expect(getByTestId('right-icon')).toBeTruthy();
  });

  it('applies full width style when fullWidth is true', () => {
    const { getByText } = render(<Button fullWidth>Full Width</Button>);

    expect(getByText('Full Width')).toBeTruthy();
  });

  it('renders React element as children', () => {
    const { getByTestId } = render(
      <Button>
        <Text testID='custom-text'>Custom Content</Text>
      </Button>
    );

    expect(getByTestId('custom-text')).toBeTruthy();
  });

  it('shows loading text when loading and loadingText provided', () => {
    const { getByText, queryByText } = render(
      <Button loading loadingText='Processing...'>
        Submit
      </Button>
    );

    expect(getByText('Processing...')).toBeTruthy();
    expect(queryByText('Submit')).toBeNull();
  });

  it('shows original text when not loading', () => {
    const { getByText, queryByText } = render(
      <Button loading={false} loadingText='Processing...'>
        Submit
      </Button>
    );

    expect(getByText('Submit')).toBeTruthy();
    expect(queryByText('Processing...')).toBeNull();
  });

  it('applies container style correctly', () => {
    const containerStyle = { marginTop: 20 };
    const { getByTestId } = render(
      <Button testID='styled-button' containerStyle={containerStyle}>
        Styled Button
      </Button>
    );

    expect(getByTestId('styled-button')).toBeTruthy();
  });

  it('handles press events with animation', () => {
    const mockPress = jest.fn();
    const { getByText } = render(
      <Button onPress={mockPress}>Animated Button</Button>
    );

    const button = getByText('Animated Button');
    fireEvent.press(button);

    expect(mockPress).toHaveBeenCalledTimes(1);
  });

  it('does not call onPress when disabled', () => {
    const mockPress = jest.fn();
    const { getByText } = render(
      <Button disabled onPress={mockPress}>
        Disabled Button
      </Button>
    );

    const button = getByText('Disabled Button');
    fireEvent.press(button);

    expect(mockPress).not.toHaveBeenCalled();
  });

  it('does not call onPress when loading', () => {
    const mockPress = jest.fn();
    const { getByText } = render(
      <Button loading onPress={mockPress}>
        Loading Button
      </Button>
    );

    const button = getByText('Loading Button');
    fireEvent.press(button);

    expect(mockPress).not.toHaveBeenCalled();
  });

  it('renders without onPress handler', () => {
    const { getByText } = render(<Button>No Handler</Button>);

    const button = getByText('No Handler');
    fireEvent.press(button);

    // Should not throw error
    expect(button).toBeTruthy();
  });
});
