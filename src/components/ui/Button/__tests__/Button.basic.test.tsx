import React from 'react';
import { render } from '@testing-library/react-native';
import { Button } from '../Button';

describe('Button Component - Basic Tests', () => {
  it('renders correctly with text', () => {
    const { getByText } = render(<Button>Test Button</Button>);

    expect(getByText('Test Button')).toBeTruthy();
  });

  it('renders with different variants', () => {
    const { getByText } = render(
      <Button variant='primary'>Primary Button</Button>
    );

    expect(getByText('Primary Button')).toBeTruthy();
  });

  it('renders when disabled', () => {
    const { getByText } = render(<Button disabled>Disabled Button</Button>);

    expect(getByText('Disabled Button')).toBeTruthy();
  });

  it('renders when loading', () => {
    const { getByText } = render(<Button loading>Loading Button</Button>);

    expect(getByText('Loading Button')).toBeTruthy();
  });
});
