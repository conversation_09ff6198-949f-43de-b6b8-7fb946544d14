import { StyleSheet, ViewStyle } from 'react-native';
import { ButtonSize, ButtonVariant } from './Button.types';

// 颜色配置
export const colors = {
  primary: '#FF6B35', // 攸家主色调橙色
  primaryDark: '#E55A2B',
  secondary: '#6B73FF',
  secondaryDark: '#5A62E6',
  danger: '#FF4757',
  dangerDark: '#FF3742',
  outline: '#DDD',
  outlineDark: '#CCC',
  ghost: 'transparent',
  white: '#FFFFFF',
  black: '#000000',
  gray: '#8E8E93',
  lightGray: '#F2F2F7',
  disabled: '#C7C7CC',
  disabledText: '#8E8E93',
};

// 尺寸配置
export const sizes = {
  small: {
    height: 32,
    paddingHorizontal: 12,
    fontSize: 14,
    iconSize: 16,
    borderRadius: 6,
  },
  medium: {
    height: 44,
    paddingHorizontal: 16,
    fontSize: 16,
    iconSize: 20,
    borderRadius: 8,
  },
  large: {
    height: 56,
    paddingHorizontal: 24,
    fontSize: 18,
    iconSize: 24,
    borderRadius: 12,
  },
};

// 获取按钮样式
export const getButtonStyles = (
  variant: ButtonVariant,
  size: ButtonSize,
  disabled: boolean,
  fullWidth: boolean
): ViewStyle => {
  const sizeConfig = sizes[size];

  const baseStyle: ViewStyle = {
    height: sizeConfig.height,
    paddingHorizontal: sizeConfig.paddingHorizontal,
    borderRadius: sizeConfig.borderRadius,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: variant === 'outline' ? 1 : 0,
    width: fullWidth ? '100%' : undefined,
    opacity: disabled ? 0.6 : 1,
  };

  // 根据变体设置颜色
  let backgroundColor: string;
  let borderColor: string;

  switch (variant) {
    case 'primary':
      backgroundColor = disabled ? colors.disabled : colors.primary;
      borderColor = 'transparent';
      break;
    case 'secondary':
      backgroundColor = disabled ? colors.disabled : colors.secondary;
      borderColor = 'transparent';
      break;
    case 'danger':
      backgroundColor = disabled ? colors.disabled : colors.danger;
      borderColor = 'transparent';
      break;
    case 'outline':
      backgroundColor = 'transparent';
      borderColor = disabled ? colors.disabled : colors.outline;
      break;
    case 'ghost':
      backgroundColor = 'transparent';
      borderColor = 'transparent';
      break;
    default:
      backgroundColor = colors.primary;
      borderColor = 'transparent';
  }

  return {
    ...baseStyle,
    backgroundColor,
    borderColor,
  };
};

// 获取文本样式
export const getTextStyles = (
  variant: ButtonVariant,
  size: ButtonSize,
  disabled: boolean
) => {
  const sizeConfig = sizes[size];

  const baseStyle = {
    fontSize: sizeConfig.fontSize,
    fontWeight: '600' as const,
    textAlign: 'center' as const,
  };

  // 根据变体设置文本颜色
  let color: string;

  if (disabled) {
    color = colors.disabledText;
  } else {
    switch (variant) {
      case 'primary':
      case 'secondary':
      case 'danger':
        color = colors.white;
        break;
      case 'outline':
        color = colors.black;
        break;
      case 'ghost':
        color = colors.primary;
        break;
      default:
        color = colors.white;
    }
  }

  return {
    ...baseStyle,
    color,
  };
};

// 基础样式
export const styles = StyleSheet.create({
  button: {
    position: 'relative',
    overflow: 'hidden',
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  leftIcon: {
    marginRight: 8,
  },
  rightIcon: {
    marginLeft: 8,
  },
  loadingIndicator: {
    marginRight: 8,
  },
  pressedOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
  },
});
