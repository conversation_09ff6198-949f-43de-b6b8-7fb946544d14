export interface Device {
  id: string;
  deviceNumber: string;
  deviceType: DeviceType;
  status: DeviceStatus;
  location: Location;
  version: string;
  isOnline: boolean;
  credentials: DeviceCredentials;
  createdAt: string;
  updatedAt: string;
}

export enum DeviceType {
  DOOR_LOCK = 'door_lock',
  GATEWAY = 'gateway',
  CALLER = 'caller',
}

export enum DeviceStatus {
  NORMAL = 'normal',
  OFFLINE = 'offline',
  FAULT = 'fault',
  MAINTENANCE = 'maintenance',
}

export interface Location {
  community: string;
  building?: string;
  unit?: string;
  floor?: string;
  room?: string;
}

export interface DeviceCredentials {
  passwordCount: number;
  nfcCount: number;
  faceCount: number;
  serverPasswordCount: number;
  serverNfcCount: number;
  serverFaceCount: number;
}
