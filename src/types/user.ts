/**
 * 用户信息类型定义
 */
export interface User {
  /** 用户ID */
  id: string;
  /** 用户名 */
  username: string;
  /** 真实姓名 */
  realName: string;
  /** 昵称 */
  nickname?: string;
  /** 手机号 */
  phone: string;
  /** 邮箱 */
  email?: string;
  /** 头像URL */
  avatar?: string;
  /** 用户状态 */
  status: UserStatus;
  /** 用户角色 */
  roles: string[];
  /** 用户权限 */
  permissions: string[];
  /** 所属组织/小区 */
  organization?: string;
  /** 岗位 */
  position?: string;
  /** 创建时间 */
  createTime: string;
  /** 更新时间 */
  updateTime: string;
}

/**
 * 用户状态枚举
 */
export enum UserStatus {
  /** 正常 */
  NORMAL = 'normal',
  /** 禁用 */
  DISABLED = 'disabled',
  /** 已删除 */
  DELETED = 'deleted',
}

/**
 * 用户登录请求
 */
export interface LoginRequest {
  /** 手机号 */
  phone: string;
  /** 验证码 */
  code: string;
}

/**
 * 用户登录响应
 */
export interface LoginResponse {
  /** 访问令牌 */
  accessToken: string;
  /** 刷新令牌 */
  refreshToken: string;
  /** 用户信息 */
  user: User;
  /** token过期时间 */
  expiresIn: number;
}

/**
 * 发送验证码请求
 */
export interface SendCodeRequest {
  /** 手机号 */
  phone: string;
  /** 验证码类型 */
  type?: 'login' | 'register' | 'reset_password';
}

/**
 * 刷新令牌请求
 */
export interface RefreshTokenRequest {
  /** 刷新令牌 */
  refreshToken: string;
}

/**
 * 刷新令牌响应
 */
export interface RefreshTokenResponse {
  /** 新的访问令牌 */
  accessToken: string;
  /** 新的刷新令牌 */
  refreshToken: string;
  /** token过期时间 */
  expiresIn: number;
}

/**
 * 修改用户信息请求
 */
export interface UpdateUserRequest {
  /** 真实姓名 */
  realName?: string;
  /** 昵称 */
  nickname?: string;
  /** 邮箱 */
  email?: string;
  /** 头像URL */
  avatar?: string;
}

/**
 * 修改手机号请求
 */
export interface UpdatePhoneRequest {
  /** 新手机号 */
  newPhone: string;
  /** 新手机号验证码 */
  newPhoneCode: string;
  /** 旧手机号验证码 */
  oldPhoneCode: string;
}

/**
 * 住户信息类型定义
 */
export interface Resident {
  /** 住户ID */
  id: string;
  /** 真实姓名 */
  realName: string;
  /** 手机号 */
  phone: string;
  /** 头像URL */
  avatar?: string;
  /** 户室信息 */
  room: string;
  /** 身份类型 */
  identity: 'owner' | 'tenant' | 'family';
  /** 登记方式 */
  registrationMethod: 'self' | 'property' | 'admin';
  /** 门禁密码状态 */
  passwordStatus: 'set' | 'unset';
  /** 人像状态 */
  faceStatus: 'recorded' | 'unrecorded';
  /** 门卡状态和信息 */
  nfcCards: NfcCard[];
  /** 登记时间 */
  registrationTime: string;
  /** 所属小区 */
  community: string;
  /** 楼栋 */
  building?: string;
  /** 单元 */
  unit?: string;
  /** 楼层 */
  floor?: string;
  /** 可访问的设备列表 */
  accessibleDevices: string[];
}

/**
 * 员工信息类型定义
 */
export interface Staff {
  /** 员工ID */
  id: string;
  /** 真实姓名 */
  realName: string;
  /** 手机号 */
  phone: string;
  /** 头像URL */
  avatar?: string;
  /** 岗位 */
  position: string;
  /** 所属小区/组织 */
  organization: string;
  /** 门禁密码状态 */
  passwordStatus: 'set' | 'unset';
  /** 人像状态 */
  faceStatus: 'recorded' | 'unrecorded';
  /** 门卡状态和信息 */
  nfcCards: NfcCard[];
  /** 门禁权限范围 */
  accessScope: string[];
  /** 添加时间 */
  addTime: string;
  /** 可访问的设备列表 */
  accessibleDevices: string[];
}

/**
 * NFC门卡信息
 */
export interface NfcCard {
  /** 卡号 */
  cardNumber: string;
  /** 卡片类型 */
  cardType: string;
  /** 录入时间 */
  recordTime: string;
}

/**
 * 数据下发请求
 */
export interface DataSyncRequest {
  /** 用户ID列表 */
  userIds: string[];
  /** 数据类型 */
  dataTypes: DataType[];
  /** 目标设备ID列表 */
  deviceIds: string[];
}

/**
 * 数据类型枚举
 */
export type DataType = 'password' | 'nfc' | 'face' | 'all';

/**
 * 数据下发响应
 */
export interface DataSyncResponse {
  /** 任务ID */
  taskId: string;
  /** 下发状态 */
  status: 'pending' | 'processing' | 'completed' | 'failed';
  /** 成功数量 */
  successCount: number;
  /** 失败数量 */
  failedCount: number;
  /** 总数量 */
  totalCount: number;
  /** 错误信息 */
  errors?: string[];
}

/**
 * 用户搜索筛选参数
 */
export interface UserSearchParams {
  /** 搜索关键词（姓名或手机号） */
  keyword?: string;
  /** 小区 */
  community?: string;
  /** 楼栋 */
  building?: string;
  /** 单元 */
  unit?: string;
  /** 楼层 */
  floor?: string;
  /** 户室 */
  room?: string;
  /** 页码 */
  page?: number;
  /** 每页数量 */
  pageSize?: number;
}

/**
 * 删除门卡请求
 */
export interface DeleteNfcCardRequest {
  /** 用户ID */
  userId: string;
  /** 卡号 */
  cardNumber: string;
}

/**
 * 邻里互助帖子类型定义
 */
export interface MutualAidPost {
  /** 帖子ID */
  id: string;
  /** 标题 */
  title: string;
  /** 内容 */
  content: string;
  /** 标签类型 */
  tag: 'seeking' | 'lost-found' | 'idle-items';
  /** 发布者信息 */
  publisher: {
    id: string;
    realName: string;
    avatar?: string;
    room: string;
    phone: string;
  };
  /** 图片附件 */
  images: string[];
  /** 视频附件 */
  videos: string[];
  /** 发布时间 */
  publishTime: string;
  /** 状态 */
  status: 'normal' | 'deleted';
  /** 删除原因 */
  deleteReason?: string;
  /** 删除时间 */
  deleteTime?: string;
  /** 评论数量 */
  commentCount: number;
  /** 位置信息 */
  location: {
    community: string;
    building?: string;
    unit?: string;
    floor?: string;
    room?: string;
  };
}

/**
 * 评论类型定义
 */
export interface Comment {
  /** 评论ID */
  id: string;
  /** 帖子ID */
  postId: string;
  /** 评论内容 */
  content: string;
  /** 评论者信息 */
  commenter: {
    id: string;
    realName: string;
    avatar?: string;
    nickname?: string;
  };
  /** 评论时间 */
  commentTime: string;
  /** 状态 */
  status: 'normal' | 'deleted';
  /** 删除原因 */
  deleteReason?: string;
  /** 删除时间 */
  deleteTime?: string;
  /** 图片附件 */
  images?: string[];
}

/**
 * 举报类型定义
 */
export interface Report {
  /** 举报ID */
  id: string;
  /** 举报类型 */
  type: string;
  /** 举报人信息 */
  reporter: {
    id: string;
    realName: string;
    phone: string;
    avatar?: string;
  };
  /** 被举报人信息 */
  reported: {
    id: string;
    realName: string;
    phone: string;
    avatar?: string;
  };
  /** 举报说明 */
  description: string;
  /** 举报内容摘要 */
  contentSummary: string;
  /** 举报内容链接 */
  contentLink?: string;
  /** 证据图片 */
  evidenceImages: string[];
  /** 证据视频 */
  evidenceVideos: string[];
  /** 举报时间 */
  reportTime: string;
  /** 处理状态 */
  status: 'pending' | 'processing' | 'completed';
  /** 处理历史 */
  processHistory: ReportProcessRecord[];
}

/**
 * 举报处理记录
 */
export interface ReportProcessRecord {
  /** 记录ID */
  id: string;
  /** 处理类型 */
  type: 'reply' | 'warning' | 'ban' | 'delete-content';
  /** 处理内容 */
  content: string;
  /** 处理人 */
  processor: {
    id: string;
    realName: string;
  };
  /** 处理时间 */
  processTime: string;
}

/**
 * 邻里互助搜索参数
 */
export interface MutualAidSearchParams {
  /** 搜索关键词（发布人姓名或手机号） */
  keyword?: string;
  /** 标签筛选 */
  tag?: 'seeking' | 'lost-found' | 'idle-items';
  /** 状态筛选 */
  status?: 'normal' | 'deleted';
  /** 小区 */
  community?: string;
  /** 楼栋 */
  building?: string;
  /** 单元 */
  unit?: string;
  /** 楼层 */
  floor?: string;
  /** 户室 */
  room?: string;
  /** 页码 */
  page?: number;
  /** 每页数量 */
  pageSize?: number;
}

/**
 * 举报搜索参数
 */
export interface ReportSearchParams {
  /** 搜索关键词（举报人姓名或手机号） */
  keyword?: string;
  /** 举报类型筛选 */
  type?: string;
  /** 处理状态筛选 */
  status?: 'pending' | 'processing' | 'completed';
  /** 时间范围 */
  startTime?: string;
  endTime?: string;
  /** 页码 */
  page?: number;
  /** 每页数量 */
  pageSize?: number;
}

/**
 * 删除帖子请求
 */
export interface DeletePostRequest {
  /** 帖子ID */
  postId: string;
  /** 删除原因 */
  reason: string;
}

/**
 * 删除评论请求
 */
export interface DeleteCommentRequest {
  /** 评论ID */
  commentId: string;
  /** 删除原因 */
  reason?: string;
}

/**
 * 处理举报请求
 */
export interface ProcessReportRequest {
  /** 举报ID */
  reportId: string;
  /** 处理类型 */
  type: 'reply' | 'warning' | 'ban' | 'delete-content';
  /** 处理内容 */
  content: string;
}
