import { useAuthStore } from '@/stores/auth';

/**
 * 权限常量定义
 */
export const PERMISSIONS = {
  // 设备管理权限
  DEVICE_REGISTER: 'device:register',
  DEVICE_DEBUG: 'device:debug',
  DEVICE_UNBIND: 'device:unbind',
  DEVICE_CONTROL: 'device:control',

  // 人员管理权限
  RESIDENTS_VIEW: 'residents:view',
  RESIDENTS_MANAGE: 'residents:manage',
  STAFF_VIEW: 'staff:view',
  STAFF_MANAGE: 'staff:manage',

  // 社区管理权限
  COMMUNITY_MANAGE: 'community:manage',
  MUTUAL_AID_MANAGE: 'mutual-aid:manage',
  REPORTS_MANAGE: 'reports:manage',

  // 系统管理权限
  SYSTEM_ADMIN: 'system:admin',
  USER_MANAGE: 'user:manage',
} as const;

/**
 * 角色常量定义
 */
export const ROLES = {
  ADMIN: 'admin',
  MANAGER: 'manager',
  INSTALLER: 'installer',
  MAINTENANCE: 'maintenance',
  OPERATOR: 'operator',
} as const;

/**
 * 角色权限映射
 */
export const ROLE_PERMISSIONS = {
  [ROLES.ADMIN]: [
    PERMISSIONS.DEVICE_REGISTER,
    PERMISSIONS.DEVICE_DEBUG,
    PERMISSIONS.DEVICE_UNBIND,
    PERMISSIONS.DEVICE_CONTROL,
    PERMISSIONS.RESIDENTS_VIEW,
    PERMISSIONS.RESIDENTS_MANAGE,
    PERMISSIONS.STAFF_VIEW,
    PERMISSIONS.STAFF_MANAGE,
    PERMISSIONS.COMMUNITY_MANAGE,
    PERMISSIONS.MUTUAL_AID_MANAGE,
    PERMISSIONS.REPORTS_MANAGE,
    PERMISSIONS.SYSTEM_ADMIN,
    PERMISSIONS.USER_MANAGE,
  ],
  [ROLES.MANAGER]: [
    PERMISSIONS.DEVICE_REGISTER,
    PERMISSIONS.DEVICE_DEBUG,
    PERMISSIONS.DEVICE_UNBIND,
    PERMISSIONS.DEVICE_CONTROL,
    PERMISSIONS.RESIDENTS_VIEW,
    PERMISSIONS.RESIDENTS_MANAGE,
    PERMISSIONS.STAFF_VIEW,
    PERMISSIONS.COMMUNITY_MANAGE,
    PERMISSIONS.MUTUAL_AID_MANAGE,
    PERMISSIONS.REPORTS_MANAGE,
  ],
  [ROLES.INSTALLER]: [
    PERMISSIONS.DEVICE_REGISTER,
    PERMISSIONS.DEVICE_DEBUG,
    PERMISSIONS.DEVICE_UNBIND,
  ],
  [ROLES.MAINTENANCE]: [
    PERMISSIONS.DEVICE_DEBUG,
    PERMISSIONS.DEVICE_CONTROL,
    PERMISSIONS.RESIDENTS_VIEW,
    PERMISSIONS.STAFF_VIEW,
  ],
  [ROLES.OPERATOR]: [
    PERMISSIONS.RESIDENTS_VIEW,
    PERMISSIONS.STAFF_VIEW,
    PERMISSIONS.COMMUNITY_MANAGE,
    PERMISSIONS.MUTUAL_AID_MANAGE,
    PERMISSIONS.REPORTS_MANAGE,
  ],
};

/**
 * 权限检查工具类
 */
export class PermissionChecker {
  /**
   * 检查用户是否有指定权限
   */
  static hasPermission(permission: string): boolean {
    const { hasPermission } = useAuthStore.getState();
    return hasPermission(permission);
  }

  /**
   * 检查用户是否有指定角色
   */
  static hasRole(role: string): boolean {
    const { hasRole } = useAuthStore.getState();
    return hasRole(role);
  }

  /**
   * 检查用户是否有任意一个权限
   */
  static hasAnyPermission(permissions: string[]): boolean {
    const { hasAnyPermission } = useAuthStore.getState();
    return hasAnyPermission(permissions);
  }

  /**
   * 检查用户是否有所有权限
   */
  static hasAllPermissions(permissions: string[]): boolean {
    const { hasAllPermissions } = useAuthStore.getState();
    return hasAllPermissions(permissions);
  }

  /**
   * 根据角色获取权限列表
   */
  static getPermissionsByRole(role: string): string[] {
    return ROLE_PERMISSIONS[role as keyof typeof ROLE_PERMISSIONS] || [];
  }

  /**
   * 检查是否是管理员
   */
  static isAdmin(): boolean {
    return this.hasRole(ROLES.ADMIN);
  }

  /**
   * 检查是否可以访问设备管理功能
   */
  static canAccessDeviceManagement(): boolean {
    return this.hasAnyPermission([
      PERMISSIONS.DEVICE_REGISTER,
      PERMISSIONS.DEVICE_DEBUG,
      PERMISSIONS.DEVICE_UNBIND,
      PERMISSIONS.DEVICE_CONTROL,
    ]);
  }

  /**
   * 检查是否可以访问人员管理功能
   */
  static canAccessUserManagement(): boolean {
    return this.hasAnyPermission([
      PERMISSIONS.RESIDENTS_VIEW,
      PERMISSIONS.RESIDENTS_MANAGE,
      PERMISSIONS.STAFF_VIEW,
      PERMISSIONS.STAFF_MANAGE,
    ]);
  }

  /**
   * 检查是否可以访问社区管理功能
   */
  static canAccessCommunityManagement(): boolean {
    return this.hasAnyPermission([
      PERMISSIONS.COMMUNITY_MANAGE,
      PERMISSIONS.MUTUAL_AID_MANAGE,
      PERMISSIONS.REPORTS_MANAGE,
    ]);
  }
}

/**
 * 权限检查 Hook
 */
export function usePermissions() {
  const {
    hasPermission,
    hasRole,
    hasAnyPermission,
    hasAllPermissions,
    permissions,
    roles,
  } = useAuthStore();

  return {
    hasPermission,
    hasRole,
    hasAnyPermission,
    hasAllPermissions,
    permissions,
    roles,
    isAdmin: () => hasRole(ROLES.ADMIN),
    canAccessDeviceManagement: () =>
      hasAnyPermission([
        PERMISSIONS.DEVICE_REGISTER,
        PERMISSIONS.DEVICE_DEBUG,
        PERMISSIONS.DEVICE_UNBIND,
        PERMISSIONS.DEVICE_CONTROL,
      ]),
    canAccessUserManagement: () =>
      hasAnyPermission([
        PERMISSIONS.RESIDENTS_VIEW,
        PERMISSIONS.RESIDENTS_MANAGE,
        PERMISSIONS.STAFF_VIEW,
        PERMISSIONS.STAFF_MANAGE,
      ]),
    canAccessCommunityManagement: () =>
      hasAnyPermission([
        PERMISSIONS.COMMUNITY_MANAGE,
        PERMISSIONS.MUTUAL_AID_MANAGE,
        PERMISSIONS.REPORTS_MANAGE,
      ]),
  };
}
