export const DEVICE_TYPES = {
  DOOR_LOCK: 'door_lock',
  GATEWAY: 'gateway',
  CALLER: 'caller',
} as const;

export const USER_ROLES = {
  INSTALLER: 'installer',
  MAINTAINER: 'maintainer',
  ADMIN: 'admin',
  AUDITOR: 'auditor',
} as const;

export const PERMISSIONS = {
  DEVICE_REGISTER: 'device:register',
  DEVICE_DEBUG: 'device:debug',
  DEVICE_UNBIND: 'device:unbind',
  DEVICE_CONTROL: 'device:control',
  USER_VIEW: 'user:view',
  COMMUNITY_AUDIT: 'community:audit',
  REPORT_HANDLE: 'report:handle',
} as const;

export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: '/auth/login',
    LOGOUT: '/auth/logout',
    REFRESH: '/auth/refresh',
    SEND_CODE: '/auth/send-code',
  },
  DEVICE: {
    LIST: '/devices',
    REGISTER: '/devices/register',
    CONTROL: '/devices/control',
    UNBIND: '/devices/unbind',
  },
  USER: {
    RESIDENTS: '/users/residents',
    STAFF: '/users/staff',
  },
} as const;

// HTTP配置常量
export const REQUEST_TIMEOUT = 10000; // 10秒
export const MAX_RETRY_COUNT = 3; // 最大重试次数
export const RETRY_DELAY = 1000; // 重试延迟(毫秒)

// API基础地址
export const API_BASE_URL =
  process.env.API_BASE_URL || 'https://api-dev.youjia.com/v1';
