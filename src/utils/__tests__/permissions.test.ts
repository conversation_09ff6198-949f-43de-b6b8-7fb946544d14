import {
  PermissionChecker,
  PERMISSIONS,
  ROLE_PERMISSIONS,
  ROLES,
} from '../permissions';

// Mock the auth store
const mockAuthStore = {
  useAuthStore: {
    getState: jest.fn(() => ({
      hasPermission: jest.fn(),
      hasRole: jest.fn(),
      hasAnyPermission: jest.fn(),
      hasAllPermissions: jest.fn(),
    })),
  },
};

jest.mock('@/stores/auth', () => mockAuthStore);

describe('PermissionChecker', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('权限常量', () => {
    it('应该定义所有必要的权限常量', () => {
      expect(PERMISSIONS.DEVICE_REGISTER).toBe('device:register');
      expect(PERMISSIONS.DEVICE_DEBUG).toBe('device:debug');
      expect(PERMISSIONS.DEVICE_UNBIND).toBe('device:unbind');
      expect(PERMISSIONS.DEVICE_CONTROL).toBe('device:control');
      expect(PERMISSIONS.RESIDENTS_VIEW).toBe('residents:view');
      expect(PERMISSIONS.STAFF_VIEW).toBe('staff:view');
      expect(PERMISSIONS.COMMUNITY_MANAGE).toBe('community:manage');
    });
  });

  describe('角色常量', () => {
    it('应该定义所有必要的角色常量', () => {
      expect(ROLES.ADMIN).toBe('admin');
      expect(ROLES.MANAGER).toBe('manager');
      expect(ROLES.INSTALLER).toBe('installer');
      expect(ROLES.MAINTENANCE).toBe('maintenance');
      expect(ROLES.OPERATOR).toBe('operator');
    });
  });

  describe('角色权限映射', () => {
    it('管理员应该拥有所有权限', () => {
      const adminPermissions = ROLE_PERMISSIONS[ROLES.ADMIN];
      expect(adminPermissions).toContain(PERMISSIONS.DEVICE_REGISTER);
      expect(adminPermissions).toContain(PERMISSIONS.DEVICE_DEBUG);
      expect(adminPermissions).toContain(PERMISSIONS.DEVICE_CONTROL);
      expect(adminPermissions).toContain(PERMISSIONS.RESIDENTS_VIEW);
      expect(adminPermissions).toContain(PERMISSIONS.COMMUNITY_MANAGE);
    });

    it('安装员应该只有设备相关权限', () => {
      const installerPermissions = ROLE_PERMISSIONS[ROLES.INSTALLER];
      expect(installerPermissions).toContain(PERMISSIONS.DEVICE_REGISTER);
      expect(installerPermissions).toContain(PERMISSIONS.DEVICE_DEBUG);
      expect(installerPermissions).toContain(PERMISSIONS.DEVICE_UNBIND);
      expect(installerPermissions).not.toContain(PERMISSIONS.RESIDENTS_VIEW);
      expect(installerPermissions).not.toContain(PERMISSIONS.COMMUNITY_MANAGE);
    });
  });

  describe('getPermissionsByRole', () => {
    it('应该返回指定角色的权限列表', () => {
      const adminPermissions = PermissionChecker.getPermissionsByRole(
        ROLES.ADMIN
      );
      expect(Array.isArray(adminPermissions)).toBe(true);
      expect(adminPermissions.length).toBeGreaterThan(0);
    });

    it('应该为未知角色返回空数组', () => {
      const unknownPermissions =
        PermissionChecker.getPermissionsByRole('unknown');
      expect(unknownPermissions).toEqual([]);
    });
  });

  describe('便捷方法', () => {
    it('canAccessDeviceManagement 应该检查设备管理权限', () => {
      const mockHasAnyPermission = jest.fn().mockReturnValue(true);
      mockAuthStore.useAuthStore.getState.mockReturnValue({
        hasAnyPermission: mockHasAnyPermission,
      });

      const result = PermissionChecker.canAccessDeviceManagement();

      expect(mockHasAnyPermission).toHaveBeenCalledWith([
        PERMISSIONS.DEVICE_REGISTER,
        PERMISSIONS.DEVICE_DEBUG,
        PERMISSIONS.DEVICE_UNBIND,
        PERMISSIONS.DEVICE_CONTROL,
      ]);
      expect(result).toBe(true);
    });

    it('canAccessUserManagement 应该检查用户管理权限', () => {
      const mockHasAnyPermission = jest.fn().mockReturnValue(false);
      mockAuthStore.useAuthStore.getState.mockReturnValue({
        hasAnyPermission: mockHasAnyPermission,
      });

      const result = PermissionChecker.canAccessUserManagement();

      expect(mockHasAnyPermission).toHaveBeenCalledWith([
        PERMISSIONS.RESIDENTS_VIEW,
        PERMISSIONS.RESIDENTS_MANAGE,
        PERMISSIONS.STAFF_VIEW,
        PERMISSIONS.STAFF_MANAGE,
      ]);
      expect(result).toBe(false);
    });

    it('canAccessCommunityManagement 应该检查社区管理权限', () => {
      const mockHasAnyPermission = jest.fn().mockReturnValue(true);
      mockAuthStore.useAuthStore.getState.mockReturnValue({
        hasAnyPermission: mockHasAnyPermission,
      });

      const result = PermissionChecker.canAccessCommunityManagement();

      expect(mockHasAnyPermission).toHaveBeenCalledWith([
        PERMISSIONS.COMMUNITY_MANAGE,
        PERMISSIONS.MUTUAL_AID_MANAGE,
        PERMISSIONS.REPORTS_MANAGE,
      ]);
      expect(result).toBe(true);
    });
  });
});
