/**
 * 路由功能测试
 */

describe('路由配置测试', () => {
  describe('路由结构', () => {
    it('应该有正确的路由结构', () => {
      // 测试路由文件是否存在
      const routes = [
        'app/_layout.tsx',
        'app/index.tsx',
        'app/(auth)/_layout.tsx',
        'app/(auth)/login.tsx',
        'app/(tabs)/_layout.tsx',
        'app/(tabs)/device-register.tsx',
        'app/(tabs)/device-debug.tsx',
        'app/(tabs)/device-unbind.tsx',
        'app/(tabs)/device-control.tsx',
        'app/(tabs)/residents.tsx',
        'app/(tabs)/staff.tsx',
        'app/(tabs)/community/index.tsx',
        'app/(tabs)/profile.tsx',
      ];

      routes.forEach(route => {
        expect(() => require(`../../../${route}`)).not.toThrow();
      });
    });
  });

  describe('权限配置', () => {
    it('应该有正确的权限常量定义', () => {
      const { PERMISSIONS } = require('../permissions');

      expect(PERMISSIONS.DEVICE_REGISTER).toBe('device:register');
      expect(PERMISSIONS.DEVICE_DEBUG).toBe('device:debug');
      expect(PERMISSIONS.DEVICE_UNBIND).toBe('device:unbind');
      expect(PERMISSIONS.DEVICE_CONTROL).toBe('device:control');
      expect(PERMISSIONS.RESIDENTS_VIEW).toBe('residents:view');
      expect(PERMISSIONS.STAFF_VIEW).toBe('staff:view');
      expect(PERMISSIONS.COMMUNITY_MANAGE).toBe('community:manage');
    });

    it('应该有正确的角色权限映射', () => {
      const { ROLES, ROLE_PERMISSIONS } = require('../permissions');

      // 管理员应该有所有权限
      const adminPermissions = ROLE_PERMISSIONS[ROLES.ADMIN];
      expect(adminPermissions).toContain('device:register');
      expect(adminPermissions).toContain('device:debug');
      expect(adminPermissions).toContain('residents:view');
      expect(adminPermissions).toContain('community:manage');

      // 安装员应该只有设备相关权限
      const installerPermissions = ROLE_PERMISSIONS[ROLES.INSTALLER];
      expect(installerPermissions).toContain('device:register');
      expect(installerPermissions).toContain('device:debug');
      expect(installerPermissions).not.toContain('residents:view');
    });
  });

  describe('路由守卫', () => {
    it('RouteGuard组件应该正确导出', () => {
      expect(() => require('../../components/common/RouteGuard')).not.toThrow();
      const { RouteGuard } = require('../../components/common/RouteGuard');
      expect(RouteGuard).toBeDefined();
    });
  });
});

describe('导航配置测试', () => {
  it('Tab导航应该有正确的配置', () => {
    // 验证Tab导航配置文件存在
    expect(() => require('../../../app/(tabs)/_layout.tsx')).not.toThrow();
  });

  it('认证路由应该有正确的配置', () => {
    // 验证认证路由配置文件存在
    expect(() => require('../../../app/(auth)/_layout.tsx')).not.toThrow();
  });
});

describe('功能页面测试', () => {
  const pages = [
    'device-register',
    'device-debug',
    'device-unbind',
    'device-control',
    'residents',
    'staff',
    'profile',
  ];

  pages.forEach(page => {
    it(`${page}页面应该正确渲染`, () => {
      expect(() => require(`../../../app/(tabs)/${page}.tsx`)).not.toThrow();
    });
  });

  it('登录页面应该正确渲染', () => {
    expect(() => require('../../../app/(auth)/login.tsx')).not.toThrow();
  });

  it('社区管理页面应该正确渲染', () => {
    expect(() =>
      require('../../../app/(tabs)/community/index.tsx')
    ).not.toThrow();
    expect(() =>
      require('../../../app/(tabs)/community/mutual-aid.tsx')
    ).not.toThrow();
    expect(() =>
      require('../../../app/(tabs)/community/reports.tsx')
    ).not.toThrow();
  });
});
