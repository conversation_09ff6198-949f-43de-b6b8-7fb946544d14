import { authApi } from '@/services/api/auth';
import { useAppStore } from '@/stores/app';
import { useAuthStore } from '@/stores/auth';
import { LoginRequest, LoginResponse, User } from '@/types';
import { useCallback } from 'react';

export const useAuth = () => {
  const {
    isAuthenticated,
    user,
    token,
    refreshToken,
    isLoading,
    error,
    permissions,
    roles,
    login: setLoginState,
    logout: clearAuthState,
    setUser,
    setToken,
    setRefreshToken,
    setLoading,
    setError,
    setPermissions,
    setRoles,
    clearError,
    hasPermission,
    hasRole,
    hasAnyPermission,
    hasAllPermissions,
  } = useAuthStore();

  const { addToast, setGlobalLoading } = useAppStore();

  // 登录方法
  const login = useCallback(
    async (credentials: LoginRequest): Promise<boolean> => {
      try {
        setLoading(true);
        setGlobalLoading(true, '登录中...');
        clearError();

        const response: LoginResponse = await authApi.login(credentials);

        // 设置认证状态
        setLoginState(
          response.accessToken,
          response.user,
          response.refreshToken
        );

        // 设置用户权限
        setPermissions(response.user.permissions);
        setRoles(response.user.roles);

        addToast({
          type: 'success',
          title: '登录成功',
          message: `欢迎回来，${response.user.realName}！`,
        });

        return true;
      } catch (err: any) {
        const errorMessage = err?.message || '登录失败，请重试';
        setError(errorMessage);

        addToast({
          type: 'error',
          title: '登录失败',
          message: errorMessage,
        });

        return false;
      } finally {
        setLoading(false);
        setGlobalLoading(false);
      }
    },
    [
      setLoading,
      setGlobalLoading,
      clearError,
      setLoginState,
      setPermissions,
      setRoles,
      addToast,
      setError,
    ]
  );

  // 登出方法
  const logout = useCallback(async (): Promise<void> => {
    try {
      if (token) {
        await authApi.logout();
      }
    } catch (err) {
      console.warn('Logout API call failed:', err);
    } finally {
      clearAuthState();
      addToast({
        type: 'info',
        title: '已退出登录',
        message: '您已安全退出系统',
      });
    }
  }, [token, clearAuthState, addToast]);

  // 刷新令牌
  const refreshAccessToken = useCallback(async (): Promise<boolean> => {
    if (!refreshToken) {
      return false;
    }

    try {
      const response = await authApi.refreshToken({ refreshToken });
      setToken(response.accessToken);
      setRefreshToken(response.refreshToken);
      return true;
    } catch (err) {
      console.warn('Token refresh failed:', err);
      // Token刷新失败，需要重新登录
      clearAuthState();
      return false;
    }
  }, [refreshToken, setToken, setRefreshToken, clearAuthState]);

  // 获取用户信息
  const fetchUserProfile = useCallback(async (): Promise<User | null> => {
    if (!isAuthenticated) {
      return null;
    }

    try {
      const userProfile = await authApi.getUserProfile();
      setUser(userProfile);
      setPermissions(userProfile.permissions);
      setRoles(userProfile.roles);
      return userProfile;
    } catch (err) {
      console.warn('Failed to fetch user profile:', err);
      return null;
    }
  }, [isAuthenticated, setUser, setPermissions, setRoles]);

  // 发送验证码
  const sendVerificationCode = useCallback(
    async (phone: string): Promise<boolean> => {
      try {
        setLoading(true);
        await authApi.sendCode({ phone, type: 'login' });

        addToast({
          type: 'success',
          title: '验证码已发送',
          message: `验证码已发送至 ${phone}`,
        });

        return true;
      } catch (err: any) {
        const errorMessage = err?.message || '验证码发送失败';
        setError(errorMessage);

        addToast({
          type: 'error',
          title: '发送失败',
          message: errorMessage,
        });

        return false;
      } finally {
        setLoading(false);
      }
    },
    [setLoading, addToast, setError]
  );

  // 权限检查方法（增强版）
  const checkPermission = useCallback(
    (permission: string): boolean => {
      return isAuthenticated && hasPermission(permission);
    },
    [isAuthenticated, hasPermission]
  );

  const checkRole = useCallback(
    (role: string): boolean => {
      return isAuthenticated && hasRole(role);
    },
    [isAuthenticated, hasRole]
  );

  const checkAnyPermission = useCallback(
    (permissions: string[]): boolean => {
      return isAuthenticated && hasAnyPermission(permissions);
    },
    [isAuthenticated, hasAnyPermission]
  );

  const checkAllPermissions = useCallback(
    (permissions: string[]): boolean => {
      return isAuthenticated && hasAllPermissions(permissions);
    },
    [isAuthenticated, hasAllPermissions]
  );

  // 检查是否需要刷新token
  const shouldRefreshToken = useCallback((): boolean => {
    if (!token || !refreshToken) {
      return false;
    }

    try {
      // 简单的token过期检查
      const payload = JSON.parse(atob(token.split('.')[1]));
      const exp = payload.exp * 1000; // 转换为毫秒
      const now = Date.now();
      const bufferTime = 5 * 60 * 1000; // 5分钟缓冲时间

      return exp - now < bufferTime;
    } catch {
      return true; // 解析失败，尝试刷新
    }
  }, [token, refreshToken]);

  return {
    // 状态
    isAuthenticated,
    user,
    token,
    refreshToken,
    isLoading,
    error,
    permissions,
    roles,

    // 方法
    login,
    logout,
    refreshAccessToken,
    fetchUserProfile,
    sendVerificationCode,
    clearError,

    // 权限检查
    checkPermission,
    checkRole,
    checkAnyPermission,
    checkAllPermissions,
    hasPermission,
    hasRole,
    hasAnyPermission,
    hasAllPermissions,

    // 工具方法
    shouldRefreshToken,
  };
};
