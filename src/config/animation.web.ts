/**
 * Web端动画配置 - 解决useNativeDriver警告
 */

import { Platform } from 'react-native';

// 只在Web端执行
if (Platform.OS === 'web') {
  // 重写Animated模块的默认配置
  const originalAnimated = require('react-native').Animated;
  
  if (originalAnimated) {
    // 重写timing方法，默认禁用useNativeDriver
    const originalTiming = originalAnimated.timing;
    originalAnimated.timing = (value: any, config: any) => {
      return originalTiming(value, {
        ...config,
        useNativeDriver: false, // 强制禁用原生驱动
      });
    };

    // 重写spring方法
    const originalSpring = originalAnimated.spring;
    originalAnimated.spring = (value: any, config: any) => {
      return originalSpring(value, {
        ...config,
        useNativeDriver: false, // 强制禁用原生驱动
      });
    };

    // 重写decay方法
    const originalDecay = originalAnimated.decay;
    originalAnimated.decay = (value: any, config: any) => {
      return originalDecay(value, {
        ...config,
        useNativeDriver: false, // 强制禁用原生驱动
      });
    };
  }

  // 设置全局动画配置
  if (typeof window !== 'undefined') {
    // 禁用所有原生动画驱动
    (window as any).REACT_NATIVE_DISABLE_NATIVE_DRIVER = true;
    
    // 设置动画配置
    (window as any).__REACT_NATIVE_WEB_ANIMATION_CONFIG__ = {
      useNativeDriver: false,
      disableNativeDriver: true,
    };
  }
}

export {};
