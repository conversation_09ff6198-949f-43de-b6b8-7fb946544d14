export const theme = {
  colors: {
    primary: '#FF6B35',
    primaryLight: '#FFE5DC',
    secondary: '#2196F3',
    success: '#4CAF50',
    warning: '#FF9800',
    error: '#F44336',
    danger: '#F44336',
    info: '#2196F3',

    background: '#FFFFFF',
    surface: '#F5F5F5',

    white: '#FFFFFF',
    black: '#000000',

    text: '#212121',
    textSecondary: '#757575',
    textDisabled: '#BDBDBD',

    border: '#E0E0E0',
    divider: '#EEEEEE',
  },

  typography: {
    h1: { fontSize: 24, fontWeight: '700' },
    h2: { fontSize: 20, fontWeight: '600' },
    h3: { fontSize: 18, fontWeight: '600' },
    body: { fontSize: 16, fontWeight: '400' },
    caption: { fontSize: 14, fontWeight: '400' },
    small: { fontSize: 12, fontWeight: '400' },
  },

  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
  },

  borderRadius: {
    sm: 4,
    md: 8,
    lg: 12,
    xl: 16,
  },
};

export type Theme = typeof theme;
