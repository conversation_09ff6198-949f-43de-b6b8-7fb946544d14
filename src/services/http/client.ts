import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';

import { API_BASE_URL, REQUEST_TIMEOUT } from '@/utils/constants';
import {
  RequestInterceptorConfig,
  ResponseInterceptorConfig,
  setupRequestInterceptor,
  setupResponseInterceptor,
} from './interceptors';

/**
 * HTTP客户端配置
 */
export interface HttpClientConfig {
  baseURL?: string;
  timeout?: number;
  headers?: Record<string, string>;
  requestInterceptor?: RequestInterceptorConfig;
  responseInterceptor?: ResponseInterceptorConfig;
}

/**
 * HTTP客户端类
 */
export class HttpClient {
  private instance: AxiosInstance;

  constructor(config: HttpClientConfig = {}) {
    // 创建axios实例
    this.instance = axios.create({
      baseURL: config.baseURL || API_BASE_URL,
      timeout: config.timeout || REQUEST_TIMEOUT,
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
        ...config.headers,
      },
    });

    // 设置拦截器
    this.setupInterceptors(config);
  }

  /**
   * 设置请求和响应拦截器
   */
  private setupInterceptors(config: HttpClientConfig = {}): void {
    // 设置请求拦截器
    setupRequestInterceptor(this.instance, config.requestInterceptor);

    // 设置响应拦截器
    setupResponseInterceptor(this.instance, config.responseInterceptor);
  }

  /**
   * GET请求
   */
  async get<T = any>(
    url: string,
    config?: AxiosRequestConfig
  ): Promise<AxiosResponse<T>> {
    return this.instance.get<T>(url, config);
  }

  /**
   * POST请求
   */
  async post<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<AxiosResponse<T>> {
    return this.instance.post<T>(url, data, config);
  }

  /**
   * PUT请求
   */
  async put<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<AxiosResponse<T>> {
    return this.instance.put<T>(url, data, config);
  }

  /**
   * DELETE请求
   */
  async delete<T = any>(
    url: string,
    config?: AxiosRequestConfig
  ): Promise<AxiosResponse<T>> {
    return this.instance.delete<T>(url, config);
  }

  /**
   * PATCH请求
   */
  async patch<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<AxiosResponse<T>> {
    return this.instance.patch<T>(url, data, config);
  }

  /**
   * 获取axios实例
   */
  getInstance(): AxiosInstance {
    return this.instance;
  }

  /**
   * 设置默认请求头
   */
  setDefaultHeader(key: string, value: string): void {
    this.instance.defaults.headers.common[key] = value;
  }

  /**
   * 移除默认请求头
   */
  removeDefaultHeader(key: string): void {
    delete this.instance.defaults.headers.common[key];
  }

  /**
   * 设置Authorization头
   */
  setAuthToken(token: string): void {
    this.setDefaultHeader('Authorization', `Bearer ${token}`);
  }

  /**
   * 清除Authorization头
   */
  clearAuthToken(): void {
    this.removeDefaultHeader('Authorization');
  }
}

// 创建默认的HTTP客户端实例
export const httpClient = new HttpClient();

// 导出默认实例的方法
export const { get, post, put, delete: del, patch } = httpClient;
