// HTTP客户端
export { del, get, HttpClient, httpClient, patch, post, put } from './client';

// 拦截器
export {
  setupRequestInterceptor,
  setupResponseInterceptor,
  type RequestInterceptorConfig,
  type ResponseInterceptorConfig,
} from './interceptors';

// 错误处理
export {
  createHttpError,
  formatErrorMessage,
  HttpError,
  HttpErrorType,
  isRetryableError,
  shouldRefreshToken,
} from './error-handler';

// 重新导出类型
export type { HttpClientConfig } from './client';
