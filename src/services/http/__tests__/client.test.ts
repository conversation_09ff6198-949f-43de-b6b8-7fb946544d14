import { HttpClient } from '../client';
import { HttpError, HttpErrorType } from '../error-handler';

// Mock axios
jest.mock('axios', () => ({
  create: jest.fn(() => ({
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn(),
    patch: jest.fn(),
    interceptors: {
      request: {
        use: jest.fn(),
      },
      response: {
        use: jest.fn(),
      },
    },
    defaults: {
      headers: {
        common: {},
      },
    },
  })),
}));

describe('HttpClient', () => {
  let httpClient: HttpClient;

  beforeEach(() => {
    httpClient = new HttpClient();
  });

  describe('constructor', () => {
    it('should create instance with default config', () => {
      expect(httpClient).toBeInstanceOf(HttpClient);
    });

    it('should create instance with custom config', () => {
      const customClient = new HttpClient({
        baseURL: 'https://custom.api.com',
        timeout: 5000,
        headers: {
          'Custom-Header': 'value',
        },
      });
      expect(customClient).toBeInstanceOf(HttpClient);
    });
  });

  describe('HTTP methods', () => {
    it('should have get method', () => {
      expect(typeof httpClient.get).toBe('function');
    });

    it('should have post method', () => {
      expect(typeof httpClient.post).toBe('function');
    });

    it('should have put method', () => {
      expect(typeof httpClient.put).toBe('function');
    });

    it('should have delete method', () => {
      expect(typeof httpClient.delete).toBe('function');
    });

    it('should have patch method', () => {
      expect(typeof httpClient.patch).toBe('function');
    });
  });

  describe('token management', () => {
    it('should set auth token', () => {
      const token = 'test-token';
      httpClient.setAuthToken(token);
      // 验证token是否设置成功需要mock axios实例
    });

    it('should clear auth token', () => {
      httpClient.clearAuthToken();
      // 验证token是否清除成功需要mock axios实例
    });
  });
});

describe('HttpError', () => {
  it('should create HttpError with correct properties', () => {
    const error = new HttpError(
      'Test error',
      HttpErrorType.CLIENT_ERROR,
      400,
      'TEST_CODE',
      { test: 'data' }
    );

    expect(error.message).toBe('Test error');
    expect(error.type).toBe(HttpErrorType.CLIENT_ERROR);
    expect(error.status).toBe(400);
    expect(error.code).toBe('TEST_CODE');
    expect(error.data).toEqual({ test: 'data' });
  });
});
