/**
 * HTTP客户端功能演示和验证
 * 这个文件用于验证HTTP客户端的基本功能是否正常工作
 */

import { httpClient, HttpError, HttpErrorType } from './index';

/**
 * 演示基本HTTP请求功能
 */
export async function demoBasicRequests() {
  console.log('🚀 HTTP客户端功能演示开始...');

  try {
    // 演示GET请求
    console.log('📡 测试GET请求...');
    // 注意：这里使用一个公共API进行测试，实际项目中会使用自己的API
    const getResponse = await httpClient.get(
      'https://jsonplaceholder.typicode.com/posts/1'
    );
    console.log('✅ GET请求成功:', getResponse.data);

    // 演示POST请求
    console.log('📡 测试POST请求...');
    const postData = {
      title: 'Test Post',
      body: 'This is a test post from YouJia App',
      userId: 1,
    };
    const postResponse = await httpClient.post(
      'https://jsonplaceholder.typicode.com/posts',
      postData
    );
    console.log('✅ POST请求成功:', postResponse.data);

    console.log('🎉 HTTP客户端基本功能验证完成！');
    return true;
  } catch (error) {
    console.error('❌ HTTP客户端功能验证失败:', error);
    return false;
  }
}

/**
 * 演示错误处理功能
 */
export async function demoErrorHandling() {
  console.log('🔧 HTTP错误处理功能演示开始...');

  try {
    // 测试404错误
    console.log('📡 测试404错误处理...');
    await httpClient.get('https://jsonplaceholder.typicode.com/posts/999999');
  } catch (error) {
    if (error instanceof HttpError) {
      console.log('✅ 404错误处理正常:', {
        type: error.type,
        status: error.status,
        message: error.message,
      });
    } else {
      console.log('⚠️ 错误类型不匹配:', error);
    }
  }

  try {
    // 测试网络错误
    console.log('📡 测试网络错误处理...');
    await httpClient.get('https://invalid-domain-that-does-not-exist.com/api');
  } catch (error) {
    if (error instanceof HttpError) {
      console.log('✅ 网络错误处理正常:', {
        type: error.type,
        message: error.message,
      });
    } else {
      console.log('⚠️ 错误类型不匹配:', error);
    }
  }

  console.log('🎉 HTTP错误处理功能验证完成！');
}

/**
 * 演示拦截器功能
 */
export function demoInterceptors() {
  console.log('🔧 HTTP拦截器功能演示开始...');

  // 设置认证Token
  httpClient.setAuthToken('demo-token-12345');
  console.log('✅ 认证Token设置完成');

  // 设置自定义请求头
  httpClient.setDefaultHeader('X-App-Version', '2.0.0');
  console.log('✅ 自定义请求头设置完成');

  // 清除认证Token
  httpClient.clearAuthToken();
  console.log('✅ 认证Token清除完成');

  console.log('🎉 HTTP拦截器功能验证完成！');
}

/**
 * 演示HttpError类型判断
 */
export function demoErrorTypes() {
  console.log('🔧 HTTP错误类型演示开始...');

  // 创建不同类型的错误
  const authError = new HttpError(
    '认证失败',
    HttpErrorType.AUTH_ERROR,
    401,
    'AUTH_FAILED'
  );

  const networkError = new HttpError(
    '网络连接失败',
    HttpErrorType.NETWORK_ERROR
  );

  const serverError = new HttpError(
    '服务器内部错误',
    HttpErrorType.SERVER_ERROR,
    500
  );

  console.log('✅ 认证错误:', {
    type: authError.type,
    status: authError.status,
    message: authError.message,
  });

  console.log('✅ 网络错误:', {
    type: networkError.type,
    message: networkError.message,
  });

  console.log('✅ 服务器错误:', {
    type: serverError.type,
    status: serverError.status,
    message: serverError.message,
  });

  console.log('🎉 HTTP错误类型演示完成！');
}

/**
 * 运行所有演示
 */
export async function runAllDemos() {
  console.log('🎯 开始运行HTTP客户端完整功能演示...\n');

  // 基本请求功能
  const basicSuccess = await demoBasicRequests();
  console.log('');

  // 错误处理功能
  await demoErrorHandling();
  console.log('');

  // 拦截器功能
  demoInterceptors();
  console.log('');

  // 错误类型功能
  demoErrorTypes();
  console.log('');

  console.log('🏁 HTTP客户端功能演示全部完成！');
  console.log(`📊 基本功能测试: ${basicSuccess ? '✅ 通过' : '❌ 失败'}`);

  return basicSuccess;
}
