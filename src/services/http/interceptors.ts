import {
  AxiosError,
  AxiosInstance,
  AxiosResponse,
  InternalAxiosRequestConfig,
} from 'axios';
import { v4 as uuidv4 } from 'uuid';

import { MAX_RETRY_COUNT, RETRY_DELAY } from '@/utils/constants';
import {
  createHttpError,
  isRetryableError,
  shouldRefreshToken,
} from './error-handler';

// 声明全局变量
declare const __DEV__: boolean;

/**
 * 请求拦截器配置
 */
export interface RequestInterceptorConfig {
  addRequestId?: boolean;
  addTimestamp?: boolean;
  logRequests?: boolean;
}

/**
 * 响应拦截器配置
 */
export interface ResponseInterceptorConfig {
  enableRetry?: boolean;
  maxRetryCount?: number;
  retryDelay?: number;
  logResponses?: boolean;
}

/**
 * 设置请求拦截器
 */
export function setupRequestInterceptor(
  instance: AxiosInstance,
  config: RequestInterceptorConfig = {}
): void {
  const {
    addRequestId = true,
    addTimestamp = true,
    logRequests = __DEV__,
  } = config;

  instance.interceptors.request.use(
    (requestConfig: InternalAxiosRequestConfig) => {
      // 添加请求ID用于追踪
      if (addRequestId) {
        requestConfig.headers['X-Request-ID'] = uuidv4();
      }

      // 添加时间戳
      if (addTimestamp) {
        requestConfig.headers['X-Timestamp'] = Date.now().toString();
      }

      // 自动添加Authorization header
      const token = getAuthToken();
      if (token && !requestConfig.headers['Authorization']) {
        requestConfig.headers['Authorization'] = `Bearer ${token}`;
      }

      // 处理请求参数格式化
      if (requestConfig.data && typeof requestConfig.data === 'object') {
        // 移除值为undefined的字段
        requestConfig.data = removeUndefinedFields(requestConfig.data);
      }

      // 开发环境下记录请求日志
      if (logRequests) {
        console.log('🚀 HTTP Request:', {
          method: requestConfig.method?.toUpperCase(),
          url: requestConfig.url,
          headers: requestConfig.headers,
          data: requestConfig.data,
          params: requestConfig.params,
        });
      }

      return requestConfig;
    },
    (error: AxiosError) => {
      console.error('❌ Request Error:', error);
      return Promise.reject(error);
    }
  );
}

/**
 * 设置响应拦截器
 */
export function setupResponseInterceptor(
  instance: AxiosInstance,
  config: ResponseInterceptorConfig = {}
): void {
  const {
    enableRetry = true,
    maxRetryCount = MAX_RETRY_COUNT,
    retryDelay = RETRY_DELAY,
    logResponses = __DEV__,
  } = config;

  instance.interceptors.response.use(
    (response: AxiosResponse) => {
      // 开发环境下记录响应日志
      if (logResponses) {
        console.log('✅ HTTP Response:', {
          status: response.status,
          statusText: response.statusText,
          url: response.config.url,
          data: response.data,
          headers: response.headers,
        });
      }

      // 统一处理响应格式
      return handleResponseFormat(response);
    },
    async (error: AxiosError) => {
      // 开发环境下记录错误日志
      if (logResponses) {
        console.error('❌ HTTP Error:', {
          status: error.response?.status,
          statusText: error.response?.statusText,
          url: error.config?.url,
          data: error.response?.data,
          message: error.message,
        });
      }

      // 创建标准化错误对象
      const httpError = createHttpError(error);

      // 处理Token自动刷新
      if (shouldRefreshToken(httpError)) {
        const refreshed = await handleTokenRefresh();
        if (refreshed && error.config) {
          // 重新发送原请求
          return instance.request(error.config);
        }
      }

      // 实现重试机制
      if (enableRetry && isRetryableError(httpError)) {
        return handleRetry(instance, error, maxRetryCount, retryDelay);
      }

      // 返回标准化错误
      return Promise.reject(httpError);
    }
  );
}

/**
 * 获取认证Token
 */
function getAuthToken(): string | null {
  // 这里应该从存储中获取token
  // 暂时返回null，后续在认证模块中实现
  return null;
}

/**
 * 移除对象中值为undefined的字段
 */
function removeUndefinedFields(obj: any): any {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(removeUndefinedFields);
  }

  const result: any = {};
  for (const [key, value] of Object.entries(obj)) {
    if (value !== undefined) {
      result[key] = removeUndefinedFields(value);
    }
  }
  return result;
}

/**
 * 处理响应格式
 */
function handleResponseFormat(response: AxiosResponse): AxiosResponse {
  // 这里可以统一处理响应数据格式
  // 例如：提取data字段、处理分页信息等
  return response;
}

/**
 * 处理Token刷新
 */
async function handleTokenRefresh(): Promise<boolean> {
  // 这里应该实现Token刷新逻辑
  // 暂时返回false，后续在认证模块中实现
  return false;
}

/**
 * 处理重试逻辑
 */
async function handleRetry(
  instance: AxiosInstance,
  error: AxiosError,
  maxRetryCount: number,
  retryDelay: number
): Promise<AxiosResponse> {
  const config = error.config as any;

  // 初始化重试计数
  if (!config.__retryCount) {
    config.__retryCount = 0;
  }

  // 检查是否超过最大重试次数
  if (config.__retryCount >= maxRetryCount) {
    return Promise.reject(error);
  }

  // 增加重试计数
  config.__retryCount += 1;

  // 等待指定时间后重试
  await new Promise(resolve => setTimeout(resolve, retryDelay));

  console.log(
    `🔄 Retrying request (${config.__retryCount}/${maxRetryCount}):`,
    config.url
  );

  // 重新发送请求
  return instance.request(config);
}
