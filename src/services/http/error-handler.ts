import { AxiosError } from 'axios';

/**
 * API错误响应数据结构
 */
interface ApiErrorData {
  message?: string;
  error?: string;
  code?: string;
  [key: string]: any;
}

/**
 * HTTP错误类型
 */
export enum HttpErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  AUTH_ERROR = 'AUTH_ERROR',
  PERMISSION_ERROR = 'PERMISSION_ERROR',
  NOT_FOUND_ERROR = 'NOT_FOUND_ERROR',
  SERVER_ERROR = 'SERVER_ERROR',
  CLIENT_ERROR = 'CLIENT_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

/**
 * 自定义HTTP错误类
 */
export class HttpError extends Error {
  public readonly type: HttpErrorType;
  public readonly status?: number;
  public readonly code?: string;
  public readonly data?: any;

  constructor(
    message: string,
    type: HttpErrorType,
    status?: number,
    code?: string,
    data?: any
  ) {
    super(message);
    this.name = 'HttpError';
    this.type = type;
    this.status = status;
    this.code = code;
    this.data = data;
  }
}

/**
 * 错误消息映射
 */
const ERROR_MESSAGES = {
  [HttpErrorType.NETWORK_ERROR]: '网络连接失败，请检查网络设置',
  [HttpErrorType.TIMEOUT_ERROR]: '请求超时，请稍后重试',
  [HttpErrorType.AUTH_ERROR]: '认证失败，请重新登录',
  [HttpErrorType.PERMISSION_ERROR]: '权限不足，无法访问该资源',
  [HttpErrorType.NOT_FOUND_ERROR]: '请求的资源不存在',
  [HttpErrorType.SERVER_ERROR]: '服务器内部错误，请稍后重试',
  [HttpErrorType.CLIENT_ERROR]: '请求参数错误',
  [HttpErrorType.UNKNOWN_ERROR]: '未知错误，请稍后重试',
} as const;

/**
 * 根据Axios错误创建HttpError
 */
export function createHttpError(error: AxiosError): HttpError {
  // 网络错误
  if (!error.response) {
    if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
      return new HttpError(
        ERROR_MESSAGES[HttpErrorType.TIMEOUT_ERROR],
        HttpErrorType.TIMEOUT_ERROR,
        undefined,
        error.code
      );
    }
    return new HttpError(
      ERROR_MESSAGES[HttpErrorType.NETWORK_ERROR],
      HttpErrorType.NETWORK_ERROR,
      undefined,
      error.code
    );
  }

  const { status, data } = error.response;
  const errorData = data as ApiErrorData;
  const serverMessage = errorData?.message || errorData?.error;

  // 根据状态码分类错误
  switch (status) {
    case 400:
      return new HttpError(
        serverMessage || ERROR_MESSAGES[HttpErrorType.CLIENT_ERROR],
        HttpErrorType.CLIENT_ERROR,
        status,
        errorData?.code,
        errorData
      );

    case 401:
      return new HttpError(
        serverMessage || ERROR_MESSAGES[HttpErrorType.AUTH_ERROR],
        HttpErrorType.AUTH_ERROR,
        status,
        errorData?.code,
        errorData
      );

    case 403:
      return new HttpError(
        serverMessage || ERROR_MESSAGES[HttpErrorType.PERMISSION_ERROR],
        HttpErrorType.PERMISSION_ERROR,
        status,
        errorData?.code,
        errorData
      );

    case 404:
      return new HttpError(
        serverMessage || ERROR_MESSAGES[HttpErrorType.NOT_FOUND_ERROR],
        HttpErrorType.NOT_FOUND_ERROR,
        status,
        errorData?.code,
        errorData
      );

    case 500:
    case 502:
    case 503:
    case 504:
      return new HttpError(
        serverMessage || ERROR_MESSAGES[HttpErrorType.SERVER_ERROR],
        HttpErrorType.SERVER_ERROR,
        status,
        errorData?.code,
        errorData
      );

    default:
      return new HttpError(
        serverMessage || ERROR_MESSAGES[HttpErrorType.UNKNOWN_ERROR],
        HttpErrorType.UNKNOWN_ERROR,
        status,
        errorData?.code,
        errorData
      );
  }
}

/**
 * 判断错误是否可以重试
 */
export function isRetryableError(error: HttpError): boolean {
  return [
    HttpErrorType.NETWORK_ERROR,
    HttpErrorType.TIMEOUT_ERROR,
    HttpErrorType.SERVER_ERROR,
  ].includes(error.type);
}

/**
 * 判断是否需要刷新Token
 */
export function shouldRefreshToken(error: HttpError): boolean {
  return error.type === HttpErrorType.AUTH_ERROR && error.status === 401;
}

/**
 * 格式化错误信息用于显示
 */
export function formatErrorMessage(error: HttpError): string {
  // 在开发环境显示详细错误信息
  if (__DEV__) {
    return `${error.message} (${error.type}${error.status ? ` - ${error.status}` : ''})`;
  }

  // 生产环境显示用户友好的错误信息
  return error.message;
}
