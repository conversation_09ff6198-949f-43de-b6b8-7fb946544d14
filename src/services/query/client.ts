import { QueryClient } from '@tanstack/react-query';

// 创建查询客户端配置
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // 默认缓存时间 5 分钟
      staleTime: 5 * 60 * 1000,
      // 数据在缓存中的时间 10 分钟
      gcTime: 10 * 60 * 1000,
      // 窗口聚焦时重新获取数据
      refetchOnWindowFocus: true,
      // 网络重连时重新获取数据
      refetchOnReconnect: true,
      // 默认重试 3 次
      retry: (failureCount, error: any) => {
        // 对于 401 和 403 错误不重试
        if (error?.status === 401 || error?.status === 403) {
          return false;
        }
        // 最多重试 3 次
        return failureCount < 3;
      },
      // 重试延迟
      retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
    },
    mutations: {
      // 默认重试 1 次
      retry: 1,
      // 重试延迟
      retryDelay: 1000,
    },
  },
});

// 查询键工厂
export const queryKeys = {
  // 用户相关
  user: {
    all: ['user'] as const,
    profile: () => [...queryKeys.user.all, 'profile'] as const,
    permissions: () => [...queryKeys.user.all, 'permissions'] as const,
  },
  // 设备相关
  device: {
    all: ['device'] as const,
    list: (filters?: any) =>
      [...queryKeys.device.all, 'list', filters] as const,
    detail: (id: string) => [...queryKeys.device.all, 'detail', id] as const,
    status: (id: string) => [...queryKeys.device.all, 'status', id] as const,
  },
  // 住户相关
  resident: {
    all: ['resident'] as const,
    list: (filters?: any) =>
      [...queryKeys.resident.all, 'list', filters] as const,
    detail: (id: string) => [...queryKeys.resident.all, 'detail', id] as const,
  },
  // 员工相关
  staff: {
    all: ['staff'] as const,
    list: (filters?: any) => [...queryKeys.staff.all, 'list', filters] as const,
    detail: (id: string) => [...queryKeys.staff.all, 'detail', id] as const,
  },
  // 社区相关
  community: {
    all: ['community'] as const,
    posts: (filters?: any) =>
      [...queryKeys.community.all, 'posts', filters] as const,
    post: (id: string) => [...queryKeys.community.all, 'post', id] as const,
  },
  // 举报相关
  report: {
    all: ['report'] as const,
    list: (filters?: any) =>
      [...queryKeys.report.all, 'list', filters] as const,
    detail: (id: string) => [...queryKeys.report.all, 'detail', id] as const,
  },
} as const;
