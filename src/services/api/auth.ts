import { httpClient } from '@/services/http';
import {
  LoginRequest,
  LoginResponse,
  RefreshTokenRequest,
  RefreshTokenResponse,
  SendCodeRequest,
  UpdatePhoneRequest,
  UpdateUserRequest,
  User,
} from '@/types';

/**
 * 认证相关API
 */
export const authApi = {
  /**
   * 发送验证码
   */
  sendCode: async (data: SendCodeRequest): Promise<void> => {
    const response = await httpClient.post('/auth/send-code', data);
    return response.data;
  },

  /**
   * 用户登录
   */
  login: async (data: LoginRequest): Promise<LoginResponse> => {
    const response = await httpClient.post<LoginResponse>('/auth/login', data);
    return response.data;
  },

  /**
   * 刷新访问令牌
   */
  refreshToken: async (
    data: RefreshTokenRequest
  ): Promise<RefreshTokenResponse> => {
    const response = await httpClient.post<RefreshTokenResponse>(
      '/auth/refresh-token',
      data
    );
    return response.data;
  },

  /**
   * 用户登出
   */
  logout: async (): Promise<void> => {
    const response = await httpClient.post('/auth/logout');
    return response.data;
  },

  /**
   * 获取用户信息
   */
  getUserProfile: async (): Promise<User> => {
    const response = await httpClient.get<User>('/auth/profile');
    return response.data;
  },

  /**
   * 更新用户信息
   */
  updateUserProfile: async (data: UpdateUserRequest): Promise<User> => {
    const response = await httpClient.put<User>('/auth/profile', data);
    return response.data;
  },

  /**
   * 上传用户头像
   */
  uploadAvatar: async (formData: FormData): Promise<{ avatarUrl: string }> => {
    const response = await httpClient.post<{ avatarUrl: string }>(
      '/auth/upload-avatar',
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
    return response.data;
  },

  /**
   * 发送旧手机号验证码（修改手机号时使用）
   */
  sendOldPhoneCode: async (): Promise<void> => {
    const response = await httpClient.post('/auth/send-old-phone-code');
    return response.data;
  },

  /**
   * 发送新手机号验证码（修改手机号时使用）
   */
  sendNewPhoneCode: async (phone: string): Promise<void> => {
    const response = await httpClient.post('/auth/send-new-phone-code', {
      phone,
    });
    return response.data;
  },

  /**
   * 修改手机号
   */
  updatePhone: async (data: UpdatePhoneRequest): Promise<void> => {
    const response = await httpClient.put('/auth/phone', data);
    return response.data;
  },

  /**
   * 注销账户
   */
  deleteAccount: async (): Promise<void> => {
    const response = await httpClient.delete('/auth/account');
    return response.data;
  },

  /**
   * 检查手机号是否已注册
   */
  checkPhoneExists: async (phone: string): Promise<{ exists: boolean }> => {
    const response = await httpClient.get<{ exists: boolean }>(
      `/auth/check-phone/${phone}`
    );
    return response.data;
  },

  /**
   * 验证验证码是否正确（不执行登录）
   */
  verifyCode: async (
    phone: string,
    code: string
  ): Promise<{ valid: boolean }> => {
    const response = await httpClient.post<{ valid: boolean }>(
      '/auth/verify-code',
      { phone, code }
    );
    return response.data;
  },
};
