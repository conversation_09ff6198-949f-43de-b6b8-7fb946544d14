import { AxiosResponse } from 'axios';

import { httpClient } from '../http/client';
import { ApiResponse } from '@/types/api';

/**
 * API服务基类
 */
export abstract class BaseApiService {
  protected client = httpClient;

  /**
   * 处理API响应
   */
  protected handleResponse<T>(response: AxiosResponse<ApiResponse<T>>): T {
    const { data } = response;

    // 检查业务状态码
    if (data.code !== 200) {
      throw new Error(data.message || '请求失败');
    }

    return data.data;
  }

  /**
   * GET请求
   */
  protected async get<T>(url: string, params?: any): Promise<T> {
    const response = await this.client.get<ApiResponse<T>>(url, { params });
    return this.handleResponse(response);
  }

  /**
   * POST请求
   */
  protected async post<T>(url: string, data?: any): Promise<T> {
    const response = await this.client.post<ApiResponse<T>>(url, data);
    return this.handleResponse(response);
  }

  /**
   * PUT请求
   */
  protected async put<T>(url: string, data?: any): Promise<T> {
    const response = await this.client.put<ApiResponse<T>>(url, data);
    return this.handleResponse(response);
  }

  /**
   * DELETE请求
   */
  protected async delete<T>(url: string): Promise<T> {
    const response = await this.client.delete<ApiResponse<T>>(url);
    return this.handleResponse(response);
  }

  /**
   * PATCH请求
   */
  protected async patch<T>(url: string, data?: any): Promise<T> {
    const response = await this.client.patch<ApiResponse<T>>(url, data);
    return this.handleResponse(response);
  }

  /**
   * 上传文件
   */
  protected async upload<T>(
    url: string,
    formData: FormData,
    onProgress?: (progress: number) => void
  ): Promise<T> {
    const response = await this.client.post<ApiResponse<T>>(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: progressEvent => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round(
            (progressEvent.loaded * 100) / progressEvent.total
          );
          onProgress(progress);
        }
      },
    });
    return this.handleResponse(response);
  }

  /**
   * 下载文件
   */
  protected async download(
    url: string,
    params?: any,
    onProgress?: (progress: number) => void
  ): Promise<Blob> {
    const response = await this.client.get(url, {
      params,
      responseType: 'blob',
      onDownloadProgress: progressEvent => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round(
            (progressEvent.loaded * 100) / progressEvent.total
          );
          onProgress(progress);
        }
      },
    });
    return response.data;
  }

  /**
   * 设置认证Token
   */
  protected setAuthToken(token: string): void {
    this.client.setAuthToken(token);
  }

  /**
   * 清除认证Token
   */
  protected clearAuthToken(): void {
    this.client.clearAuthToken();
  }
}
