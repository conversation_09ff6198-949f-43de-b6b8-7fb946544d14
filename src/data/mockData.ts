import { LocationItem } from '@/components/business';

// 模拟位置数据
export const mockLocationData: LocationItem[] = [
  {
    id: 'community_1',
    name: '未来城小区',
    children: [
      {
        id: 'building_1',
        name: '1号楼',
        children: [
          {
            id: 'unit_1_1',
            name: '1单元',
            children: [
              {
                id: 'floor_1_1_1',
                name: '1楼',
                children: [
                  { id: 'room_1_1_1_101', name: '101室' },
                  { id: 'room_1_1_1_102', name: '102室' },
                  { id: 'room_1_1_1_103', name: '103室' },
                  { id: 'room_1_1_1_104', name: '104室' },
                ]
              },
              {
                id: 'floor_1_1_2',
                name: '2楼',
                children: [
                  { id: 'room_1_1_2_201', name: '201室' },
                  { id: 'room_1_1_2_202', name: '202室' },
                  { id: 'room_1_1_2_203', name: '203室' },
                  { id: 'room_1_1_2_204', name: '204室' },
                ]
              },
              {
                id: 'floor_1_1_3',
                name: '3楼',
                children: [
                  { id: 'room_1_1_3_301', name: '301室' },
                  { id: 'room_1_1_3_302', name: '302室' },
                  { id: 'room_1_1_3_303', name: '303室' },
                  { id: 'room_1_1_3_304', name: '304室' },
                ]
              },
            ]
          },
          {
            id: 'unit_1_2',
            name: '2单元',
            children: [
              {
                id: 'floor_1_2_1',
                name: '1楼',
                children: [
                  { id: 'room_1_2_1_101', name: '101室' },
                  { id: 'room_1_2_1_102', name: '102室' },
                ]
              },
              {
                id: 'floor_1_2_2',
                name: '2楼',
                children: [
                  { id: 'room_1_2_2_201', name: '201室' },
                  { id: 'room_1_2_2_202', name: '202室' },
                ]
              },
            ]
          },
        ]
      },
      {
        id: 'building_2',
        name: '2号楼',
        children: [
          {
            id: 'unit_2_1',
            name: '1单元',
            children: [
              {
                id: 'floor_2_1_1',
                name: '1楼',
                children: [
                  { id: 'room_2_1_1_101', name: '101室' },
                  { id: 'room_2_1_1_102', name: '102室' },
                ]
              },
            ]
          },
        ]
      },
    ]
  },
  {
    id: 'community_2',
    name: '艾依雅居小区',
    children: [
      {
        id: 'building_3',
        name: 'A栋',
        children: [
          {
            id: 'unit_3_1',
            name: '1单元',
            children: [
              {
                id: 'floor_3_1_1',
                name: '1楼',
                children: [
                  { id: 'room_3_1_1_101', name: '101室' },
                  { id: 'room_3_1_1_102', name: '102室' },
                ]
              },
            ]
          },
        ]
      },
    ]
  },
];

// 功能设施类型
export const mockFacilityTypes = [
  { id: 'ground_unit_door', name: '地上单元门' },
  { id: 'underground_unit_door', name: '地下单元门' },
  { id: 'community_gate', name: '小区大门' },
  { id: 'garage_door', name: '车棚门' },
  { id: 'building_entrance', name: '楼栋入口' },
];

// 模拟设备数据
export interface MockDevice {
  id: string;
  deviceNumber: string;
  type: 'door' | 'gateway' | 'caller';
  location: string;
  locationPath: string[];
  status: 'online' | 'offline';
  facilityType?: string;
  openToResidents?: boolean;
  residentScope?: string[];
  remark?: string;
  registrationTime: string;
  version: string;
  // 门禁实录数据
  deviceData: {
    faces: number;
    passwords: number;
    nfc: number;
  };
  // 服务实录数据
  serverData: {
    faces: number;
    passwords: number;
    nfc: number;
  };
}

export const mockDevices: MockDevice[] = [
  {
    id: 'device_001',
    deviceNumber: 'YJ001234567890',
    type: 'door',
    location: '未来城小区 1号楼 1单元',
    locationPath: ['未来城小区', '1号楼', '1单元'],
    status: 'online',
    facilityType: '地上单元门',
    openToResidents: true,
    residentScope: ['1号楼1单元'],
    remark: '主入口门禁',
    registrationTime: '2024-12-15 10:30:00',
    version: 'V2.1.3',
    deviceData: { faces: 45, passwords: 12, nfc: 38 },
    serverData: { faces: 47, passwords: 12, nfc: 40 },
  },
  {
    id: 'device_002',
    deviceNumber: 'YJ001234567891',
    type: 'door',
    location: '未来城小区 1号楼 2单元',
    locationPath: ['未来城小区', '1号楼', '2单元'],
    status: 'offline',
    facilityType: '地上单元门',
    openToResidents: true,
    residentScope: ['1号楼2单元'],
    registrationTime: '2024-12-14 14:20:00',
    version: 'V2.1.2',
    deviceData: { faces: 23, passwords: 8, nfc: 19 },
    serverData: { faces: 25, passwords: 8, nfc: 21 },
  },
  {
    id: 'device_003',
    deviceNumber: 'YJ001234567892',
    type: 'gateway',
    location: '未来城小区 1号楼 1单元 2楼 201室',
    locationPath: ['未来城小区', '1号楼', '1单元', '2楼', '201室'],
    status: 'online',
    registrationTime: '2024-12-13 09:15:00',
    version: 'V1.5.1',
    deviceData: { faces: 0, passwords: 0, nfc: 0 },
    serverData: { faces: 0, passwords: 0, nfc: 0 },
  },
  {
    id: 'device_004',
    deviceNumber: 'YJ001234567893',
    type: 'caller',
    location: '未来城小区',
    locationPath: ['未来城小区'],
    status: 'online',
    remark: '物业前台呼叫器',
    registrationTime: '2024-12-12 16:45:00',
    version: 'V1.2.0',
    deviceData: { faces: 0, passwords: 0, nfc: 0 },
    serverData: { faces: 0, passwords: 0, nfc: 0 },
  },
];

// 模拟用户调试数据
export interface MockUserDebugData {
  deviceId: string;
  userId: string;
  password?: string;
  nfcCard?: string;
  faceImage?: string;
  createdAt: string;
}

export const mockUserDebugData: MockUserDebugData[] = [
  {
    deviceId: 'device_001',
    userId: 'current_user',
    password: '123456',
    nfcCard: 'NFC_001234',
    faceImage: 'face_image_url',
    createdAt: '2024-12-19 10:30:00',
  },
];

// 导入住户和员工类型
import {
    Resident,
    Staff
} from '@/types/user';

// 模拟住户数据
export const mockResidents: Resident[] = [
  {
    id: 'resident_001',
    realName: '张三',
    phone: '138****1234',
    avatar: 'https://via.placeholder.com/50',
    room: '1号楼1单元101室',
    identity: 'owner',
    registrationMethod: 'self',
    passwordStatus: 'set',
    faceStatus: 'recorded',
    nfcCards: [
      {
        cardNumber: 'NFC_001234',
        cardType: 'IC卡',
        recordTime: '2024-12-15 10:30:00',
      },
    ],
    registrationTime: '2024-12-15 10:30:00',
    community: '未来城小区',
    building: '1号楼',
    unit: '1单元',
    floor: '1楼',
    accessibleDevices: ['device_001', 'device_003'],
  },
  {
    id: 'resident_002',
    realName: '李四',
    phone: '139****5678',
    room: '1号楼1单元102室',
    identity: 'tenant',
    registrationMethod: 'property',
    passwordStatus: 'unset',
    faceStatus: 'unrecorded',
    nfcCards: [],
    registrationTime: '2024-12-14 14:20:00',
    community: '未来城小区',
    building: '1号楼',
    unit: '1单元',
    floor: '1楼',
    accessibleDevices: ['device_001'],
  },
  {
    id: 'resident_003',
    realName: '王五',
    phone: '136****9012',
    avatar: 'https://via.placeholder.com/50',
    room: '1号楼1单元201室',
    identity: 'owner',
    registrationMethod: 'self',
    passwordStatus: 'set',
    faceStatus: 'recorded',
    nfcCards: [
      {
        cardNumber: 'NFC_002345',
        cardType: 'IC卡',
        recordTime: '2024-12-13 09:15:00',
      },
      {
        cardNumber: 'NFC_002346',
        cardType: '手机NFC',
        recordTime: '2024-12-13 09:20:00',
      },
    ],
    registrationTime: '2024-12-13 09:15:00',
    community: '未来城小区',
    building: '1号楼',
    unit: '1单元',
    floor: '2楼',
    accessibleDevices: ['device_001', 'device_003'],
  },
  {
    id: 'resident_004',
    realName: '赵六',
    phone: '137****3456',
    room: '1号楼2单元101室',
    identity: 'family',
    registrationMethod: 'admin',
    passwordStatus: 'set',
    faceStatus: 'unrecorded',
    nfcCards: [],
    registrationTime: '2024-12-12 16:45:00',
    community: '未来城小区',
    building: '1号楼',
    unit: '2单元',
    floor: '1楼',
    accessibleDevices: ['device_002'],
  },
  {
    id: 'resident_005',
    realName: '孙七',
    phone: '135****7890',
    avatar: 'https://via.placeholder.com/50',
    room: '2号楼1单元101室',
    identity: 'owner',
    registrationMethod: 'self',
    passwordStatus: 'set',
    faceStatus: 'recorded',
    nfcCards: [
      {
        cardNumber: 'NFC_003456',
        cardType: 'IC卡',
        recordTime: '2024-12-11 11:30:00',
      },
    ],
    registrationTime: '2024-12-11 11:30:00',
    community: '未来城小区',
    building: '2号楼',
    unit: '1单元',
    floor: '1楼',
    accessibleDevices: ['device_005'],
  },
];

// 模拟员工数据
export const mockStaff: Staff[] = [
  {
    id: 'staff_001',
    realName: '物业管理员',
    phone: '188****1111',
    avatar: 'https://via.placeholder.com/50',
    position: '物业经理',
    organization: '未来城小区',
    passwordStatus: 'set',
    faceStatus: 'recorded',
    nfcCards: [
      {
        cardNumber: 'STAFF_001',
        cardType: '员工卡',
        recordTime: '2024-12-01 09:00:00',
      },
    ],
    accessScope: ['未来城小区', '1号楼', '2号楼', '地下车库'],
    addTime: '2024-12-01 09:00:00',
    accessibleDevices: ['device_001', 'device_002', 'device_003', 'device_004', 'device_005'],
  },
  {
    id: 'staff_002',
    realName: '保安队长',
    phone: '188****2222',
    position: '保安',
    organization: '未来城小区',
    passwordStatus: 'set',
    faceStatus: 'recorded',
    nfcCards: [
      {
        cardNumber: 'STAFF_002',
        cardType: '员工卡',
        recordTime: '2024-12-01 10:00:00',
      },
    ],
    accessScope: ['未来城小区', '1号楼', '2号楼'],
    addTime: '2024-12-01 10:00:00',
    accessibleDevices: ['device_001', 'device_002', 'device_004'],
  },
  {
    id: 'staff_003',
    realName: '维修工程师',
    phone: '188****3333',
    avatar: 'https://via.placeholder.com/50',
    position: '维修工',
    organization: '未来城小区',
    passwordStatus: 'unset',
    faceStatus: 'unrecorded',
    nfcCards: [],
    accessScope: ['1号楼', '2号楼'],
    addTime: '2024-12-02 14:30:00',
    accessibleDevices: ['device_001', 'device_002'],
  },
  {
    id: 'staff_004',
    realName: '清洁员',
    phone: '188****4444',
    position: '清洁工',
    organization: '未来城小区',
    passwordStatus: 'set',
    faceStatus: 'recorded',
    nfcCards: [
      {
        cardNumber: 'STAFF_004',
        cardType: '员工卡',
        recordTime: '2024-12-03 08:00:00',
      },
    ],
    accessScope: ['1号楼1单元', '1号楼2单元'],
    addTime: '2024-12-03 08:00:00',
    accessibleDevices: ['device_001', 'device_002'],
  },
];

// 邻里互助Mock数据
export const mockMutualAidPosts = [
  {
    id: 'post_001',
    title: '寻找丢失的小猫',
    content: '我家的橘猫昨天晚上跑丢了，名字叫小橘，很亲人，如果有人看到请联系我，谢谢！',
    tag: 'seeking' as const,
    publisher: {
      id: 'resident_001',
      realName: '张三',
      avatar: 'https://via.placeholder.com/50',
      room: '1号楼1单元101室',
      phone: '138****1234',
    },
    images: ['https://via.placeholder.com/300x200', 'https://via.placeholder.com/300x200'],
    videos: [],
    publishTime: '2024-12-18 14:30:00',
    status: 'normal' as const,
    commentCount: 3,
    location: {
      community: '未来城小区',
      building: '1号楼',
      unit: '1单元',
      floor: '1楼',
      room: '101室',
    },
  },
  {
    id: 'post_002',
    title: '捡到一串钥匙',
    content: '在小区门口捡到一串钥匙，有车钥匙和门钥匙，失主请联系我认领。',
    tag: 'lost-found' as const,
    publisher: {
      id: 'resident_002',
      realName: '李四',
      room: '1号楼1单元102室',
      phone: '139****5678',
    },
    images: ['https://via.placeholder.com/300x200'],
    videos: [],
    publishTime: '2024-12-17 09:15:00',
    status: 'normal' as const,
    commentCount: 1,
    location: {
      community: '未来城小区',
      building: '1号楼',
      unit: '1单元',
      floor: '1楼',
      room: '102室',
    },
  },
  {
    id: 'post_003',
    title: '转让闲置儿童自行车',
    content: '孩子长大了，闲置一辆儿童自行车，9成新，有需要的邻居可以联系我。',
    tag: 'idle-items' as const,
    publisher: {
      id: 'resident_003',
      realName: '王五',
      avatar: 'https://via.placeholder.com/50',
      room: '1号楼1单元201室',
      phone: '136****9012',
    },
    images: ['https://via.placeholder.com/300x200', 'https://via.placeholder.com/300x200'],
    videos: [],
    publishTime: '2024-12-16 16:20:00',
    status: 'normal' as const,
    commentCount: 5,
    location: {
      community: '未来城小区',
      building: '1号楼',
      unit: '1单元',
      floor: '2楼',
      room: '201室',
    },
  },
  {
    id: 'post_004',
    title: '不当言论已删除',
    content: '该帖子因包含不当言论已被删除。',
    tag: 'seeking' as const,
    publisher: {
      id: 'resident_004',
      realName: '赵六',
      room: '1号楼2单元101室',
      phone: '137****3456',
    },
    images: [],
    videos: [],
    publishTime: '2024-12-15 11:45:00',
    status: 'deleted' as const,
    deleteReason: '包含不当言论',
    deleteTime: '2024-12-15 12:00:00',
    commentCount: 0,
    location: {
      community: '未来城小区',
      building: '1号楼',
      unit: '2单元',
      floor: '1楼',
      room: '101室',
    },
  },
];

// 评论Mock数据
export const mockComments = [
  {
    id: 'comment_001',
    postId: 'post_001',
    content: '我昨天在小区里看到一只橘猫，可能是你家的，在2号楼附近。',
    commenter: {
      id: 'resident_002',
      realName: '李四',
      nickname: '热心邻居',
    },
    commentTime: '2024-12-18 15:30:00',
    status: 'normal' as const,
  },
  {
    id: 'comment_002',
    postId: 'post_001',
    content: '我也看到了，应该就是你家的小猫，很可爱！',
    commenter: {
      id: 'resident_003',
      realName: '王五',
      avatar: 'https://via.placeholder.com/50',
    },
    commentTime: '2024-12-18 16:45:00',
    status: 'normal' as const,
  },
  {
    id: 'comment_003',
    postId: 'post_001',
    content: '希望能早点找到小猫！',
    commenter: {
      id: 'resident_005',
      realName: '孙七',
      avatar: 'https://via.placeholder.com/50',
    },
    commentTime: '2024-12-18 17:20:00',
    status: 'normal' as const,
  },
  {
    id: 'comment_004',
    postId: 'post_002',
    content: '谢谢你的拾金不昧！',
    commenter: {
      id: 'resident_001',
      realName: '张三',
      avatar: 'https://via.placeholder.com/50',
    },
    commentTime: '2024-12-17 10:30:00',
    status: 'normal' as const,
  },
  {
    id: 'comment_005',
    postId: 'post_003',
    content: '自行车还在吗？我想要。',
    commenter: {
      id: 'resident_004',
      realName: '赵六',
    },
    commentTime: '2024-12-16 17:00:00',
    status: 'normal' as const,
  },
  {
    id: 'comment_006',
    postId: 'post_003',
    content: '多少钱？',
    commenter: {
      id: 'resident_002',
      realName: '李四',
    },
    commentTime: '2024-12-16 17:30:00',
    status: 'normal' as const,
  },
  {
    id: 'comment_007',
    postId: 'post_003',
    content: '不当评论已删除',
    commenter: {
      id: 'resident_001',
      realName: '张三',
    },
    commentTime: '2024-12-16 18:00:00',
    status: 'deleted' as const,
    deleteReason: '包含不当言论',
    deleteTime: '2024-12-16 18:15:00',
  },
];

// 举报类型Mock数据
export const mockReportTypes = [
  { id: 'spam', name: '垃圾信息' },
  { id: 'inappropriate', name: '不当言论' },
  { id: 'harassment', name: '骚扰他人' },
  { id: 'fraud', name: '虚假信息' },
  { id: 'violence', name: '暴力威胁' },
  { id: 'other', name: '其他' },
];

// 举报Mock数据
export const mockReports = [
  {
    id: 'report_001',
    type: '不当言论',
    reporter: {
      id: 'resident_001',
      realName: '张三',
      phone: '138****1234',
      avatar: 'https://via.placeholder.com/50',
    },
    reported: {
      id: 'resident_004',
      realName: '赵六',
      phone: '137****3456',
    },
    description: '该用户在邻里互助中发布了包含不当言论的内容，影响社区和谐。',
    contentSummary: '在寻人寻物帖子中使用了不文明用语',
    contentLink: '/community/mutual-aid/post_004',
    evidenceImages: ['https://via.placeholder.com/300x200'],
    evidenceVideos: [],
    reportTime: '2024-12-15 11:50:00',
    status: 'completed' as const,
    processHistory: [
      {
        id: 'process_001',
        type: 'reply' as const,
        content: '感谢您的举报，我们已经收到并开始处理。',
        processor: {
          id: 'staff_001',
          realName: '物业管理员',
        },
        processTime: '2024-12-15 12:00:00',
      },
      {
        id: 'process_002',
        type: 'warning' as const,
        content: '您发布的内容包含不当言论，请注意文明用语。',
        processor: {
          id: 'staff_001',
          realName: '物业管理员',
        },
        processTime: '2024-12-15 12:05:00',
      },
      {
        id: 'process_003',
        type: 'delete-content' as const,
        content: '已删除相关不当内容。',
        processor: {
          id: 'staff_001',
          realName: '物业管理员',
        },
        processTime: '2024-12-15 12:10:00',
      },
    ],
  },
  {
    id: 'report_002',
    type: '骚扰他人',
    reporter: {
      id: 'resident_002',
      realName: '李四',
      phone: '139****5678',
    },
    reported: {
      id: 'resident_005',
      realName: '孙七',
      phone: '135****7890',
      avatar: 'https://via.placeholder.com/50',
    },
    description: '该用户多次在评论中骚扰其他住户，请处理。',
    contentSummary: '在多个帖子下发布骚扰性评论',
    evidenceImages: ['https://via.placeholder.com/300x200', 'https://via.placeholder.com/300x200'],
    evidenceVideos: [],
    reportTime: '2024-12-17 14:20:00',
    status: 'processing' as const,
    processHistory: [
      {
        id: 'process_004',
        type: 'reply' as const,
        content: '我们已经收到您的举报，正在调查处理中。',
        processor: {
          id: 'staff_001',
          realName: '物业管理员',
        },
        processTime: '2024-12-17 15:00:00',
      },
    ],
  },
  {
    id: 'report_003',
    type: '虚假信息',
    reporter: {
      id: 'resident_003',
      realName: '王五',
      phone: '136****9012',
      avatar: 'https://via.placeholder.com/50',
    },
    reported: {
      id: 'resident_001',
      realName: '张三',
      phone: '138****1234',
      avatar: 'https://via.placeholder.com/50',
    },
    description: '该用户发布的失物招领信息是虚假的，请核实。',
    contentSummary: '发布虚假的失物招领信息',
    contentLink: '/community/mutual-aid/post_002',
    evidenceImages: [],
    evidenceVideos: [],
    reportTime: '2024-12-18 10:30:00',
    status: 'pending' as const,
    processHistory: [],
  },
];
