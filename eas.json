{"cli": {"version": ">= 12.0.0"}, "build": {"development": {"developmentClient": true, "distribution": "internal", "channel": "development", "env": {"NODE_ENV": "development"}, "ios": {"resourceClass": "m-medium", "simulator": true, "buildConfiguration": "Debug"}, "android": {"resourceClass": "medium", "buildType": "apk", "gradleCommand": ":app:assembleDebug"}}, "staging": {"distribution": "internal", "channel": "staging", "env": {"NODE_ENV": "staging"}, "ios": {"resourceClass": "m-medium", "buildConfiguration": "Release"}, "android": {"resourceClass": "medium", "buildType": "apk", "gradleCommand": ":app:assembleRelease"}}, "preview": {"distribution": "internal", "channel": "preview", "env": {"NODE_ENV": "production"}, "ios": {"resourceClass": "m-medium", "buildConfiguration": "Release"}, "android": {"resourceClass": "medium", "buildType": "apk", "gradleCommand": ":app:assembleRelease"}}, "production": {"channel": "production", "env": {"NODE_ENV": "production"}, "ios": {"resourceClass": "m-medium", "buildConfiguration": "Release"}, "android": {"resourceClass": "medium", "buildType": "app-bundle", "gradleCommand": ":app:bundleRelease"}}}, "submit": {"production": {"ios": {"appleId": "<EMAIL>", "ascAppId": "**********", "appleTeamId": "XXXXXXXXXX"}, "android": {"serviceAccountKeyPath": "./android-service-account.json", "track": "production"}}, "staging": {"ios": {"appleId": "<EMAIL>", "ascAppId": "**********", "appleTeamId": "XXXXXXXXXX"}, "android": {"serviceAccountKeyPath": "./android-service-account.json", "track": "internal"}}}}