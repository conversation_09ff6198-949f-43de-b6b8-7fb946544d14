{"expo": {"name": "攸家App", "slug": "youjia-app", "version": "2.0.0", "description": "攸家智能门禁管理系统移动端应用", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "<PERSON><PERSON>a", "userInterfaceStyle": "automatic", "newArchEnabled": false, "privacy": "unlisted", "ios": {"supportsTablet": true, "bundleIdentifier": "com.youjia.app", "buildNumber": "1", "infoPlist": {"NSCameraUsageDescription": "此应用需要使用相机来扫描设备二维码和录入人脸信息", "NSPhotoLibraryUsageDescription": "此应用需要访问相册来选择头像图片", "NSMicrophoneUsageDescription": "此应用需要使用麦克风进行语音通话功能", "NSLocationWhenInUseUsageDescription": "此应用需要获取位置信息来定位设备安装位置"}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "com.youjia.app", "versionCode": 1, "permissions": ["android.permission.CAMERA", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE", "android.permission.ACCESS_FINE_LOCATION", "android.permission.ACCESS_COARSE_LOCATION", "android.permission.RECORD_AUDIO", "android.permission.VIBRATE", "android.permission.INTERNET", "android.permission.ACCESS_NETWORK_STATE"]}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png", "build": {"babel": {"include": ["@babel/plugin-syntax-import-meta", "@babel/plugin-syntax-dynamic-import"]}}}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], ["expo-camera", {"cameraPermission": "此应用需要使用相机来扫描设备二维码和录入人脸信息"}], ["expo-image-picker", {"photosPermission": "此应用需要访问相册来选择头像图片"}], "expo-secure-store", "expo-web-browser"], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "00afd8c0-e6aa-489f-9d41-c865320db6d5"}}}}