const { getDefaultConfig } = require('expo/metro-config');

const config = getDefaultConfig(__dirname);

// 添加路径别名支持
config.resolver.alias = {
  '@': './src',
};

// 优化打包性能设置
config.resolver.platforms = ['ios', 'android', 'native', 'web'];

// 配置资源解析规则
config.resolver.assetExts.push(
  // 图片格式
  'svg',
  'png',
  'jpg',
  'jpeg',
  'gif',
  'webp',
  // 字体格式
  'ttf',
  'otf',
  'woff',
  'woff2',
  // 其他资源
  'json',
  'mp4',
  'mp3'
);

// 修复Node.js v22兼容性问题 - 禁用有问题的缓存
config.cacheStores = [];

// 优化 transformer 设置 - 修复Web端模块系统问题
config.transformer = {
  ...config.transformer,
  unstable_allowRequireContext: true,
  assetPlugins: ['expo-asset/tools/hashAssetFiles'],
  // 修复Web端import.meta问题
  getTransformOptions: async () => ({
    transform: {
      experimentalImportSupport: false,
      inlineRequires: true,
    },
  }),
};

// Web端模块解析配置 - 修复import.meta模块系统错误
config.resolver = {
  ...config.resolver,
  alias: {
    '@': './src',
  },
  platforms: ['ios', 'android', 'native', 'web'],
  unstable_enablePackageExports: false, // 禁用包导出以避免模块系统冲突
};

// Web端特定配置 - 确保正确的模块系统
if (process.env.EXPO_PLATFORM === 'web') {
  config.transformer.minifierConfig = {
    mangle: {
      keep_fnames: true,
    },
    output: {
      ascii_only: true,
      quote_keys: true,
      wrap_iife: true,
    },
  };
}

module.exports = config;
